package cz.kpsys.portaro.acme;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.SystemInstitution;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.function.Consumer;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AcmeService {

    @NonNull AcmeTemplate acmeTemplate;
    @NonNull Provider<Department> realRootDepartmentProvider;
    @NonNull List<@NonNull String> managedServerUrls;
    @NonNull ContextualProvider<Department, @NonNull SystemInstitution> systemInstitutionProvider;
    @NonNull Consumer<AcmeChallenge> acmeChallengeKeySetter;
    @NonNull Saver<CertificateChainAndPrivateKeySaveRequest, ?> certAndKeySaver;
    @NonFinal Runnable afterRenewCallback = () -> {};


    public AcmeService withAfterRenew(Runnable afterRenewCallback) {
        this.afterRenewCallback = afterRenewCallback;
        return this;
    }


    public void renew() {
        CertificateChainAndPrivateKey certKeyPair = fetchCertificateChainAndPrivateKey();
        certAndKeySaver.save(new CertificateChainAndPrivateKeySaveRequest(certKeyPair, realRootDepartmentProvider.get()));
        afterRenewCallback.run();
    }


    private static String getDomainFromServerUrl(String serverUrl) {
        return UriComponentsBuilder.fromUriString(serverUrl)
                .build()
                .getHost();
    }


    private CertificateChainAndPrivateKey fetchCertificateChainAndPrivateKey() {
        List<String> domains = managedServerUrls.stream()
                .map(AcmeService::getDomainFromServerUrl)
                .distinct()
                .toList();

        SystemInstitution systemInstitution = systemInstitutionProvider.getOn(realRootDepartmentProvider.get());

        CertificateRequest request = new CertificateRequest(domains, systemInstitution.notBlankName());
        return acmeTemplate.orderNewCertificate(request, acmeChallengeKeySetter);
    }
}

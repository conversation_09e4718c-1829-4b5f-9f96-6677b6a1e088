package cz.kpsys.portaro.acme;

import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.setting.CustomSetting;
import cz.kpsys.portaro.setting.CustomSettingId;
import cz.kpsys.portaro.setting.CustomSettingLoader;
import cz.kpsys.portaro.setting.SettingTypeId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Consumer;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class IniSavingChallengeKeySetter implements Consumer<AcmeChallenge> {

    @NonNull Saver<CustomSetting<String>, ?> customSettingSaver;
    @NonNull SettingTypeId settingTypeId;
    @NonNull Integer departmentId;
    @NonNull CustomSettingLoader customSettingLoader;

    @Override
    public void accept(AcmeChallenge acmeChallenge) {
        CustomSettingId customSettingId = new CustomSettingId(settingTypeId, departmentId, null);
        var loadedCustomSetting = customSettingLoader.getByCompleteIdOrCreateEmpty(customSettingId);
        customSettingSaver.save(new CustomSetting<>(customSettingId, acmeChallenge.getKey(), null, loadedCustomSetting.getUuid()));

        log.debug("ACME challenge key {} saved to ini {} to be accessible at {}", acmeChallenge.getKey(), settingTypeId, acmeChallenge.getUrl());
    }
}

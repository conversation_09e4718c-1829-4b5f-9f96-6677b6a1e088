package cz.kpsys.portaro.auth.bankid;

import cz.kpsys.portaro.auth.UserIsNotEligibleForAuthenticationException;
import cz.kpsys.portaro.auth.bankid.reponse.BankIDUserInfoResponse;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.DateUtils;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class BankIDAcceptableAuthenticationValidator {

    @NonNull ContextualProvider<Department, Integer> minimalRegistrationAgeProvider;

    /**
     * Throws exception if user is younger than minimal age or birthDate is not present.
     */
    public void validOrThrow(Department ctx, @NonNull BankIDUserInfoResponse token) {
        if (!isValid(ctx, token)) {
            throw new UserIsNotEligibleForAuthenticationException(
                    "Users age do not allow authentication.",
                    Texts.ofMessageCodedOrNative("login.bankid.AgeLimitException"));
        }
    }

    private boolean isValid(Department ctx, @NonNull BankIDUserInfoResponse token) {
        if (token.birthDate() != null) {
            int age = DateUtils.ageFrom(token.birthDate());
            Integer minimalAge = minimalRegistrationAgeProvider.getOn(ctx);
            if (minimalAge == null) {
                return true;
            }
            return minimalAge <= age;
        }

        return false;
    }
}

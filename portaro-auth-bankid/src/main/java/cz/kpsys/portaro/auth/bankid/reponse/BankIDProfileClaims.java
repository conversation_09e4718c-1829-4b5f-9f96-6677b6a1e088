package cz.kpsys.portaro.auth.bankid.reponse;

import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.validation.nullablenotempty.NullableNotEmpty;
import cz.kpsys.portaro.user.Gender;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

public record BankIDProfileClaims(

        @JsonProperty("title_prefix")
        @NullableNotBlank
        String titlePrefix,

        @JsonProperty("title_suffix")
        @NullableNotBlank
        String titleSuffix,

        @JsonProperty("given_name")
        @NullableNotBlank
        String givenName,

        @JsonProperty("middle_name")
        @NullableNotBlank
        String middleName,

        @JsonProperty("family_name")
        @NullableNotBlank
        String familyName,

        @Nullable
        Gender gender,

        @Nullable
        LocalDate birthdate,

        @NullableNotBlank
        String birthnumber,

        @Nullable
        Integer age,

        @Nullable
        Boolean majority,

        @JsonProperty("date_of_death")
        @NullableNotBlank
        String deathDate,

        @NullableNotBlank
        String birthplace,

        @JsonProperty("birthcountry")
        @NullableNotBlank
        String birthCountry,

        @NullableNotEmpty
        List<String> nationalities,

        @JsonProperty("maritalstatus")
        @Nullable
        BankIDProfileMaritalStatusType maritalStatus,

        @NullableNotEmpty
        List<BankIDProfileAddressResponse> addresses,

        @NullableNotEmpty
        List<BankIDIdcard> idcards,

        @NullableNotBlank
        String email,

        @JsonProperty("phone_number")
        @NullableNotBlank
        String phoneNumber,

        @Nullable
        Boolean pep,

        @JsonProperty("limited_legal_capacity")
        @Nullable
        Boolean limitedLegalCapacity,

        @JsonProperty("paymentAccounts")
        @NullableNotEmpty
        List<String> paymentAccounts,

        @JsonProperty("paymentAccountsDetails")
        @NullableNotEmpty
        List<BankIDPaymentAccountDetails> paymentAccountsDetails,

        @JsonProperty("updated_at")
        @Nullable
        Instant updatedAt
) {
}

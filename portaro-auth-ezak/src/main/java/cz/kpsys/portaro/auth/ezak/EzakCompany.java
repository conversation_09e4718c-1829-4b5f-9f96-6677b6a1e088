package cz.kpsys.portaro.auth.ezak;

import java.util.Objects;

/**
 * Created by janpa on 22.08.2016.
 */
public class EzakCompany {

    private String id;
    private String name;
    private String ico;
    private String address;
    private String country;
    private String role;

    public EzakCompany(String id, String name, String ico, String address, String country, String role) {
        this.id = id;
        this.name = Objects.requireNonNull(name, "Ezak company does not have name");
        this.ico = Objects.requireNonNull(ico, "Ezak company does not have IČO");
        this.address = address;
        this.country = country;
        this.role = role;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getIco() {
        return ico;
    }

    public String getAddress() {
        return address;
    }

    public String getCountry() {
        return country;
    }

    public String getRole() {
        return role;
    }
}

package cz.kpsys.portaro.auth.internal.credentials;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.UserHasNotAPasswordException;
import cz.kpsys.portaro.auth.internal.InternalAuthSecurityActions;
import cz.kpsys.portaro.auth.password.PasswordChecker;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.validation.Validity;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.remotevalidation.DefaultModelRemoteValidationRequest;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.localization.LocalizationCodes;
import cz.kpsys.portaro.mail.*;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.template.TemplateEngine;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserConstants;
import cz.kpsys.portaro.user.UserLoader;
import cz.kpsys.portaro.user.registration.PersonLoginIdentifierValueProvider;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static cz.kpsys.portaro.user.UserApiConstants.USERS_URL_PATH;

@RequestMapping(USERS_URL_PATH)
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class UserCredentialsApiController extends GenericApiController {

    @NonNull UserLoader userLoader;
    @NonNull PasswordChanger passwordChanger;
    @NonNull SecurityManager securityManager;
    @NonNull MailService mailService;
    @NonNull TemplateEngine templateEngine;
    @NonNull PersonLoginIdentifierValueProvider personLoginIdentifierValueProvider;
    @NonNull UserCredentialsDeleter userCredentialsDeleter;
    @NonNull PasswordRecreationLinkService passwordRecreationLinkService;
    @NonNull PasswordChecker passwordChecker;


    @PostMapping("password-change")
    public ActionResponse passwordChange(@RequestBody @ValidFormObject PasswordChangeRequest request,
                                         UserAuthentication currentAuth,
                                         @CurrentDepartment Department ctx) {
        securityManager.throwIfCannot(InternalAuthSecurityActions.USER_PASSWORD_CHANGE, currentAuth, ctx, request.user());
        passwordChanger.changePassword(ctx, request.user(), currentAuth, request.oldPassword(), request.newPassword());
        return new FinishedActionResponse(Texts.ofMessageCoded("ctenar.HesloByloUspesneZmeneno"));
    }


    @PostMapping("password-change-validator")
    public Validity validate(@RequestBody @ValidFormObject DefaultModelRemoteValidationRequest<String, PasswordChangeRequest> validationRequest) {
        String fieldName = validationRequest.fieldName();

        if (fieldName.equals(PasswordChangeRequest.Fields.oldPassword)) {
            if (validationRequest.value() == null) {
                return Validity.ofEmptyAsValid(fieldName);
            }
            return new Validity(PasswordChangeRequest.Fields.oldPassword, Validity.TYPE_PASSWORD_MATCH, passwordChecker.check(validationRequest.value(), validationRequest.formModel().user()), Texts.ofMessageCoded("login.WrongOldPassword"));
        }

        return Validity.ofEmptyAsValid(fieldName);
    }


    @PostMapping("tokened-password-change-mail")
    public ActionResponse tokenedPasswordChange(@RequestBody @ValidFormObject TokenedPasswordChangeMailRequest request,
                                                @CurrentDepartment Department ctx,
                                                UserAuthentication currentAuth,
                                                Locale locale) {
        User user = userLoader.getUser(request.user());

        if (user.getPasswordHash() == null) {
            throw new UserHasNotAPasswordException("User wants to reset password but has not set login credentials");
        }

        Map<String, Object> tm = new HashMap<>();
        tm.put("loginPropertyValue", personLoginIdentifierValueProvider.getFirstLoginWayIdentifierValue(ctx, user).orElse(null));
        tm.put("changePasswordUrl", passwordRecreationLinkService.createLink(ctx, user));
        String body = templateEngine.build(UserConstants.Template.TEMPLATE_USER_PASSWORD_RESET_MAIL, currentAuth, ctx, tm, locale);

        mailService.sendRawBodyMail(new NonspecificMailSendCommand(
                RawBodyMailTopics.USER_COMMON,
                ctx,
                From.system(),
                To.user(user),
                Texts.ofMessageCoded("login.resetPassword.mail.subject"),
                body,
                List.of(),
                currentAuth
        ));

        return new FinishedActionResponse(Texts.ofMessageCoded(LocalizationCodes.MailWasSuccessfullySent));
    }


    @GetMapping("{id}/credentials-delete")
    public ActionResponse credentialsDeletion(@PathVariable("id") User user,
                                              @CurrentDepartment Department ctx,
                                              UserAuthentication currentAuth) {
        securityManager.throwIfCannot(InternalAuthSecurityActions.USER_CREDENTIALS_DELETE, currentAuth, ctx, user);
        userCredentialsDeleter.deleteCredentials(user);
        return new FinishedActionResponse(Texts.ofMessageCoded(LocalizationCodes.Deleted));
    }

}

package cz.kpsys.portaro.auth.oauth2.jwt;

import cz.kpsys.portaro.auth.MoreThanOneUserWithGivenIdentifierException;
import cz.kpsys.portaro.auth.external.ExternallyAuthenticatedUserIsNotInLocalException;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.MoreThanOneItemFoundException;
import cz.kpsys.portaro.commons.property.Property;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.StaticParamsModifier;
import cz.kpsys.portaro.token.ClaimField;
import cz.kpsys.portaro.token.Claims;
import cz.kpsys.portaro.token.Oauth2JwtClaimFields;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class AttributeValueSearchingJwtUserResolver implements Function<Claims, User> {

    @NonNull ParameterizedSearchLoader<MapBackedParams, User> userSearchLoader;
    @NonNull ByIdLoadable<User, Integer> userLoader;
    @NonNull ClaimField<String> identifyingOauth2ClaimField;
    @NonNull Property<List<String>> identifyingProperty;

    @Override
    public User apply(@NonNull Claims claims) {
        Optional<Long> userId = claims.findClaim(Oauth2JwtClaimFields.USER_ID);
        if (userId.isPresent()) {
            return userLoader.getById(userId.get().intValue());
        }

        Optional<String> claimValue = claims.findClaim(identifyingOauth2ClaimField);
        if (claimValue.isPresent()) {
            try {
                return userSearchLoader.getMaxOne(StaticParamsModifier.of(identifyingProperty, List.of(claimValue.get())))
                        .orElseThrow(() -> new ExternallyAuthenticatedUserIsNotInLocalException(claimValue.get(), false));
            } catch (MoreThanOneItemFoundException e) {
                throw new MoreThanOneUserWithGivenIdentifierException(claimValue.get(), e);
            }
        }

        throw new IllegalStateException("No claim %s in JWT".formatted(identifyingOauth2ClaimField.name()));
    }
}

package cz.kpsys.portaro.auth.oauth2.jwt;

import cz.kpsys.portaro.token.ClaimField;
import cz.kpsys.portaro.token.Claims;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.security.oauth2.jwt.Jwt;

import java.time.Instant;
import java.util.Objects;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringJwtClaims implements Claims {

    @NonNull Jwt springJwt;
    @NonNull ClaimObjectToValueConverter converter;

    @NonNull
    @Override
    public Instant getExpirationDate() {
        return Objects.requireNonNull(springJwt.getExpiresAt(), "JWT does not contain 'exp' field");
    }

    @Override
    public <E> Optional<E> findClaim(@NonNull ClaimField<E> field) {
        Object claim = springJwt.getClaim(field.name());
        return Optional.ofNullable(converter.convert(claim, field));
    }
}

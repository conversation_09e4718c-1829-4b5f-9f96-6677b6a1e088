package cz.kpsys.portaro.auth.mojeid;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.setting.SettingKey;
import lombok.NonNull;

import java.util.List;

public class MojeIDAuthorizationCodeAuthSettingKeys {

    public static final String SECTION_AUTH_MOJEID = "auth.mojeid";

    public static final SettingKey<@NonNull Boolean> MOJEID_ENABLED = new SettingKey<>(SECTION_AUTH_MOJEID, "enabled");
    public static final SettingKey<@NonNull Boolean> MOJEID_SANDBOX_ENABLED = new SettingKey<>(SECTION_AUTH_MOJEID, "sandboxEnabled");
    public static final SettingKey<@NullableNotBlank String> MOJEID_CLIENT_ID = new SettingKey<>(SECTION_AUTH_MOJEID, "clientId");
    public static final SettingKey<@NullableNotBlank String> MOJEID_CLIENT_SECRET = new SettingKey<>(SECTION_AUTH_MOJEID, "clientSecret");
    public static final SettingKey<@NonNull List<@NonNull String>> MOJEID_REQUIRED_PROPS = new SettingKey<>(SECTION_AUTH_MOJEID, "requiredProperties");
    public static final SettingKey<List<String>> MOJEID_TRUSTED_PROPS = new SettingKey<>(SECTION_AUTH_MOJEID, "trustedProperties");
    public static final SettingKey<@NonNull List<@NonNull String>> MOJEID_CLAIMS = new SettingKey<>(SECTION_AUTH_MOJEID, "claims");
    public static final SettingKey<Integer> MOJEID_DEPARTMENT_FOR_REDIRECT_URL = new SettingKey<>(SECTION_AUTH_MOJEID, "departmentForRedirectUrl");
}
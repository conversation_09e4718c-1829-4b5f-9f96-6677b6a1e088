package cz.kpsys.portaro.auth.mojeid;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.RequestEntity;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrations;
import org.springframework.util.Assert;
import org.springframework.web.client.RestOperations;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Collections;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MojeIDClientRegistrationBuilderProvider implements ContextualProvider<Department, ClientRegistration.Builder> {

    private static final String OIDC_METADATA_PATH = "/.well-known/openid-configuration";

    public static final String MOJEID_SANDBOX_URL = "https://mojeid.regtest.nic.cz/";
    public static final String MOJEID_PRODUCTION_URL = "https://mojeid.cz/";
    @NonNull ContextualProvider<Department, @NonNull Boolean> mojeIDEnabled;
    @NonNull ContextualProvider<Department, @NonNull Boolean> mojeIDSandboxEnabled;
    @NonNull RestOperations rest;

    @Override
    public ClientRegistration.Builder getOn(Department ctx) {
        return mojeIDEnabled.getOn(ctx) ? mojeIDSandboxEnabled.getOn(ctx) ? getBankIDSandbox() : getBankIDProduction() : getBankIDSandbox();
    }

    private @NonNull ClientRegistration.Builder getBankIDProduction() {
        return getClientRegistrationForMojeID(MOJEID_PRODUCTION_URL);
    }

    private @NonNull ClientRegistration.Builder getBankIDSandbox() {
        return getClientRegistrationForMojeID(MOJEID_SANDBOX_URL);
    }

    private ClientRegistration.@NonNull Builder getClientRegistrationForMojeID(String issuer) {
        URI issuerUri = URI.create(issuer);
        URI uri = UriComponentsBuilder.fromUri(issuerUri)
                .replacePath(issuerUri.getPath() + OIDC_METADATA_PATH)
                .build(Collections.emptyMap());
        RequestEntity<Void> request = RequestEntity.get(uri).build();
        ParameterizedTypeReference<Map<String, Object>> typeReference = new ParameterizedTypeReference<>() {};
        Map<String, Object> configuration = rest.exchange(request, typeReference).getBody();
        Assert.state(configuration != null, "MojeID configuration cannot be null.");

        // DO NOT REMOVE !!!
        // This is done because MojeID configuration return field acr_values_supported with one item in array that is empty BUT OIDC configuration parse that is used in Spring support only empty values in this field.
        // MojeID support said that the empty value in this field is correct so that is why there needs to be this hack. Application don't use acr values so there is no harm in removing this field.
        configuration.remove("acr_values_supported");

        return ClientRegistrations.fromOidcConfiguration(configuration);
    }
}
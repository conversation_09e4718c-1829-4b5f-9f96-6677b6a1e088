package cz.kpsys.portaro.auth.multifactor;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.licence.FeatureNotEnabledException;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CompositeAuthenticationUpgradeDialogResolver implements AuthenticationUpgradeDialogResolver {

    @NonNull List<ConditionalResolver> resolvers = new ArrayList<>();

    public CompositeAuthenticationUpgradeDialogResolver register(@NonNull ContextualProvider<Department, @NonNull Boolean> enabledProvider, @NonNull AuthenticationUpgradeDialogResolver resolver) {
        resolvers.add(new ConditionalResolver(enabledProvider, resolver));
        return this;
    }

    @Override
    public ActionResponse resolve(UserAuthentication currentAuth, Department currentDepartment) {
        return resolvers.stream()
                .filter(conditionalResolver -> conditionalResolver.isEnabledOn(currentDepartment))
                .findFirst()
                .orElseThrow(() -> new FeatureNotEnabledException("MFA", Texts.ofNative("Žádná vícefaktorová autentizace není v systému zapnutá")))
                .resolve(currentAuth, currentDepartment);
    }

}

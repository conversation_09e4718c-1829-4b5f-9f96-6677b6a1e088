package cz.kpsys.portaro.auth.saml2.sp;

import lombok.NonNull;
import lombok.Value;

@Value
public class Saml2ServiceProviderProperties {

    private static final String SSO_CIRCLE_IDP_VERIFICATION_CERTIFICATE = """
            -----B<PERSON><PERSON> CERTIFICATE-----
            MIIEYzCCAkugAwIBAgIDIAZmMA0GCSqGSIb3DQEBCwUAMC4xCzAJBgNVBAYTAkRF
            MRIwEAYDVQQKDAlTU09DaXJjbGUxCzAJBgNVBAMMAkNBMB4XDTE2MDgwMzE1MDMy
            M1oXDTI2MDMwNDE1MDMyM1owPTELMAkGA1UEBhMCREUxEjAQBgNVBAoTCVNTT0Np
            cmNsZTEaMBgGA1UEAxMRaWRwLnNzb2NpcmNsZS5jb20wggEiMA0GCSqGSIb3DQEB
            AQUAA4IBDwAwggEKAoIBAQCAwWJyOYhYmWZF2TJvm1VyZccs3ZJ0TsNcoazr2pTW
            cY8WTRbIV9d06zYjngvWibyiylewGXcYONB106ZNUdNgrmFd5194Wsyx6bPvnjZE
            ERny9LOfuwQaqDYeKhI6c+veXApnOfsY26u9Lqb9sga9JnCkUGRaoVrAVM3yfghv
            /Cg/QEg+I6SVES75tKdcLDTt/FwmAYDEBV8l52bcMDNF+JWtAuetI9/dWCBe9VTC
            asAr2Fxw1ZYTAiqGI9sW4kWS2ApedbqsgH3qqMlPA7tg9iKy8Yw/deEn0qQIx8Gl
            VnQFpDgzG9k+jwBoebAYfGvMcO/BDXD2pbWTN+DvbURlAgMBAAGjezB5MAkGA1Ud
            EwQCMAAwLAYJYIZIAYb4QgENBB8WHU9wZW5TU0wgR2VuZXJhdGVkIENlcnRpZmlj
            YXRlMB0GA1UdDgQWBBQhAmCewE7aonAvyJfjImCRZDtccTAfBgNVHSMEGDAWgBTA
            1nEA+0za6ppLItkOX5yEp8cQaTANBgkqhkiG9w0BAQsFAAOCAgEAAhC5/WsF9ztJ
            Hgo+x9KV9bqVS0MmsgpG26yOAqFYwOSPmUuYmJmHgmKGjKrj1fdCINtzcBHFFBC1
            maGJ33lMk2bM2THx22/O93f4RFnFab7t23jRFcF0amQUOsDvltfJw7XCal8JdgPU
            g6TNC4Fy9XYv0OAHc3oDp3vl1Yj8/1qBg6Rc39kehmD5v8SKYmpE7yFKxDF1ol9D
            KDG/LvClSvnuVP0b4BWdBAA9aJSFtdNGgEvpEUqGkJ1osLVqCMvSYsUtHmapaX3h
            iM9RbX38jsSgsl44Rar5Ioc7KXOOZFGfEKyyUqucYpjWCOXJELAVAzp7XTvA2q55
            u31hO0w8Yx4uEQKlmxDuZmxpMz4EWARyjHSAuDKEW1RJvUr6+5uA9qeOKxLiKN1j
            o6eWAcl6Wr9MreXR9kFpS6kHllfdVSrJES4ST0uh1Jp4EYgmiyMmFCbUpKXifpsN
            WCLDenE3hllF0+q3wIdu+4P82RIM71n7qVgnDnK29wnLhHDat9rkC62CIbonpkVY
            mnReX0jze+7twRanJOMCJ+lFg16BDvBcG8u0n/wIDkHHitBI7bU1k6c6DydLQ+69
            h8SCo6sO9YuD+/3xAGKad4ImZ6vTwlB4zDCpu6YgQWocWRXE+VkOb+RBfvP755PU
            aLfL63AFVlpOnEpIio5++UjNJRuPuAA=
            -----END CERTIFICATE-----""";

    private static final String TUL_IDP_VERIFICATION_CERTIFICATE = """
            -----BEGIN CERTIFICATE-----
            MIID6jCCAtKgAwIBAgIBCjANBgkqhkiG9w0BAQsFADCBmjELMAkGA1UEBhMCQ1ox
            EDAOBgNVBAcTB0xpYmVyZWMxKDAmBgNVBAoTH1RlY2huaWNhbCBVbml2ZXJzaXR5
            IG9mIExpYmVyZWMxDjAMBgNVBAsTBUxpYW5lMSIwIAYDVQQDExlUVUwgQ2VydGlm
            aWNhdGUgQXV0aG9yaXR5MRswGQYJKoZIhvcNAQkBFgxsaWFuZUB0dWwuY3owHhcN
            MTUxMDI2MDAwMDAwWhcNMzAxMDI1MjM1OTU5WjBxMQswCQYDVQQGEwJDWjEQMA4G
            A1UEBxMHTGliZXJlYzEoMCYGA1UEChMfVGVjaG5pY2FsIFVuaXZlcnNpdHkgb2Yg
            TGliZXJlYzEOMAwGA1UECxMFTGlhbmUxFjAUBgNVBAMTDXNoaWJiby50dWwuY3ow
            ggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCdUYyRY1/QVM6cuR3mTWpw
            wHdc6Aeuy/DNUcvzXUEnWZ6bhdPBScu231BBr9NCZ5wr8pZ3N4JB7n5qjScRqpz2
            D1VOtb/BLy8mFEThE/wWE6DINbAMzF1FQjGE4vLO7FWt0G1nURxqe5USZkFLIZUL
            Wqs1Fnrlfsl1hGbm2ELu/8gfXNbnKQKZ12a8qqojpwIec860+zGS+YkHyYz4IzUq
            kRHM0KQG0qcPzeB3OdQMy44nhHknLeNQ7WIpVAn1bx6g3blyAYxEhqGVjGxbm2j1
            r+FV2r/0ZrwVavM9aeOw3bX+F/M2VBPBsbimnuE/xYnjVhIN76PcsaMdtSsYR4+9
            AgMBAAGjYzBhMAsGA1UdDwQEAwIFoDAyBgNVHR8EKzApMCegJaAjhiFodHRwOi8v
            bGlhbmUudHVsLmN6L2NybC90dWxDQS5jcmwwHgYJYIZIAYb4QgENBBEWD3hjYSBj
            ZXJ0aWZpY2F0ZTANBgkqhkiG9w0BAQsFAAOCAQEAZfabkvqZVo4w4LOoLQfVtTP8
            VCI/HoJsgAsYl7Gf2RngZ76XAePhza0JNCfo0SJDJBWca0VtX91EzazFWkRR1cEO
            B0IwF0MVLQttZA8XItk0EaXi05sC/s4qeykLk31AP3mPD2SC58Vty2DbPCWJxLBQ
            31qNr9zxzVzDVAtzXkVWdfdRrgzFICDIfo9BqELUPCIcw2+/xzoy4h33NB05ZFa7
            +UqvbZl6I4Rz3ZWHt8moGUGQF04kKGI3psHS6M0hxMQccNtbb269gFcuK5vb5AZa
            nH0Q6NVuH5v93Y3fOM0wIgosOcrB90QrXq5BpWu+XL1VxhyPnSxTf/teOIy3hw==
            -----END CERTIFICATE-----""";

    @NonNull String idpEntityId;
    @NonNull String idpSingleSignOnServiceLocation;
    @NonNull String idpCertificate;
    @NonNull String userIdentifyingSaml2AttributeName;

    public static Saml2ServiceProviderProperties createSSOCircle() {
        return new Saml2ServiceProviderProperties(
                "https://idp.ssocircle.com",
                "https://idp.ssocircle.com:443/sso/SSORedirect/metaAlias/publicidp",
                SSO_CIRCLE_IDP_VERIFICATION_CERTIFICATE,
                "EmailAddress"
        );
    }

    public static Saml2ServiceProviderProperties createTul() {
        return new Saml2ServiceProviderProperties(
                "https://shibbo.tul.cz/idp/shibboleth",
                "https://shibbo.tul.cz/idp/profile/SAML2/Redirect/SSO",
                TUL_IDP_VERIFICATION_CERTIFICATE,
                "urn:oid:*******.4.1.5923.*******"
        );
    }

}

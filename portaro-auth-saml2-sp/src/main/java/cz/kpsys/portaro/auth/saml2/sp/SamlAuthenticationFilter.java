package cz.kpsys.portaro.auth.saml2.sp;

import cz.kpsys.portaro.auth.AuthenticationSetter;
import cz.kpsys.portaro.auth.Authenticator;
import cz.kpsys.portaro.auth.process.AuthenticationRequestFinder;
import cz.kpsys.portaro.commons.servlet.OnceProcessingFilter;
import cz.kpsys.portaro.commons.web.WebResolver;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.security.saml2.provider.service.authentication.AbstractSaml2AuthenticationRequest;
import org.springframework.security.saml2.provider.service.web.Saml2AuthenticationRequestRepository;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SamlAuthenticationFilter extends OnceProcessingFilter {

    @NonNull AuthenticationRequestFinder<Saml2AuthenticationRequest> authenticationRequestFinder;
    @NonNull WebResolver<Department> currentDepartmentWebResolver;
    @NonNull Authenticator<Saml2AuthenticationRequest, Saml2SuccessAuthentication> authenticator;
    @NonNull AuthenticationSetter authenticationHolder;
    @NonNull Saml2AuthenticationRequestRepository<AbstractSaml2AuthenticationRequest> authenticationRequestRepository;
    @NonNull AuthenticationSuccessHandler successHandler = new SimpleUrlAuthenticationSuccessHandler();

    @Override
    public void doFilterOnce(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException {
        Department department = currentDepartmentWebResolver.resolve(request);
        Optional<Saml2AuthenticationRequest> authRequest = authenticationRequestFinder.findAuthenticationRequest(request, department);
        if (authRequest.isEmpty()) {
            chain.doFilter(request, response);
            return;
        }

        Saml2SuccessAuthentication successAuth = authenticator.authenticate(authRequest.get());
        authenticationHolder.addAuthentication(successAuth);

        authenticationRequestRepository.removeAuthenticationRequest(request, response);

        successHandler.onAuthenticationSuccess(request, response, successAuth);
    }
}

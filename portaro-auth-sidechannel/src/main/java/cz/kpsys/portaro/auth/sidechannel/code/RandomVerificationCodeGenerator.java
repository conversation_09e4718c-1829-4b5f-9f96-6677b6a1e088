package cz.kpsys.portaro.auth.sidechannel.code;

import cz.kpsys.portaro.commons.util.StringUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Random;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RandomVerificationCodeGenerator implements VerificationCodeGenerator {

    public static final Integer CODE_LENGTH = 6;

    @NonNull Random random;

    public RandomVerificationCodeGenerator() {
        this(new Random());
    }

    public static RandomVerificationCodeGenerator ofCustomSeed(long seed) {
        return new RandomVerificationCodeGenerator(new Random(seed));
    }

    @Override
    public String generate() {
        Integer codeNumber = (int) (random.nextDouble() * Math.pow(10, CODE_LENGTH));
        return StringUtil.prependToSize(String.valueOf(codeNumber), '0', CODE_LENGTH);
    }
}

package cz.kpsys.portaro.auth.sidechannel.link;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.web.EnablableUrl;
import cz.kpsys.portaro.commons.web.UrlCreator;
import cz.kpsys.portaro.conversation.ActionRequest;
import cz.kpsys.portaro.conversation.ActionRequestUrlGenerator;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.security.AccessDeniedException;
import cz.kpsys.portaro.token.InsufficientScopeException;
import cz.kpsys.portaro.token.TemporalTokenRepository;
import cz.kpsys.portaro.token.TokenExpiredException;
import cz.kpsys.portaro.token.UserTemporalToken;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.Duration;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class TokenVerificationLinkService implements VerificationLinkGenerator, VerificationLinkAcceptor {

    private static final String LINK_AUTH_SCOPE = "link-auth";

    @NonNull ContextualProvider<Department, Boolean> enabledProvider;
    @NonNull TemporalTokenRepository<UserTemporalToken, Department> temporalTokenRepository;
    @NonNull ActionRequestUrlGenerator actionRequestUrlGenerator;

    @Override
    public EnablableUrl createLink(@NonNull BasicUser user, @NonNull Department ctx) {
        if (!enabledProvider.getOn(ctx)) {
            return EnablableUrl.disabled();
        }

        String jwt = temporalTokenRepository.save(UserTemporalToken.ofSingleScope(user, LINK_AUTH_SCOPE, Duration.ofMinutes(5)), ctx);
        String actionRequestPath = new UrlCreator()
                .path(VerificationLinkAcceptController.VERIFICATION_LINK_ACCEPT_ENDPOINT)
                .addParameter(VerificationLinkAcceptController.TOKEN_PARAMETER_NAME, jwt)
                .build();
        String url = actionRequestUrlGenerator.generateUrl(ctx, ActionRequest.ofPost(actionRequestPath, new Object()));
        return EnablableUrl.enabled(url);
    }

    @Override
    public void acceptToken(@NonNull VerificationTokenAcceptCommand command) {
        try {
            temporalTokenRepository.load(command.getToken(), command.getCurrentDepartment()).assertScope(LINK_AUTH_SCOPE);
        } catch (TokenExpiredException | InsufficientScopeException e) {
            throw new AccessDeniedException(e.getMessage(), e.getText(), e);
        }
    }

}

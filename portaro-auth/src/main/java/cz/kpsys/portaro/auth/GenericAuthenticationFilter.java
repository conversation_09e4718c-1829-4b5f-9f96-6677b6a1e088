package cz.kpsys.portaro.auth;

import cz.kpsys.portaro.auth.process.AuthenticationRequest;
import cz.kpsys.portaro.auth.process.AuthenticationRequestFinder;
import cz.kpsys.portaro.auth.process.AuthoritiedSuccessAuthentication;
import cz.kpsys.portaro.commons.servlet.OnceProcessingFilter;
import cz.kpsys.portaro.commons.web.WebResolver;
import cz.kpsys.portaro.department.Department;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class GenericAuthenticationFilter<AUTH_REQUEST extends AuthenticationRequest<?>, SUCCESS_AUTH extends AuthoritiedSuccessAuthentication> extends OnceProcessingFilter {

    @NonNull AuthenticationRequestFinder<AUTH_REQUEST> authenticationRequestFinder;
    @NonNull WebResolver<Department> currentDepartmentWebResolver;
    @NonNull Authenticator<AUTH_REQUEST, SUCCESS_AUTH> authenticator;
    @NonNull AuthenticationSetter authenticationHolder;

    @Override
    public void doFilterOnce(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException {
        Department department = currentDepartmentWebResolver.resolve(request);
        Optional<AUTH_REQUEST> authRequest = authenticationRequestFinder.findAuthenticationRequest(request, department);
        if (authRequest.isPresent()) {
            log.debug("Request contains auth request {} (by finder {}, authenticator {})", authRequest, authenticationRequestFinder, authenticator);
            SUCCESS_AUTH successAuth = authenticator.authenticate(authRequest.get());
            authenticationHolder.addAuthentication(successAuth);
        } else {
            log.debug("Request does not contain auth request (by finder {}, authenticator {})", authenticationRequestFinder, authenticator);
        }

        chain.doFilter(request, response);
    }
}

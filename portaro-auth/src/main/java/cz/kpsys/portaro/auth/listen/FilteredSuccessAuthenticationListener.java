package cz.kpsys.portaro.auth.listen;

import cz.kpsys.portaro.auth.process.AuthoritiedSuccessAuthentication;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.function.Predicate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class FilteredSuccessAuthenticationListener implements SuccessAuthenticationListener {

    @NonNull SuccessAuthenticationListener delegate;
    @NonNull Predicate<AuthoritiedSuccessAuthentication> filter;

    public static FilteredSuccessAuthenticationListener forAllowedTypes(@NonNull SuccessAuthenticationListener delegate, Collection<Class<? extends AuthoritiedSuccessAuthentication>> types) {
        return new FilteredSuccessAuthenticationListener(delegate, authoritiedSuccessAuthentication -> types.stream().anyMatch(authType -> authoritiedSuccessAuthentication.getClass() == authType));
    }

    @Override
    public void onSuccessAuth(AuthoritiedSuccessAuthentication authentication) {
        if (filter.test(authentication)) {
            delegate.onSuccessAuth(authentication);
        }
    }
}

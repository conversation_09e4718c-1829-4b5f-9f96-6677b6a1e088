package cz.kpsys.portaro.auth.process;

import cz.kpsys.portaro.department.Department;
import jakarta.servlet.http.HttpServletRequest;
import lombok.NonNull;

import java.util.Optional;

public interface AuthenticationRequestFinder<AUTH_REQUEST extends AuthenticationRequest<?>> {

    Optional<AUTH_REQUEST> findAuthenticationRequest(@NonNull HttpServletRequest request, @NonNull Department department);
}

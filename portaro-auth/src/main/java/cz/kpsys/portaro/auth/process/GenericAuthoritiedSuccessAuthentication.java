package cz.kpsys.portaro.auth.process;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.AuthableUser;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.util.Collection;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public abstract class GenericAuthoritiedSuccessAuthentication extends AbstractAuthenticationToken implements AuthoritiedSuccessAuthentication {

    @NonNull
    AuthableUser user;

    @Getter
    @NonNull
    Department ctx;

    /**
     * Creates a token with the supplied array of authorities.
     */
    public GenericAuthoritiedSuccessAuthentication(@NonNull AuthableUser user, @NonNull Department ctx) {
        this(user, ctx, ListUtil.convert(user.getRole(), SimpleGrantedAuthority::new));
    }

    /**
     * Creates a token with the supplied array of authorities.
     */
    public GenericAuthoritiedSuccessAuthentication(@NonNull AuthableUser user, @NonNull Department ctx, @NonNull Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.user = user;
        this.ctx = ctx;
        super.setAuthenticated(true); //we must call on super, on this is usualy blocked, see cz.kpsys.portaro.user.auth.anonym.AnonymAuthenticationRequest.setAuthenticated
    }

    @Override
    public Object getPrincipal() {
        return user;
    }

    @Override
    public AuthableUser getActiveUser() {
        return user;
    }

}

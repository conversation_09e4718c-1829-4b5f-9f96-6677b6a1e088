package cz.kpsys.portaro.view.web.rest;

import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.formannotation.annotations.form.ConfirmableRequest;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.bool.BooleanEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.number.NumberEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.singleacceptable.SingleAcceptableEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.text.TextEditor;
import cz.kpsys.portaro.search.lucene.facets.FacetDefinitionType;
import cz.kpsys.portaro.search.lucene.facets.FacetScope;
import cz.kpsys.portaro.search.lucene.facets.FacetType;
import cz.kpsys.portaro.sorting.SortingItem;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.With;

@Form(id = "facetTypeEditationRequest", title = "{commons.Uprava}")
@FormSubmit(path = CatalogWebConstants.API_URL_PREFIX + CatalogWebConstants.FACET_TYPES_URL_PART + "/edit")
@With
public record FacetTypeEditationRequest(
        Boolean confirmed,

        @NotNull
        Integer id,

        @FormPropertyLabel("{commons.nazev}")
        @TextEditor
        @Size(min = 1, max = 80)
        @NotBlank
        String name,

        @FormPropertyLabel("{commons.Poradi}")
        @NumberEditor
        @NotNull
        @Min(0)
        Integer order,

        @FormPropertyLabel("Množina")
        @SingleAcceptableEditor(valuesSourceBean = "facetScopeLoader")
        @NotNull
        FacetScope scope,

        @FormPropertyLabel("{util.TypDefinice}")
        @SingleAcceptableEditor(valuesSourceBean = "facetDefinitionTypeLoader")
        @NotNull
        FacetDefinitionType definitionType,

        @FormPropertyLabel("{util.Definice}")
        @TextEditor
        @Size(min = 1, max = 2000)
        @NullableNotBlank
        String definition,

        @FormPropertyLabel("{util.ExemplarovyTyp}")
        @BooleanEditor
        Boolean exemplarType,

        @FormPropertyLabel("{commons.Zapnuto}")
        @BooleanEditor
        Boolean enabled,

        @FormPropertyLabel("Datový typ")
        @TextEditor
        @Size(min = 1, max = FacetType.PROPERTY_DATATYPE_MAX_LENGTH)
        @NullableNotBlank
        String datatype,

        @FormPropertyLabel("Řazení")
        @SingleAcceptableEditor(valuesSourceBean = "facetTypeSortingLoader")
        @NotNull
        SortingItem sorting
) implements ConfirmableRequest<FacetTypeEditationRequest> {

}

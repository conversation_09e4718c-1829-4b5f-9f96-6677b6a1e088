package cz.kpsys.portaro.view.web.rest.exemplar;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.volume.Volume;
import cz.kpsys.portaro.exemplar.volumenumber.VolumeNumberSequenceItemLoader;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.sequence.SequenceItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Calendar;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class VolumeRequestDefaulter implements TypedAuthenticatedContextualObjectModifier<VolumeCreationRequest> {

    @NonNull VolumeNumberSequenceItemLoader volumeNumberSequenceItemLoader;

    @Override
    public VolumeCreationRequest modify(VolumeCreationRequest volumeRequest, Department ctx, UserAuthentication currentAuth) {
        SequenceItem volumeNumberSequenceItem = volumeNumberSequenceItemLoader.getByDocument(volumeRequest.document());
        String volumeNumber = null;
        if (volumeNumberSequenceItem != null) {
            volumeNumber = volumeNumberSequenceItem.getValue();
        }

        return volumeRequest
                .withVolumeNumber(volumeNumber)
                .withYear(Integer.toString(Calendar.getInstance().get(Calendar.YEAR)))
                .withPeriodicityMultiplier(Volume.DEFAULT_PERIODICITY_MULTIPLIER);
    }
}

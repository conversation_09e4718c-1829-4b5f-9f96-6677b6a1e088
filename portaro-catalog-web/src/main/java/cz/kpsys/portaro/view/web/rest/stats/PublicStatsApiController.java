package cz.kpsys.portaro.view.web.rest.stats;

import cz.kpsys.portaro.action.ActionSaver;
import cz.kpsys.portaro.action.Movement;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.TimeGranularity;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.editedproperty.EditedPropertyList;
import cz.kpsys.portaro.form.valueeditor.date.DateValueEditor;
import cz.kpsys.portaro.form.valueeditor.singleacceptable.SingleAcceptableValueEditor;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.DefaultAsMapGettableSearchParams;
import cz.kpsys.portaro.search.DefaultParamsFactory;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.stats.ParamsWithResult;
import cz.kpsys.portaro.stats.StatsConstants;
import cz.kpsys.portaro.stats.StatsLoader;
import cz.kpsys.portaro.view.web.ratelimit.RateLimited;
import cz.kpsys.portaro.web.GenericApiController;
import jakarta.servlet.http.HttpSession;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/api/stats/public")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class PublicStatsApiController extends GenericApiController {

    @NonNull ActionSaver actionSaver;
    @NonNull StatsLoader<MapBackedParams> statsLoader;
    @NonNull DefaultParamsFactory<?> defaultParamsFactory;

    @RequestMapping
    @RateLimited("PublicStatsApiController.get")
    public PublicStatsData get(MapBackedParams params,
                               HttpSession session,
                               @CurrentDepartment Department ctx,
                               UserAuthentication currentAuth) {
        actionSaver.logAction(new Movement(session.getId(), Movement.PREDMET_STATISTIKY));

        DefaultAsMapGettableSearchParams defaultParams = defaultParamsFactory.create(currentAuth, ctx);
        MapBackedParams finalParams = defaultParams.overwriteWith(params);

        ParamsWithResult<?> stats = statsLoader.load(finalParams);
        return new PublicStatsData(stats);
    }

    @RequestMapping("formSettings")
    public EditedPropertyList getFormSettings() {
        return new EditedPropertyList()
                .add(CoreSearchParams.FROM_DATE.getName(), Texts.ofMessageCoded("commons.From"), DateValueEditor.getEmptyEditor()
                        .withRequired(true))
                .add(CoreSearchParams.TO_DATE.getName(), Texts.ofMessageCoded("commons.To"), DateValueEditor.getEmptyEditor()
                        .withRequired(true))
                .add(StatsConstants.SearchParams.TIME_GRANULARITY.getName(), Texts.ofMessageCoded("statistiky.Seskupit"), SingleAcceptableValueEditor.getEmptyEditor(TimeGranularity.CODEBOOK)
                        .withRequired(true)
                        .withBlockInRow());
    }

    public record PublicStatsData(
            ParamsWithResult<?> stats
    ) {}
}
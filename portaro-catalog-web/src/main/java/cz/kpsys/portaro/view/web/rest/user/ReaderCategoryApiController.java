package cz.kpsys.portaro.view.web.rest.user;

import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedSaveResponse;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import cz.kpsys.portaro.web.GenericApiController;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Hidden
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
@RequestMapping(CatalogWebConstants.API_URL_PREFIX + CatalogWebConstants.READER_CATEGORIES_URL_PART)
@ResponseBody
public class ReaderCategoryApiController extends GenericApiController {

    @NonNull AllValuesProvider<ReaderCategory> readerCategoryLoader;
    @NonNull Saver<ReaderCategory, ReaderCategory> readerCategorySaver;

    @GetMapping
    public List<ReaderCategory> getAll() {
        return readerCategoryLoader.getAll();
    }

    @PostMapping
    public ActionResponse save(@RequestBody @ValidFormObject ReaderCategoryCreationRequest request) {
        ReaderCategory readerCategory = mapCreationRequestToReaderCategory(request);
        readerCategory = readerCategorySaver.save(readerCategory);
        return FinishedSaveResponse.saved(readerCategory);
    }

    private ReaderCategory mapCreationRequestToReaderCategory(@NonNull ReaderCategoryCreationRequest request) {
        return new ReaderCategory(
                request.getId(),
                request.getName(),
                request.getRegistrationFee(),
                request.getRegistrationDurationMonths(),
                request.getLendingAllowed(),
                request.getRemindingActive(),
                request.getLoanCountLimit(),
                request.getRegistrationToDateInFormatDDMM(),
                request.getInternetSessionLimitMinutes(),
                request.getGroupable(),
                request.getReservationsCountLimit(),
                request.getOrdersCountLimit());
    }

}

package cz.kpsys.portaro.department.activation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentEntity;
import cz.kpsys.portaro.event.ActivationCommand;
import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.event.Eventer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DefaultDepartmentActivator implements DepartmentActivator {

    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull Saver<Department, DepartmentEntity> departmentSaver;
    @NonNull Eventer eventer;

    @Override
    public void activateDepartment(UserAuthentication currentAuth, Department currentDepartment, @NonNull ActivationCommand<Department> activationCommand) {
        if (activationCommand.getEntity().isActive()) {
            throw new IllegalStateException("Cannot activate already activated department");
        }

        Event activationEvent = eventer.save(
                Event.Codes.DEPARTMENT_ACTIVATION,
                currentAuth,
                currentDepartment,
                new DepartmentActivationEventData(
                        activationCommand.getInitiator().getId(),
                        activationCommand.getEntity().getId()
                )
        );
        Department department = departmentLoader.getById(activationCommand.getEntity().getId());
        department.setActivationEventId(activationEvent.getId());
        departmentSaver.save(department);
    }


    private record DepartmentActivationEventData(@NonNull Object initiatorUserId, @NonNull Object entityId) {
    }

}

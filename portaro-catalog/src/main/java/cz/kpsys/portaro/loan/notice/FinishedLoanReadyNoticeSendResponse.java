package cz.kpsys.portaro.loan.notice;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.conversation.ActionResponseWithText;
import cz.kpsys.portaro.conversation.ExceptionedFinishedResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.localization.LocalizationCodes;
import lombok.NonNull;

public class FinishedLoanReadyNoticeSendResponse {

    public static @NonNull ActionResponseWithText noticeSenderResultToResponse(@NonNull LoanReadyNoticeSendResult result) {
        return switch (result) {
            case LoanReadyNoticeMailSender.MailSentOK _ ->
                    new FinishedActionResponse(Texts.ofMessageCoded(LocalizationCodes.MailWasSuccessfullySent));
            case LoanReadyNoticeSmsSender.SmsSentOK _ ->
                    new FinishedActionResponse(Texts.ofMessageCoded(LocalizationCodes.SmsWasSuccessfullySent));
            case LoanReadyNoticeSendResult.SendingError err -> new ExceptionedFinishedResponse(err.lastError());
            default -> FinishedActionResponse.ok();
        };
    }

}

package cz.kpsys.portaro.stats;

import cz.kpsys.portaro.search.SearchParams;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class StatsLoaderConverterAdapter<PARAMS extends SearchParams> implements StatsLoader<PARAMS> {

    @NonNull Converter<PARAMS, Object> converter;

    @Override
    public ParamsWithResult<PARAMS> load(PARAMS params) {
        Object result = converter.convert(params);
        return new ParamsWithResult<>(params, result);
    }
}

package cz.kpsys.portaro.stats.overall;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.exemplar.acquisitionway.AcquisitionWay;
import cz.kpsys.portaro.exemplar.thematicgroup.ThematicGroup;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import cz.kpsys.portaro.stats.model.LabeledCount;
import cz.kpsys.portaro.stats.model.MultipleCountsStat;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.commons.util.ListUtil.getListOfIds;
import static cz.kpsys.portaro.databasestructure.ExemplarDb.KAT1_5;
import static cz.kpsys.portaro.databasestructure.ExemplarDb.UBYTKY;
import static cz.kpsys.portaro.exemplar.ExemplarConstants.SearchParams.LOCATION;
import static cz.kpsys.portaro.search.CoreSearchParams.*;


@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbOverallStatsLoader implements OverallStatsLoader, RowMapper<Map<String, Integer>> {
    
    private static final String STAT_VERDENIK = "STAT_VERDENIK";
    private static final String FK_PUJC = "FK_PUJC";
    private static final String DATUM = "DATUM";
    
    
    
    //2. UŽIVATELÉ
    
    private static final String POC_REG_CTEN = "POC_REG_CTEN"; //0201 - Registrovaní uživtelé ve sledovaném období
    private static final String SUM_POC_REG_CTEN_AS = POC_REG_CTEN;
    
    private static final String POC_REG_CTEN15 = "POC_REG_CTEN15"; //0202 - Z toho registrovaní uživatelé do 15 let
    private static final String SUM_POC_REG_CTEN15_AS = POC_REG_CTEN15;
    
    private static final String PNK_CELKEM = "PNK_VYP+PNK_INT+PNK_KULT+PNK_VZDEL+PNK_ONLINE"; //0203 - Návštěvníci celkem
    private static final String SUM_PNK_CELKEM_AS = "PNK_CELKEM";
    
    private static final String PNK_FYZ = "PNK_VYP+PNK_INT+PNK_KULT+PNK_VZDEL"; //0204 - Návštěvníci knihovny (fyzičtí)
    private static final String SUM_PNK_FYZ_AS = "PNK_FYZ";
    
    private static final String PNK_VYP = "PNK_VYP"; //0205 - Návštěvníci půjčoven s studoven
    private static final String SUM_PNK_VYP_AS = PNK_VYP;
    
    private static final String PNK_INT = "PNK_INT"; //0206 - Návštěvníci využívající internet v knihovně
    private static final String SUM_PNK_INT_AS = PNK_INT;
    
    private static final String PNK_KULT = "PNK_KULT"; //0207 - Návštěvníci kulturních akcí
    private static final String SUM_PNK_KULT_AS = PNK_KULT;
    
    private static final String PNK_VZDEL = "PNK_VZDEL"; //0208 - Návštěvníci vzdělávacích akcí
    private static final String SUM_PNK_VZDEL_AS = PNK_VZDEL;
    
    private static final String PNK_ONLINE = "PNK_ONLINE"; //0209 - Návštěvníci on-line služeb
    private static final String SUM_PNK_ONLINE_AS = PNK_ONLINE;
    
    
    
    
    //3. VÝPŮJČKY
    
    private static final String VYP_CELKEM = "VYP_DN+VYP_MN+VYP_DB+VYP_MB+VYP_PER+VYP_RUK+VYP_MIKRO+VYP_KARTO+VYP_HUD+VYP_ZVUK+VYP_ZVOB+VYP_OBR+VYP_ELE+VYP_OST"; //0301 - Výpůjčky celkem
    private static final String SUM_VYP_CELKEM_AS = "VYP_CELKEM";
    
    private static final String VYP_DN = "VYP_DN"; //0302 - Naučná literatura dospělým uživatelům (knihy)
    private static final String SUM_VYP_DN_AS = VYP_DN;
    
    private static final String VYP_DB = "VYP_DB"; //0303 - Krásná literatura dospělým uživatelům (knihy)
    private static final String SUM_VYP_DB_AS = VYP_DB;
    
    private static final String VYP_MN = "VYP_MN"; //0304 - Naučná literatura dětem (knihy)
    private static final String SUM_VYP_MN_AS = VYP_MN;
    
    private static final String VYP_MB = "VYP_MB"; //0305 - Krásná literatura dětem (knihy)
    private static final String SUM_VYP_MB_AS = VYP_MB;
    
    private static final String VYP_PER = "VYP_PER"; //0306 - Výpůjčky periodik
    private static final String SUM_VYP_PER_AS = VYP_PER;
    
    private static final String VYP_RUK = "VYP_RUK"; //0307 - Rukopisy
    private static final String SUM_VYP_RUK_AS = VYP_RUK;
    
    private static final String VYP_MIKRO = "VYP_MIKRO"; //0308 - Mikrografické dokumenty
    private static final String SUM_VYP_MIKRO_AS = VYP_MIKRO;
    
    private static final String VYP_KARTO = "VYP_KARTO"; //0309 - Kartografické dokumenty
    private static final String SUM_VYP_KARTO_AS = VYP_KARTO;
    
    private static final String VYP_HUD = "VYP_HUD"; //0310 - Tištěné hudebniny
    private static final String SUM_VYP_HUD_AS = VYP_HUD;
    
    private static final String VYP_ZVUK = "VYP_ZVUK"; //0311 - Zvukové
    private static final String SUM_VYP_ZVUK_AS = VYP_ZVUK;
    
    private static final String VYP_ZVOB = "VYP_ZVOB"; //0312 - Zvukově obrazové
    private static final String SUM_VYP_ZVOB_AS = VYP_ZVOB;
    
    private static final String VYP_OBR = "VYP_OBR"; //0313 - Obrazové
    private static final String SUM_VYP_OBR_AS = VYP_OBR;
    
    private static final String VYP_ELE = "VYP_ELE"; //0314 - Elektronické dokumenty 
    private static final String SUM_VYP_ELE_AS = VYP_ELE;
    
    private static final String VYP_OST = "VYP_OST"; //0315 - Jiné
    private static final String SUM_VYP_OST_AS = VYP_OST;
    
    private static final String VYP_PREZ = "VYP_PREZ"; //0316 - Prezenční výpůjčky evidované
    private static final String SUM_VYP_PREZ_AS = VYP_PREZ;
    
    private static final String VYP_PROL = "VYP_PROL"; //0317 - Prolongace
    private static final String SUM_VYP_PROL_AS = VYP_PROL;
    
    
    
    //4. DALŠÍ ÚDAJE
    
    //Meziknihovní výpůjční služba v rámci státu
    //Obdržené požadavky z jiných knihoven
    private static final String MVS_AKT_CELK = "MVS_AKT_CELK"; //0401 - Počet požadavků
    private static final String SUM_MVS_AKT_CELK_AS = MVS_AKT_CELK;
    private static final String MVS_AKT_OK = "MVS_AKT_OK"; //0402 - Počet kladně vyřízených požadavků
    private static final String SUM_MVS_AKT_OK_AS = MVS_AKT_OK;
    //Zaslané požadavky jiným knihovnám
    private static final String MVS_PAS_CELK = "MVS_PAS_CELK"; //0403 - Počet požadavků
    private static final String SUM_MVS_PAS_CELK_AS = MVS_PAS_CELK;
    private static final String MVS_PAS_OK = "MVS_PAS_OK"; //0404 - Počet kladně vyřízených požadavků
    private static final String SUM_MVS_PAS_OK_AS = MVS_PAS_OK;
    
    //Mezinárodní meziknihovní výpůjční služba
    //Požadavky z jiných zemí
    private static final String MMVS_AKT_CELK = "MMVS_AKT_CELK"; //0405 - Počet požadavků
    private static final String SUM_MMVS_AKT_CELK_AS = MMVS_AKT_CELK;
    private static final String MMVS_AKT_OK = "MMVS_AKT_OK"; //0406 - Počet kladně vyřízených požadavků
    private static final String SUM_MMVS_AKT_OK_AS = MMVS_AKT_OK;
    //Požadavky do jiných zemí
    private static final String MMVS_PAS_CELK = "MMVS_PAS_CELK"; //0407 - Počet požadavků
    private static final String SUM_MMVS_PAS_CELK_AS = MMVS_PAS_CELK;
    private static final String MMVS_PAS_OK = "MMVS_PAS_OK"; //0408 - Počet kladně vyřízených požadavků
    private static final String SUM_MMVS_PAS_OK_AS = MMVS_PAS_OK;
    
    //Výměnné fondy
    //Půjčené jiným knihovnám
    private static final String VF_AKT_SOUB = "VF_AKT_SOUB"; //0409 - Počet souborů
    private static final String SUM_VF_AKT_SOUB_AS = VF_AKT_SOUB;
    private static final String VF_AKT_POC = "VF_AKT_POC"; //0410 - Počet svazků
    private static final String SUM_VF_AKT_POC_AS = VF_AKT_POC;
    //Půjčené od jiných knihoven
    private static final String VF_PAS_SOUB = "VF_PAS_SOUB"; //0411 - Počet souborů
    private static final String SUM_VF_PAS_SOUB_AS = VF_PAS_SOUB;
    private static final String VF_PAS_POC = "VF_PAS_POC"; //0412 - Počet svazků
    private static final String SUM_VF_PAS_POC_AS = VF_PAS_POC;
    
    //Dokončení oddíl 4.
    private static final String POR_KONZ_RF = "POR_KONZ_RF"; //0413 - Poradenská a konzultační činnost pro knihovníky a v rámci RF
    private static final String SUM_POR_KONZ_RF_AS = POR_KONZ_RF;
    
    private static final String MET_NAV = "MET_NAV"; //???? - Metodické návštěvy  - celkem
    private static final String SUM_MET_NAV_AS = MET_NAV;
    
    private static final String MET_AKCE_CELK = "MET_AKCE_CELK"; //???? - Vzdělávací akce (a porady) pro knihovníky a v rámci RF - Počet akcí
    private static final String SUM_MET_AKCE_CELK_AS = MET_AKCE_CELK;
    
    private static final String MET_AKCE_NAV = "MET_AKCE_NAV"; //???? - Vzdělávací akce (a porady) pro knihovníky a v rámci RF - Počet knihovníků
    private static final String SUM_MET_AKCE_NAV_AS = MET_AKCE_NAV;


    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull ByIdLoadable<Fond, Integer> fondLoader;
    @NonNull ByIdLoadable<AcquisitionWay, String> acquisitionWayLoader;
    @NonNull ByIdLoadable<ThematicGroup, String> thematicGroupLoader;
    @NonNull DepartmentAccessor departmentAccessor;
        
    
    /**
     * Statistika se pocita na budovu a lokaci.
     * - zmena - jen na budovu
     * @param p
     * @return 
     */
    @Override
    public Map<String, Integer> get(MapBackedParams p) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(
                AS(SUM(POC_REG_CTEN), SUM_POC_REG_CTEN_AS),
                AS(SUM(POC_REG_CTEN15), SUM_POC_REG_CTEN15_AS),
                AS(SUM(PNK_CELKEM), SUM_PNK_CELKEM_AS),
                AS(SUM(PNK_FYZ), SUM_PNK_FYZ_AS),
                AS(SUM(PNK_VYP), SUM_PNK_VYP_AS),
                AS(SUM(PNK_INT), SUM_PNK_INT_AS),
                AS(SUM(PNK_KULT), SUM_PNK_KULT_AS),
                AS(SUM(PNK_VZDEL), SUM_PNK_VZDEL_AS),
                AS(SUM(PNK_ONLINE), SUM_PNK_ONLINE_AS),
                AS(SUM(VYP_CELKEM), SUM_VYP_CELKEM_AS),
                AS(SUM(VYP_DN), SUM_VYP_DN_AS),
                AS(SUM(VYP_DB), SUM_VYP_DB_AS),
                AS(SUM(VYP_MN), SUM_VYP_MN_AS),
                AS(SUM(VYP_MB), SUM_VYP_MB_AS),
                AS(SUM(VYP_PER), SUM_VYP_PER_AS),
                AS(SUM(VYP_RUK), SUM_VYP_RUK_AS),
                AS(SUM(VYP_MIKRO), SUM_VYP_MIKRO_AS),
                AS(SUM(VYP_KARTO), SUM_VYP_KARTO_AS),
                AS(SUM(VYP_HUD), SUM_VYP_HUD_AS),
                AS(SUM(VYP_ZVUK), SUM_VYP_ZVUK_AS),
                AS(SUM(VYP_ZVOB), SUM_VYP_ZVOB_AS),
                AS(SUM(VYP_OBR), SUM_VYP_OBR_AS),
                AS(SUM(VYP_ELE), SUM_VYP_ELE_AS),
                AS(SUM(VYP_OST), SUM_VYP_OST_AS),
                AS(SUM(VYP_PREZ), SUM_VYP_PREZ_AS),
                AS(SUM(VYP_PROL), SUM_VYP_PROL_AS),
                AS(SUM(MVS_AKT_CELK), SUM_MVS_AKT_CELK_AS),
                AS(SUM(MVS_AKT_OK), SUM_MVS_AKT_OK_AS),
                AS(SUM(MVS_PAS_CELK), SUM_MVS_PAS_CELK_AS),
                AS(SUM(MVS_PAS_OK), SUM_MVS_PAS_OK_AS),
                AS(SUM(MMVS_AKT_CELK), SUM_MMVS_AKT_CELK_AS),
                AS(SUM(MMVS_AKT_OK), SUM_MMVS_AKT_OK_AS),
                AS(SUM(MMVS_PAS_CELK), SUM_MMVS_PAS_CELK_AS),
                AS(SUM(MMVS_PAS_OK), SUM_MMVS_PAS_OK_AS),
                AS(SUM(VF_AKT_SOUB), SUM_VF_AKT_SOUB_AS),
                AS(SUM(VF_AKT_POC), SUM_VF_AKT_POC_AS),
                AS(SUM(VF_PAS_SOUB), SUM_VF_PAS_SOUB_AS),
                AS(SUM(VF_PAS_POC), SUM_VF_PAS_POC_AS),
                AS(SUM(POR_KONZ_RF), SUM_POR_KONZ_RF_AS),
                AS(SUM(MET_NAV), SUM_MET_NAV_AS),
                AS(SUM(MET_AKCE_CELK), SUM_MET_AKCE_CELK_AS),
                AS(SUM(MET_AKCE_NAV), SUM_MET_AKCE_NAV_AS)
        );
        
        sq.from(STAT_VERDENIK);

        if (p.has(DEPARTMENT) && !departmentAccessor.coversAllDepartments(p.get(DEPARTMENT))) {
            if (!p.hasLength(DEPARTMENT)) {
                return Map.of();
            }
            sq.where().and().in(FK_PUJC, getListOfIds(p.get(DEPARTMENT)));
        }
//        if (ListUtil.hasLength(p.getLocation())) {
//            List<Department> departments = departmentLoader.getAllByLocations(p.getLocation());
//            sq.where().and().in(FK_PUJC, ListUtil.getListOfIds(departments));
//        }
        if (p.has(FROM_DATE)) {
            sq.where().and().gtEq(DATUM, p.get(FROM_DATE));
        }
        if (p.has(TO_DATE)) {
            sq.where().and().lt(DATUM, p.get(TO_DATE));
        }
        
        return jdbcTemplate.queryForObject(sq.getSql(), sq.getParamMap(), this);
    }




    @Override
    public MultipleCountsStat<Long> getExemplarIncreasesByAcqWaysStats(MapBackedParams p) {
        return getExemplarsByStats(p, KAT1_5.FK_ZPNAB, acquisitionWayLoader, false);
    }

    @Override
    public MultipleCountsStat<Long> getExemplarIncreasesByFondsStats(MapBackedParams p) {
        return getExemplarsByStats(p, KAT1_5.FK_DOKFOND, fondLoader, false);
    }
    
    @Override
    public MultipleCountsStat<Long> getExemplarIncreasesByThematicGroupStats(MapBackedParams p) {
        return getExemplarsByStats(p, KAT1_5.FK_TEMSKUP, thematicGroupLoader, false);
    }

    @Override
    public MultipleCountsStat<Long> getExemplarDecreasesByAcqWaysStats(MapBackedParams p) {
        return getExemplarsByStats(p, KAT1_5.FK_ZPNAB, acquisitionWayLoader, true);
    }

    @Override
    public MultipleCountsStat<Long> getExemplarDecreasesByFondsStats(MapBackedParams p) {
        return getExemplarsByStats(p, KAT1_5.FK_DOKFOND, fondLoader, true);
    }

    @Override
    public MultipleCountsStat<Long> getExemplarDecreasesByThematicGroupStats(MapBackedParams p) {
        return getExemplarsByStats(p, KAT1_5.FK_TEMSKUP, thematicGroupLoader, true);
    }


    /**
     * Statistika se pocita na budovu a lokaci!
     * @param p
     * @param groupByKat15Column
     * @param byIdLoadable
     * @param decreases zda se jedna o prirustky nebo ubytky
     * @return
     */
    public <E, ID> MultipleCountsStat<Long> getExemplarsByStats(MapBackedParams p, String groupByKat15Column, ByIdLoadable<E, ID> byIdLoadable, boolean decreases) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(COUNT_ALL__POCET, TC(KAT1_5.TABLE, groupByKat15Column));
        sq.from(KAT1_5.TABLE);
        if (p.has(DEPARTMENT) && !departmentAccessor.coversAllDepartments(p.get(DEPARTMENT))) {
            if (!p.hasLength(DEPARTMENT)) {
                return MultipleCountsStat.ofEmptyLongs();
            }
            sq.where().and().in(KAT1_5.FK_PUJC, getListOfIds(p.get(DEPARTMENT)));
        }
        if (p.has(LOCATION)) {
            if (!p.hasLength(LOCATION)) {
                return MultipleCountsStat.ofEmptyLongs();
            }
            sq.where().and().in(KAT1_5.FK_LOKACE, getListOfIds(p.get(LOCATION)));
        }
        if (decreases) {
            //jedna se o ubytky - sjoinujeme s UBYTKY a datum hledame podle UBYTKY.DATUM_UBYT
            sq.joins().add(UBYTKY.UBYTKY, COLSEQ(TC(UBYTKY.UBYTKY, UBYTKY.ID_UBYTEK), TC(KAT1_5.TABLE, KAT1_5.FK_UBYTEK)));
            if (p.has(FROM_DATE)) {
                sq.where().and().gtEq(UBYTKY.DATUM_UBYT, p.get(FROM_DATE));
            }
            if (p.has(TO_DATE)) {
                sq.where().and().lt(UBYTKY.DATUM_UBYT, p.get(TO_DATE));
            }
        } else {
            //jedna se o prirustky - datum hledame podle KAT1_5.DATUM
//            sq.where().and().isNull(ExemplarDb.KAT1_5.FK_UBYTEK); ?? toto tu asi nema byt proto zakomentovano (chceme vsechny prirustky v danem obdobi, ne jen ty, ze kterych se nestaly ubytky)
            if (p.has(FROM_DATE)) {
                sq.where().and().gtEq(KAT1_5.DATUM, p.get(FROM_DATE));
            }
            if (p.has(TO_DATE)) {
                sq.where().and().lt(KAT1_5.DATUM, p.get(TO_DATE));
            }
        }
        sq.groupBy(groupByKat15Column);
        return MultipleCountsStat.ofLongs(jdbcTemplate.query(sq.getSql(), sq.getParamMap(), new CountRowMapper<>(byIdLoadable, groupByKat15Column)));
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class CountRowMapper<E, ID> implements RowMapper<LabeledCount<Long>> {
        
        @NonNull ByIdLoadable<E, ID> byIdLoadable;
        @NonNull String idColumnLabel;
        @NonNull String valueColumnLabel = "POCET";
        
        @Override
        public LabeledCount<Long> mapRow(ResultSet rs, int rowNum) throws SQLException {
            E obj = byIdLoadable.getById((ID) DbUtils.getStandardObject(rs, idColumnLabel));
            Text text = obj instanceof LabeledIdentified ? ((LabeledIdentified) obj).getText() : Texts.ofNative(obj.toString());
            long count = rs.getLong(valueColumnLabel);
            return LabeledCount.ofLong(text, count);
        }
        
    }
    

    @Override
    public Map<String, Integer> mapRow(ResultSet rs, int index) throws SQLException {
        Map<String, Integer> map = new HashMap<>();
        
        ResultSetMetaData rsMetaData = rs.getMetaData();
        int numberOfColumns = rsMetaData.getColumnCount();
        for (int i = 1; i < numberOfColumns + 1; i++) {
            String fieldLabel = rsMetaData.getColumnLabel(i);
            int value = rs.getInt(fieldLabel);
            map.put(fieldLabel, value);
        }
        
        return map;
    }
    
}

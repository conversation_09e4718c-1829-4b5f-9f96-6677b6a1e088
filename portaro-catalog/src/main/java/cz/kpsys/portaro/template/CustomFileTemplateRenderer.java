package cz.kpsys.portaro.template;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.print.RecordDetailRawPrinter;
import cz.kpsys.portaro.record.print.RecordDetailUserFriendlyTextPrinter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanFactory;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CustomFileTemplateRenderer implements StaticTemplateRenderer {

    @NonNull TemplateEngine templateEngine;
    @NonNull BeanFactory applicationContext;
    @NonNull Translator<Department> translator;

    @NonNull
    @Override
    public String render(@NonNull TemplateDescriptor templateDescriptor, @NonNull UserAuthentication currentAuth, @NonNull Department ctx, @NonNull Locale locale) {
        Map<String, Object> model = new HashMap<>();
        model.put("currentAuth", currentAuth);
        model.put("currentBuilding", ctx);
        model.put("applicationContext", applicationContext);
        model.put("recordPrinter", new RecordDetailUserFriendlyTextPrinter(translator, ctx, locale));
        model.put("recordPlainPrinter", new RecordDetailRawPrinter(translator, ctx, locale));
        try {
            return templateEngine.build(templateDescriptor, currentAuth, ctx, model, locale);
        } catch (TemplateNotFoundException e) {
            return "";
        } catch (TemplateAccessException e) {
            log.error("Error while access to template {}", templateDescriptor, e);
            return "";
        }
    }
}

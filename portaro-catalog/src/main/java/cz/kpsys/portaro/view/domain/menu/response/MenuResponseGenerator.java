package cz.kpsys.portaro.view.domain.menu.response;

import cz.kpsys.portaro.MappingConfig;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualBiFunction;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.FilteredList;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.filter.BlacklistObjectFilter;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.intent.DefinedAction;
import cz.kpsys.portaro.intent.GoToAction;
import cz.kpsys.portaro.intent.MappedAction;
import cz.kpsys.portaro.user.AuthableUser;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserByBasicUserLoader;
import cz.kpsys.portaro.user.relation.RepresentableUserLoader;
import cz.kpsys.portaro.view.domain.menu.MenuItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MenuResponseGenerator {

    private static final String USER_MENU_TYPE = "user";

    @NonNull String contextPath;
    @NonNull Provider<Boolean> generateUserMenuProvider;
    @NonNull RepresentableUserLoader representableUserLoader;
    @NonNull List<String> forbiddenMenuItems;
    @NonNull ContextualBiFunction<User, UserAuthentication, Department, Integer> numberOfUserAttentionRequiringActionsProvider;
    @NonNull UserByBasicUserLoader userLoader;
    @NonNull GoToMenuItemActionToResponseMapper goToMenuItemActionToResponseMapper = Mappers.getMapper(GoToMenuItemActionToResponseMapper.class);
    @NonNull MappedMenuItemActionToResponseMapper mappedMenuItemActionToResponseMapper = Mappers.getMapper(MappedMenuItemActionToResponseMapper.class);

    public List<MenuItemResponse> generateMenu(@NonNull List<MenuItem> menuItems, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
        List<MenuItemResponse> menuItemResponses = generateCommonMenu(menuItems, currentAuth, ctx);

        if (generateUserMenuProvider.get()) {
            MenuItemResponse userMenu = new MenuItemResponse();
            userMenu.setType(USER_MENU_TYPE);
            userMenu.setRequiresAuth(true);

            boolean userActionRequired = isUserActionRequired(currentAuth, ctx);
            userMenu.setUserActionRequired(userActionRequired);


            if (currentAuth.isEvided()) {
                List<MenuItemResponse> userSubmenu = new ArrayList<>();

                userSubmenu.add(new MenuItemResponse("user-account", Texts.ofMessageCoded("konto.mojeKonto"), false, new GoToMenuItemActionResponse("%s/#!/users/%s".formatted(contextPath, currentAuth.getActiveUser().getId())), null, null, null, userActionRequired));

                getSwitchableUsers(currentAuth)
                        .map(user -> new MenuItemResponse("login-asx", Texts.ofMessageCodedWithLocalizedArgs("user.auth.LoginAsX", user.getText()), true, new GoToMenuItemActionResponse("%s/login/impersonate?user=%s".formatted(contextPath, user.getId())), null, null, null, false))
                        .forEach(userSubmenu::add);

                userSubmenu.add(new MenuItemResponse("logout", Texts.ofMessageCoded("login.odhlasit"), false, new GoToMenuItemActionResponse(contextPath + "/logout"), null, null, null, false));

                userMenu.setText(currentAuth.getActiveUser().getText());
                userMenu.setSubmenu(userSubmenu);
            } else {
                userMenu.setText(Texts.ofMessageCoded("login.menuLoginButton"));
            }

            menuItemResponses.add(userMenu);
        }

        return menuItemResponses;
    }

    private boolean isUserActionRequired(@NonNull UserAuthentication currentAuth, Department ctx) {
        return numberOfUserAttentionRequiringActionsProvider.getOn(userLoader.getUser(currentAuth.getActiveUser()), currentAuth, ctx) > 0;
    }

    private List<MenuItemResponse> generateCommonMenu(@NonNull List<? extends MenuItem> menuItems, @NonNull UserAuthentication currentAuth, @NonNull Department currentDepartment) {
        return new FilteredList<>(menuItems, new BlacklistObjectFilter<>(forbiddenMenuItems, source -> String.valueOf(source.getId())))
                .stream()
                .filter(menuItem -> menuItem.isShowable(currentDepartment, currentAuth))
                .map(menuItem -> mapMenuItemToResponse(menuItem, currentAuth, currentDepartment))
                .collect(Collectors.toList());
    }

    private MenuItemResponse mapMenuItemToResponse(@NonNull MenuItem menuItem, @NonNull UserAuthentication currentAuth, @NonNull Department currentDepartment) {
        List<MenuItemResponse> submenu = null;
        if (!menuItem.isTerminal(currentDepartment, currentAuth)) {
            submenu = generateCommonMenu(menuItem.getItems(), currentAuth, currentDepartment);
        }

        return new MenuItemResponse(
                menuItem.getId(),
                menuItem.getText(),
                menuItem.requiresLoggedUser() && !currentAuth.isEvided(),
                mapAction(menuItem),
                null,
                submenu,
                menuItem.getIconName() == null ? MenuItemIconMapper.getMenuItemIcon(menuItem.getId()) : menuItem.getIconName(),
                menuItem.isUserActionRequired()
        );
    }

    @Nullable
    private MenuItemActionResponse mapAction(@NonNull MenuItem menuItem) {
        DefinedAction action = menuItem.getAction();
        return switch (action) {
            case null -> null;
            case GoToAction goToAction -> goToMenuItemActionToResponseMapper.map(goToAction);
            case MappedAction<?> mappedAction -> mappedMenuItemActionToResponseMapper.map(mappedAction);
            default ->
                    throw new UnsupportedOperationException("Menu item action %s is not supported".formatted(action));
        };
    }

    private Stream<BasicUser> getSwitchableUsers(@NonNull UserAuthentication currentAuth) {
        BasicUser activeUser = currentAuth.getActiveUser();
        AuthableUser authenticatedUser = currentAuth.getAuthenticatedUser();
        List<BasicUser> allRepresentableUsers = representableUserLoader.getAllRepresentableUsers(activeUser);

        return Stream.concat(Stream.of(authenticatedUser), allRepresentableUsers.stream())
                .filter(switchableUsers -> !activeUser.equals(switchableUsers))
                .distinct();
    }

    @Mapper(config = MappingConfig.class)
    interface GoToMenuItemActionToResponseMapper {
        GoToMenuItemActionResponse map(GoToAction source);
    }

    @Mapper(config = MappingConfig.class)
    interface MappedMenuItemActionToResponseMapper {

        @Mapping(target = "params", source = "params")
        MappedMenuItemActionResponse<Object> map(MappedAction<?> source);
    }
}

package cz.kpsys.portaro.commons.converter;

import cz.kpsys.portaro.commons.convert.StringToIntegerListConverter;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.List;

@Tag("ci")
@Tag("unit")
public class StringToIntegerListConverterTest {
    
    @Test
    public void korektniRozparsovaniIntegeru() {
        String s = "65; 5448;46213;0; -6;1;";
        List<Integer> list = new StringToIntegerListConverter().convert(s);
        
        Assertions.assertNotNull(list);
        Assertions.assertEquals(list.size(), 6);
        Assertions.assertEquals((int) list.get(0), 65);
        Assertions.assertEquals((int) list.get(1), 5448);
        Assertions.assertEquals((int) list.get(2), 46213);
        Assertions.assertEquals((int) list.get(3), 0);
        Assertions.assertEquals((int) list.get(4), -6);
        Assertions.assertEquals((int) list.get(5), 1);
    }
}

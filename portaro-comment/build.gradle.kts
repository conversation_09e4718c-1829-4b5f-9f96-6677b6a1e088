dependencies {
    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")
    implementation("org.mapstruct:mapstruct:+")
    annotationProcessor("org.mapstruct:mapstruct-processor:+")

    implementation(project(":portaro-auth"))
    implementation(project(":portaro-appserver"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-database-structure"))
    implementation(project(":portaro-form-annotation"))
    implementation(project(":portaro-record"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-security"))
    implementation(project(":portaro-sql-generator"))
    implementation(project(":portaro-user"))
    implementation(project(":portaro-web"))

    implementation("org.springframework:spring-core:6.+")
    implementation("org.springframework:spring-jdbc:6.+")
    implementation("org.springframework:spring-web:6.+")

    implementation("org.slf4j:slf4j-api:+")
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
    implementation("com.google.guava:guava:+")
}
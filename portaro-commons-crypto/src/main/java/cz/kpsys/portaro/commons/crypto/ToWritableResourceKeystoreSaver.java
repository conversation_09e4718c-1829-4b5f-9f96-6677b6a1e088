package cz.kpsys.portaro.commons.crypto;

import cz.kpsys.portaro.commons.object.repo.Saver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.WritableResource;

import java.io.IOException;
import java.io.OutputStream;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ToWritableResourceKeystoreSaver implements Saver<KeyStore, KeyStore> {

    @NonNull WritableResource resource;
    @NonNull String keystorePassword;

    @Override
    public @NonNull KeyStore save(@NonNull KeyStore keystore) {
        try (OutputStream out = resource.getOutputStream()) {

            keystore.store(out, keystorePassword.toCharArray());
            log.info("Saved keystore into {}", resource);
            return keystore;

        } catch (IOException | CertificateException | KeyStoreException | NoSuchAlgorithmException ex) {
            throw new RuntimeException(ex);
        }
    }

}

package cz.kpsys.portaro.commons.security;

import cz.kpsys.portaro.commons.crypto.KeyStoreAccessor;
import cz.kpsys.portaro.commons.crypto.NewKeystoreProvider;
import cz.kpsys.portaro.commons.crypto.ToFilesystemKeystoreSaver;
import cz.kpsys.portaro.commons.file.FileUtils;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;

import java.io.File;

import static org.junit.jupiter.api.Assertions.assertTrue;

@Tag("ci")
@Tag("unit")
public class KeyStoreAccessorTest {

    private static final String KEYSTORE_DEFAULT_PASSWORD = "changeit";

    private void saveToTestFileAndCheckNotEmpty(KeyStoreAccessor keystore, String filename) {
        File keystoreFile = FileUtils.createTestTempFile(getClass(), filename);
        new ToFilesystemKeystoreSaver(keystoreFile, KEYSTORE_DEFAULT_PASSWORD).save(keystore.getKeyStore());

        System.out.println(keystoreFile.getAbsolutePath());

        assertTrue(keystoreFile.exists());
        assertTrue(keystoreFile.length() > 0);
    }

    @Test
    public void shouldSaveOpengateCertAndKeyToNewKeystore() {
        KeyStoreAccessor keystore = new KeyStoreAccessor(NewKeystoreProvider.ofJks(KEYSTORE_DEFAULT_PASSWORD).get());
        keystore.setCertificateChainAndKey(new ClassPathResource("opengate-linux_cert+ca.pem"), new ClassPathResource("opengate-lib_opengate_cz.key"), null, "tomcat");
        saveToTestFileAndCheckNotEmpty(keystore, "opengateKeystore.p12");
    }

    @Test
    public void shouldSaveLetsEncryptCertAndKeyToNewKeystore() {
        KeyStoreAccessor keystore = new KeyStoreAccessor(NewKeystoreProvider.ofJks(KEYSTORE_DEFAULT_PASSWORD).get());
        keystore.setCertificateChainAndKey(new ClassPathResource("opengate-linux_cert+ca.pem"), new ClassPathResource("letsencrypt-domain.key"), null, "tomcat");
        saveToTestFileAndCheckNotEmpty(keystore, "letsencryptKeystore.p12");
    }
    
}

package cz.kpsys.portaro.collection;

import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.Set;
import java.util.function.Predicate;

@FieldDefaults(level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public final class SetCutter<F> {

    @NonNull Set<? extends F> remainingItems;

    public SetCutter(@NonNull Collection<? extends F> remainingItems) {
        this.remainingItems = Set.copyOf(remainingItems);
    }

    public @NonNull Set<F> cut(@NonNull Predicate<F> predicate) {
        var split = ListUtil.splitSet(remainingItems, predicate);
        remainingItems = split.unmatched();
        return split.matched();
    }

    public F cutFirst() {
        F first = remainingItems.iterator().next();
        Assert.state(remainingItems.remove(first), "Concurrent modification of remaining items in SetCutter");
        return first;
    }

    public @NonNull Set<? extends F> remainingItems() {
        return remainingItems;
    }

    public boolean hasRemainingItems() {
        return !remainingItems.isEmpty();
    }

    @Override
    public String toString() {
        return "SetCutter{" + remainingItems.size() + " remaining" + '}';
    }
}

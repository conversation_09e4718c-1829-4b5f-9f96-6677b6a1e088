package cz.kpsys.portaro.commons.barcode;

import cz.kpsys.portaro.commons.object.Provider;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ItfBarcodeValidator implements BarCodeValidator {

    @NonNull Provider<@NonNull Integer> lengthProvider;

    @Override
    public String getTypeName() {
        return "Interleaved2of5(%d)".formatted(getCoreLength());
    }

    @Override
    public boolean isValid(@NonNull String source) {
        return source.matches(getRegexPattern());
    }

    @Override
    public String getCore(@NonNull String source) throws InvalidBarCodeException {
        throwIfInvalid(source);
        return source;
    }

    @Override
    public int getMaximumLength() {
        return lengthProvider.get();
    }

    @Override
    public int getCoreLength() {
        return lengthProvider.get();
    }

    @Override
    public String getEditorValidationPattern() {
        return getRegexPattern();
    }

    private String getRegexPattern() {
        return "^(.{%d})$".formatted(getMaximumLength());
    }

    @Override
    public String toString() {
        return "%s barcode validator".formatted(getTypeName());
    }
}

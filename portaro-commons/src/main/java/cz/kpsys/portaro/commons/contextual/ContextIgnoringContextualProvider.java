package cz.kpsys.portaro.commons.contextual;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ContextIgnoringContextualProvider<CTX, V> implements ContextualProvider<CTX, V> {

    @NonNull Provider<V> singleValueProvider;

    public static <CTX, V> ContextIgnoringContextualProvider<CTX, V> ofNull() {
        return new ContextIgnoringContextualProvider<>(StaticProvider.ofNull());
    }

    public static <CTX, V> ContextIgnoringContextualProvider<CTX, @NonNull V> of(@NonNull V staticValue) {
        return new ContextIgnoringContextualProvider<>(StaticProvider.of(staticValue));
    }

    public static <CTX, V> ContextIgnoringContextualProvider<CTX, V> of(@NonNull Provider<V> singleValueProvider) {
        return new ContextIgnoringContextualProvider<>(singleValueProvider);
    }

    @Override
    public V getOn(CTX ctx) throws ItemNotFoundException {
        return singleValueProvider.get();
    }
}

package cz.kpsys.portaro.commons.contextual;

import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;

import java.util.function.BiConsumer;
import java.util.function.Consumer;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class DecoratableContextualProvider<CTX, V> implements ContextualProvider<CTX, V> {

    @NonNull ContextualProvider<CTX, V> delegate;
    @NonNull @NonFinal Consumer<CTX> preHook = department -> {};
    @NonNull @NonFinal BiConsumer<CTX, V> postHook = (department, v) -> {};

    public DecoratableContextualProvider<CTX, V> withPreHook(Consumer<CTX> preHook) {
        this.preHook = preHook;
        return this;
    }

    public DecoratableContextualProvider<CTX, V> withPostHook(BiConsumer<CTX, V> postHook) {
        this.postHook = postHook;
        return this;
    }

    @Override
    public V getOn(CTX department) throws ItemNotFoundException {
        preHook.accept(department);
        V val = delegate.getOn(department);
        postHook.accept(department, val);
        return val;
    }
}

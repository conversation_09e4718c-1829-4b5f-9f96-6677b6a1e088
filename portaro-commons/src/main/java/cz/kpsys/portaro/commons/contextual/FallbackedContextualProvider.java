package cz.kpsys.portaro.commons.contextual;

import cz.kpsys.portaro.commons.object.Provider;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class FallbackedContextualProvider<CTX, E> implements ContextualProvider<CTX, @NonNull E> {

    @NonNull ContextualProvider<CTX, E> primary;
    @NonNull ContextualProvider<CTX, @NonNull E> fallback;

    public static <CTX, E> FallbackedContextualProvider<CTX, E> of(@NonNull ContextualProvider<CTX, E> primary, @NonNull ContextualProvider<CTX, @NonNull E> fallback) {
        return new FallbackedContextualProvider<CTX, E>(primary, fallback);
    }

    public static <CTX, E> FallbackedContextualProvider<CTX, E> of(@NonNull ContextualProvider<CTX, E> primary, @NonNull Provider<@NonNull E> fallback) {
        return of(primary, ContextIgnoringContextualProvider.of(fallback));
    }

    @NonNull
    @Override
    public E getOn(CTX ctx) {
        E primaryValue = primary.getOn(ctx);

        if (primaryValue != null) {
            return primaryValue;
        }

        return fallback.getOn(ctx);
    }
}

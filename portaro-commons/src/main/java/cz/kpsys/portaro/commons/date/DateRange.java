package cz.kpsys.portaro.commons.date;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.commons.util.DateUtils;
import lombok.NonNull;
import lombok.With;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

@JsonIgnoreProperties
public class DateRange {

    @JsonProperty("fromDate")
    @With
    private final Instant inclusiveFromDate;

    @JsonProperty("toDate")
    @With
    private final Instant exclusiveToDate;

    @JsonCreator
    public DateRange(@JsonProperty("fromDate") Instant inclusiveFromDate,
                     @JsonProperty("toDate") Instant exclusiveToDate) {
        this.inclusiveFromDate = inclusiveFromDate;
        this.exclusiveToDate = exclusiveToDate;
        if (this.inclusiveFromDate != null && this.exclusiveToDate != null && this.inclusiveFromDate.isAfter(this.exclusiveToDate)) {
            throw new IllegalStateException("From date cannot be greather than to date");
        }
    }

    public DateRange(@NonNull LocalDateTime inclusiveFromDate,
                     @NonNull LocalDateTime exclusiveToDate,
                     @NonNull ZoneId zoneId) {
        this(inclusiveFromDate.atZone(zoneId).toInstant(), exclusiveToDate.atZone(zoneId).toInstant());
    }

    public DateRange(@NonNull ZonedDateTime inclusiveFromDate,
                     @NonNull ZonedDateTime exclusiveToDate) {
        this(inclusiveFromDate.toInstant(), exclusiveToDate.toInstant());
    }

    public DateRange() {
        this((Instant) null, null);
    }

    public static DateRange ofDay(@NonNull LocalDate date, @NonNull ZoneId timeZoneId) {
        return ofStartOfDays(date, date.plusDays(1), timeZoneId);
    }

    public static DateRange ofStartOfDays(LocalDate inclusiveFromDate, LocalDate exclusiveToDate, ZoneId zoneId) {
        Instant inclusiveFromDateAtStartOfDay = inclusiveFromDate.atStartOfDay(zoneId).toInstant();
        Instant exclusiveToDateAtStartOfDay = exclusiveToDate.atStartOfDay(zoneId).toInstant();
        return new DateRange(inclusiveFromDateAtStartOfDay, exclusiveToDateAtStartOfDay);
    }

    public Instant fromDate() {
        return inclusiveFromDate;
    }

    public Instant toDate() {
        return exclusiveToDate;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 43 * hash + (this.inclusiveFromDate != null ? this.inclusiveFromDate.hashCode() : 0);
        hash = 43 * hash + (this.exclusiveToDate != null ? this.exclusiveToDate.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DateRange dateRange)) {
            return false;
        }
        return Objects.equals(inclusiveFromDate, dateRange.inclusiveFromDate) && Objects.equals(exclusiveToDate, dateRange.exclusiveToDate);
    }

    public static DateRange ofMonth(YearMonth yearMonth, ZoneId timeZone) {
        ZonedDateTime zonedFromDate = yearMonth.atDay(1).atStartOfDay(timeZone);
        ZonedDateTime zonedToDate = zonedFromDate.plusMonths(1);
        return new DateRange(zonedFromDate, zonedToDate);
    }

    public static DateRange ofMonth(Instant fromDate, boolean truncateDate, ZoneId timeZone) {
        ZonedDateTime zonedFromDate = fromDate.atZone(timeZone);
        if (truncateDate) {
            zonedFromDate = zonedFromDate.with(TemporalAdjusters.firstDayOfMonth()).truncatedTo(ChronoUnit.DAYS);
        }
        ZonedDateTime zonedToDate = zonedFromDate.plusMonths(1);
        return new DateRange(zonedFromDate, zonedToDate);
    }

    public static DateRange ofYear(Instant fromDate, boolean truncateDate, ZoneId timeZone) {
        ZonedDateTime zonedFromDate = fromDate.atZone(timeZone);
        if (truncateDate) {
            zonedFromDate = zonedFromDate.with(TemporalAdjusters.firstDayOfYear()).truncatedTo(ChronoUnit.DAYS);
        }
        ZonedDateTime zonedToDate = zonedFromDate.plusYears(1);
        return new DateRange(zonedFromDate, zonedToDate);
    }

    public LocalTimeRange toLocalTimeRange(ZoneId timeZone) {
        Objects.requireNonNull(inclusiveFromDate, "Cannot convert date range to local time range, because inclusiveFromDate is null");
        Objects.requireNonNull(exclusiveToDate, "Cannot convert date range to local time range, because exclusiveToDate is null");
        LocalTime fromLocalTime = inclusiveFromDate.atZone(timeZone).toLocalTime();
        LocalTime toLocalTime = exclusiveToDate.atZone(timeZone).toLocalTime();
        return new LocalTimeRange(fromLocalTime, toLocalTime);
    }

    @Override
    public String toString() {
        return "[" + inclusiveFromDate + " - " + exclusiveToDate + ")";
    }

    public boolean touches(DateRange other) {
        return (inclusiveFromDate == null || other.exclusiveToDate == null || inclusiveFromDate.isBefore(other.exclusiveToDate)) &&
               (exclusiveToDate == null || other.inclusiveFromDate == null || exclusiveToDate.isAfter(other.inclusiveFromDate));
    }

    public Stream<LocalDate> streamLocalDates(@NonNull ZoneId zoneId) {
        LocalDate from = DateUtils.instantToLocalDate(inclusiveFromDate, zoneId);
        LocalDate exclusiveTo = DateUtils.instantToLocalDate(exclusiveToDate, zoneId);
        return from.datesUntil(exclusiveTo);
    }

    public YearMonth getMonth(@NonNull ZoneId zoneId) throws IllegalStateException {
        Optional<YearMonth> yearMonthOpt = DateUtils.getIfExactlyStartOfMonth(inclusiveFromDate, zoneId);
        if (yearMonthOpt.isEmpty()) {
            throw new IllegalStateException("inclusiveFromDate " + inclusiveFromDate + " is not start of month");
        }
        YearMonth yearMonth = yearMonthOpt.get();

        // check if exclusiveToDate is exactly same as if we add month to yearMonth
        Instant expectedExclusiveToDate = yearMonth.plusMonths(1).atDay(1).atStartOfDay(zoneId).toInstant();
        if (!expectedExclusiveToDate.equals(exclusiveToDate)) { // also supports null in exclusiveToDate
            throw new IllegalStateException("exclusiveToDate " + exclusiveToDate + " is not exactly same as if we add month to inclusiveFromDate " + inclusiveFromDate);
        }

        return yearMonth;
    }
}

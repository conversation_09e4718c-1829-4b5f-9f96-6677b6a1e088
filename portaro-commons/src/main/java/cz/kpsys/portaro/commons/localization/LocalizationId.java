package cz.kpsys.portaro.commons.localization;

import lombok.Getter;
import lombok.NonNull;

public record LocalizationId(@Getter @NonNull String code,
                             @Getter @NonNull Object contextId) {

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        LocalizationId localizationId = (LocalizationId) o;

        if (!code.equals(localizationId.code)) {
            return false;
        }
        return contextId.equals(localizationId.contextId);
    }

}

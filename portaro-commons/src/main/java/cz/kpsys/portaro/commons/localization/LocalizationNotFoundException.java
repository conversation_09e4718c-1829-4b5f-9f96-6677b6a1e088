package cz.kpsys.portaro.commons.localization;

import java.util.Locale;

/**
 * Created by <PERSON> on 24.12.2017.
 */
public class LocalizationNotFoundException extends RuntimeException {

    /**
     * Create a new exception.
     * @param locale locale that was used to search message
     */
    public LocalizationNotFoundException(String message) {
        super(message);
    }

    /**
     * Create a new exception.
     * @param code code that could not be resolved for given locale
     * @param locale locale that was used to search for the code within
     */
    public LocalizationNotFoundException(String code, Locale locale) {
        super("No message found under code '" + code + "' for locale '" + locale + "'.");
    }

}

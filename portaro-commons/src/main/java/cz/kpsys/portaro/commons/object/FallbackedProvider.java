package cz.kpsys.portaro.commons.object;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.util.HashSet;
import java.util.Set;
import java.util.function.Predicate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FallbackedProvider<E> implements Provider<E> {

    @NonNull Provider<E> primary;
    @NonNull Provider<E> fallback;
    @NonFinal boolean fallbackOnNull = false;
    @NonNull Set<Class<? extends Exception>> fallbackOnAnyOfExceptions = new HashSet<>(2);
    @NonFinal Predicate<@NonNull E> toDoFallbackResolver = null;

    public static <E> FallbackedProvider<E> ofStaticFallback(Provider<E> primary, E fallback) {
        return new FallbackedProvider<E>(primary, StaticProvider.of(fallback));
    }

    public FallbackedProvider<E> fallbackOnNull() {
        this.fallbackOnNull = true;
        return this;
    }

    public FallbackedProvider<E> fallbackOnValue(@NonNull Predicate<@NonNull E> toDoFallbackResolver) {
        this.toDoFallbackResolver = toDoFallbackResolver;
        return this;
    }

    public FallbackedProvider<E> fallbackOnException() {
        return fallbackOnException(Exception.class);
    }

    public FallbackedProvider<E> fallbackOnException(@NonNull Class<? extends Exception> fallbackOnException) {
        this.fallbackOnAnyOfExceptions.add(fallbackOnException);
        return this;
    }

    @Override
    public E get() {
        E primaryValue;
        try {
            primaryValue = primary.get();
        } catch (Exception e) {
            if (fallbackOnAnyOfExceptions.stream().anyMatch(fallbackException -> fallbackException.isInstance(e))) {
                return fallback.get();
            }
            throw e;
        }

        if (primaryValue == null) {
            if (fallbackOnNull) {
                return fallback.get();
            }
            return null;
        }

        if (toDoFallbackResolver != null && toDoFallbackResolver.test(primaryValue)) {
            return fallback.get();
        }

        return primaryValue;
    }
}

package cz.kpsys.portaro.commons.object;

import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.function.Predicate;

/**
 * Proxovany list, ktery vzdy nejdrive filtruje pres objectFilter.
 * <AUTHOR>
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FilteredList<E> extends ProxiedList<E> {

    @NonNull List<E> listOfAll;
    @NonNull Predicate<E> objectFilter;

    @Override
    protected List<E> getList() {
        return ListUtil.filter(listOfAll, objectFilter);
    }
    
}

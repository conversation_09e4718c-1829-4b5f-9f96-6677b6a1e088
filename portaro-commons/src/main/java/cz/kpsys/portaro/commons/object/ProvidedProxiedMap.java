package cz.kpsys.portaro.commons.object;

import lombok.NonNull;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * Proxy mapa, ktera prijima pouze provider.
 *
 * @param <K>
 * @param <V>
 * <AUTHOR>
 */
public class ProvidedProxiedMap<K, V> implements Map<K, V> {

    /**
     * The map to decorate
     */
    protected Provider<Map<K, V>> mapProvider;

    /**
     * Constructor that wraps (not copies).
     *
     * @param mapProvider the map to decorate, must not be null
     * @throws IllegalArgumentException if the collection is null
     */
    public ProvidedProxiedMap(Provider<Map<K, V>> mapProvider) {
        if (mapProvider == null) {
            throw new IllegalArgumentException("Map provider must not be null");
        }
        this.mapProvider = mapProvider;
    }

    /**
     * Gets the map being decorated.
     *
     * @return the decorated map
     */
    protected Map<K, V> getMap() {
        return mapProvider.get();
    }

    @Override
    public void clear() {
        getMap().clear();
    }

    @Override
    public boolean containsKey(Object key) {
        return getMap().containsKey(key);
    }

    @Override
    public boolean containsValue(Object value) {
        return getMap().containsValue(value);
    }

    @Override
    public Set<Entry<K, V>> entrySet() {
        return getMap().entrySet();
    }

    @Override
    public V get(Object key) {
        return getMap().get(key);
    }

    @Override
    public boolean isEmpty() {
        return getMap().isEmpty();
    }

    @Override
    public Set<K> keySet() {
        return getMap().keySet();
    }

    @Override
    public V put(K key, V value) {
        return getMap().put(key, value);
    }

    @Override
    public void putAll(@NonNull Map<? extends K, ? extends V> mapToCopy) {
        getMap().putAll(mapToCopy);
    }

    @Override
    public V remove(Object key) {
        return getMap().remove(key);
    }

    @Override
    public int size() {
        return getMap().size();
    }

    @Override
    public Collection<V> values() {
        return getMap().values();
    }

    @Override
    public boolean equals(Object object) {
        if (object == getMap()) {
            return true;
        }
        return getMap().equals(object);
    }

    @Override
    public int hashCode() {
        return getMap().hashCode();
    }

    @Override
    public String toString() {
        return getMap().toString();
    }

}

package cz.kpsys.portaro.commons.object;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.dao.EmptyResultDataAccessException;

import java.util.Optional;
import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class ProviderByIdProvider<E, ID> implements Provider<E> {

    @NonNull ByIdLoadable<E, ID> itemByIdLoader;
    @NonNull Provider<ID> idProvider;
    @NonFinal Provider<E> defaultItemProvider;
    @NonFinal Set<ID> idsForFallbackingToDefault;
    @NonFinal boolean returnNullIfNotFoundException;

    public static <E, ID> ProviderByIdProvider<E, ID> ofStaticId(ByIdLoadable<E, ID> itemByIdLoader, ID id) {
        return of(itemByIdLoader, StaticProvider.of(id));
    }

    public static <E, ID> ProviderByIdProvider<E, ID> of(ByIdLoadable<E, ID> itemByIdLoader, Provider<ID> idProvider) {
        return new ProviderByIdProvider<E, ID>(itemByIdLoader, idProvider);
    }

    public ProviderByIdProvider<E, ID> withDefaultSupport(Provider<E> defaultItemProvider, ID...idsForFallbackingToDefault) {
        this.defaultItemProvider = defaultItemProvider;
        this.idsForFallbackingToDefault = Set.of(idsForFallbackingToDefault);
        return this;
    }

    public ProviderByIdProvider<E, ID> returnNullIfNotFoundException() {
        this.returnNullIfNotFoundException = true;
        return this;
    }

    @Override
    public Provider<Optional<E>> optionally() {
        return () -> {
            if (returnNullIfNotFoundException) {
                return Optional.ofNullable(get());
            }
            try {
                return Optional.of(get());
            } catch (EmptyResultDataAccessException | ItemNotFoundException e) {
                return Optional.empty();
            }
        };
    }

    @Override
    public E get() {
        ID itemId = idProvider.get();
        if (defaultItemProvider != null && idsForFallbackingToDefault.contains(itemId)) {
            return defaultItemProvider.get();
        }
        try {
            return itemByIdLoader.getById(itemId);
        } catch (EmptyResultDataAccessException | ItemNotFoundException e) {
            if (returnNullIfNotFoundException) {
                return null;
            }
            throw e;
        }
    }
}

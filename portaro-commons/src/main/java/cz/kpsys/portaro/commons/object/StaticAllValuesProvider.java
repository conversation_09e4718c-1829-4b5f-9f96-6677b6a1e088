package cz.kpsys.portaro.commons.object;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Arrays;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class StaticAllValuesProvider<E> implements AllValuesProvider<E> {

    @NonNull List<E> values;

    public static <E> AllValuesProvider<E> of(List<E> values) {
        return new StaticAllValuesProvider<>(values);
    }

    @SafeVarargs
    public static <E> AllValuesProvider<E> of(E...values) {
        return new StaticAllValuesProvider<>(Arrays.asList(values));
    }

    @Override
    public List<E> getAll() {
        return values;
    }
}

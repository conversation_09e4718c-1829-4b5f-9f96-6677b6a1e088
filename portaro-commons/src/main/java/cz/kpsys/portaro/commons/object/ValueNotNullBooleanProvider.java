package cz.kpsys.portaro.commons.object;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ValueNotNullBooleanProvider implements Provider<@NonNull Boolean> {

    @NonNull Provider<?> valueProvider;

    @Override
    public Boolean get() {
        return valueProvider.get() != null;
    }

}

package cz.kpsys.portaro.commons.object.repo;

import cz.kpsys.portaro.commons.localization.Texts;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CastingByIdLoader<E, ID, T> implements ByIdAndByIdOptLoadable<T, ID> {

    @NonNull ByIdLoadable<E, ID> delegate;
    @NonNull Class<T> requiredTargetType;

    @Override
    public T getById(@NonNull ID id) throws ItemNotFoundException {
        E loaded = delegate.getById(id);
        if (!requiredTargetType.isInstance(loaded)) {
            throw new ItemNotFoundException(requiredTargetType.getSimpleName(), id, "Object found by id %s, but is not of required type %s (%s)".formatted(id, requiredTargetType.getSimpleName(), loaded), Texts.ofNative("Object found by id %s, but is not of required type %s".formatted(id, requiredTargetType.getSimpleName())));
        }
        return requiredTargetType.cast(loaded);
    }

    @Override
    public Optional<T> findById(@NonNull ID id) {
        E loaded;
        try {
            loaded = delegate.getById(id);
        } catch (ItemNotFoundException e) {
            return Optional.empty();
        }

        if (!requiredTargetType.isInstance(loaded)) {
            return Optional.empty();
        }
        T castedLoaded = requiredTargetType.cast(loaded);
        return Optional.of(castedLoaded);
    }
}

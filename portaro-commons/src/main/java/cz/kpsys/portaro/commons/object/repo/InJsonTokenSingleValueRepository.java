package cz.kpsys.portaro.commons.object.repo;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class InJsonTokenSingleValueRepository<E> implements SingleValueRepository<E, String> {

    @NonNull ObjectMapper objectMapper;
    @NonNull TypeReference<E> valueType;

    public static <E> InJsonTokenSingleValueRepository<E> ofDefaultObjectMapper(@NonNull TypeReference<E> valueType) {
        ObjectMapper objectMapper = new ObjectMapper()
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .disable(SerializationFeature.WRITE_DURATIONS_AS_TIMESTAMPS)
                .registerModule(new JavaTimeModule());
        return new InJsonTokenSingleValueRepository<>(objectMapper, valueType);
    }

    @SneakyThrows
    @Override
    public String store(E params) {
        return objectMapper.writeValueAsString(params);
    }

    @SneakyThrows
    @Override
    public Optional<E> restore(String resumptionToken) {
        return Optional.of(objectMapper.readValue(resumptionToken, valueType));
    }

}

package cz.kpsys.portaro.commons.template.spel;

import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

public class SpElTemplateEngine {

    public String evaluate(String expressionString, Object model) {
        ExpressionParser expressionParser = new SpelExpressionParser();
        Expression expression = expressionParser.parseExpression(expressionString, new TemplateParserContext());
        EvaluationContext evaluationContext = new StandardEvaluationContext(model);
        return expression.getValue(evaluationContext, String.class);
    }

}

package cz.kpsys.portaro.commons.validation.contains;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.TYPE_USE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({FIELD, TYPE_USE})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = ContainsAllValidator.class)
public @interface ContainsAll {

    String message() default "{jakarta.validation.constraints.ContainsAll.message}";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };

    /**
     * Name of bean, which resolves list of acceptable values. <br/>
     * Must be instance of @code AllValuesProvider
     */
    String valuesSourceBean();

}

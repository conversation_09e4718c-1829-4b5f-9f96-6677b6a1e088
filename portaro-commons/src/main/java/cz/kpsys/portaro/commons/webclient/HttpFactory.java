package cz.kpsys.portaro.commons.webclient;

import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.web.HttpHeaderConstants.AcceptEncoding;
import cz.kpsys.portaro.commons.web.HttpHeaderConstants.UserAgent;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.auth.AuthScope;
import org.apache.hc.client5.http.auth.CredentialsProvider;
import org.apache.hc.client5.http.auth.NTCredentials;
import org.apache.hc.client5.http.config.ConnectionConfig;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.auth.CredentialsProviderBuilder;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.client5.http.io.HttpClientConnectionManager;
import org.apache.hc.client5.http.ssl.NoopHostnameVerifier;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactoryBuilder;
import org.apache.hc.client5.http.ssl.TrustAllStrategy;
import org.apache.hc.client5.http.utils.URIUtils;
import org.apache.hc.core5.http.io.SocketConfig;
import org.apache.hc.core5.http.message.BasicHeader;
import org.apache.hc.core5.pool.PoolConcurrencyPolicy;
import org.apache.hc.core5.pool.PoolReusePolicy;
import org.apache.hc.core5.ssl.SSLContextBuilder;
import org.apache.hc.core5.util.Timeout;
import org.springframework.lang.Nullable;

import java.net.URI;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

import static java.util.Objects.requireNonNull;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class HttpFactory {

    @NonNull String userAgent;
    @Nullable @NonFinal String httpProxyUrl;
    @NonFinal boolean httpProxyAuth;
    @Nullable @NonFinal String httpProxyUsername;
    @Nullable @NonFinal String httpProxyPassword;
    @NonNull @NonFinal List<String> excludedProxyUrls = List.of();

    public HttpFactory withProxy(@NonNull String httpProxyUrl,
                                 boolean httpProxyAuth,
                                 @Nullable String httpProxyUsername,
                                 @Nullable String httpProxyPassword,
                                 @NonNull List<String> excludedProxyUrls) {
        this.httpProxyUrl = httpProxyUrl;
        this.httpProxyAuth = httpProxyAuth;
        this.httpProxyUsername = httpProxyUsername;
        this.httpProxyPassword = httpProxyPassword;
        this.excludedProxyUrls = excludedProxyUrls;
        return this;
    }


    private void addProxy(HttpClientBuilder builder) {
        log.info("Enabling http proxy {}{}", httpProxyUrl, httpProxyAuth ? " with authorization" : "");
        builder.setRoutePlanner(new ExcludingProxyRoutePlanner(httpProxyUrl, excludedProxyUrls));
        if (httpProxyAuth) {
            CredentialsProvider credsProvider = CredentialsProviderBuilder.create()
                            .add(new AuthScope(URIUtils.extractHost(URI.create(requireNonNull(httpProxyUrl)))), new NTCredentials(requireNonNull(httpProxyUsername), requireNonNull(httpProxyPassword).toCharArray(), "", ""))
                            .build();
            builder.setDefaultCredentialsProvider(credsProvider);
        }
    }

    public PoolingHttpClientConnectionManager newDefaultConnectionManager() {
        try {
            return PoolingHttpClientConnectionManagerBuilder.create()
                    .setMaxConnTotal(50)
                    .setMaxConnPerRoute(30)
                    .setDefaultConnectionConfig(ConnectionConfig.custom()
                            .setConnectTimeout(Timeout.ofSeconds(10)) //10s the time to establish the connection with the remote host
                            .setSocketTimeout(Timeout.ofSeconds(20)) //20s the time waiting for data – after the connection was established; maximum time of inactivity between two data packets; this is the time of inactivity to wait for packets to arrive
                            .build())
                    .setSSLSocketFactory(SSLConnectionSocketFactoryBuilder.create()
                            .setSslContext(SSLContextBuilder.create().loadTrustMaterial(TrustAllStrategy.INSTANCE).build())
                            //.setTlsVersions(TLS.V_1_3, TLS.V_1_2)
                            .setHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                            .build())
                    .setDefaultSocketConfig(SocketConfig.custom()
                            .setSoTimeout(Timeout.ofSeconds(20))
                            .build())
                    .setPoolConcurrencyPolicy(PoolConcurrencyPolicy.STRICT)
                    .setConnPoolPolicy(PoolReusePolicy.LIFO)
                    .build();
        } catch (KeyStoreException | NoSuchAlgorithmException | KeyManagementException e) {
            throw new RuntimeException("Cannot create http connection manager", e);
        }
    }

    public HttpClientBuilder httpClientBuilder() {
        return httpClientBuilder(newDefaultConnectionManager());
    }

    public HttpClientBuilder httpClientBuilder(@NonNull HttpClientConnectionManager clientConnectionManager) {
        HttpClientBuilder builder = HttpClients.custom()
                .setConnectionManager(clientConnectionManager)
                .setDefaultRequestConfig(RequestConfig.custom()
                        .setConnectionRequestTimeout(Timeout.ofSeconds(5)) //5s the time to wait for a connection from the connection manager/pool - important in high load scenarios
                        .build())
                .setDefaultHeaders(List.of(
                        new BasicHeader(AcceptEncoding.NAME, AcceptEncoding.Value.ALL),
                        new BasicHeader(UserAgent.NAME, userAgent)));

        if (StringUtil.hasLength(httpProxyUrl)) {
            addProxy(builder);
        }

        return builder;
    }

}

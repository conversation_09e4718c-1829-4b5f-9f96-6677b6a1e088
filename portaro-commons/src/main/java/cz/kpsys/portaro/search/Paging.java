package cz.kpsys.portaro.search;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import cz.kpsys.portaro.commons.object.Range;

import java.util.LinkedHashMap;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.PROPERTY,
        property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(value = KeysetPaging.class, name = "keyset"),
        @JsonSubTypes.Type(value = RangePaging.class, name = "range")
})
public sealed interface Paging permits KeysetPaging, RangePaging {

    int FIRST_PAGE_NUMBER = 1;

    int pageSize();

    int pageNumber();

    static RangePaging ofRange(Range range) {
        return new RangePaging(range);
    }

    static KeysetPaging ofKeySet(int pageSize, int pageNumber, LinkedHashMap<String, Object> lastValues) {
        return new KeysetPaging(pageSize, pageNumber, lastValues);
    }
}

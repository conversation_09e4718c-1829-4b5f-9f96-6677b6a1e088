package cz.kpsys.portaro.time;

import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.util.TimeMeter;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public final class LoadingTask {

    @NonNull String description;
    @NonNull TimeMeter timeMeter;
    int nestingLevel;
    @Nullable LoadingTask parent;
    @NonNull List<LoadingTask> subtasks = new ArrayList<>();
    @Nullable @NonFinal Exception exception = null;
    @Nullable @NonFinal Duration duration = null;

    public static LoadingTask start(@NonNull String description) {
        return new LoadingTask(description, TimeMeter.start(), 0, null);
    }

    public LoadingTask startNested(@NonNull String description) {
        LoadingTask task = new LoadingTask(description, TimeMeter.start(), nestingLevel + 1, this);
        subtasks.add(task);
        return task;
    }

    public void finish() {
        duration = timeMeter.elapsedDuration();
    }

    public void finishWithException(@NonNull Exception e) {
        duration = timeMeter.elapsedDuration();
        exception = e;
    }

    @Nullable
    public LoadingTask parent() {
        return parent;
    }

    public @NonNull String description() {
        return description;
    }

    public @NonNull Duration duration() {
        return Objects.requireNonNull(duration, "Report item has not been stopped");
    }

    public String print(@NonNull Duration minDurationToPrint) {
        if (duration().minus(minDurationToPrint).isNegative()) {
            return null;
        }
        String indent = " |  ".repeat(nestingLevel);
        double millis = duration().toNanos() / (double) TimeUnit.MILLISECONDS.toNanos(1);
        String trimmedDescription = StringUtil.limitCharsAndTrimWithEllipsis(description(), 400, true);
        String exceptionPart = exception == null ? "" : " - finished with exception: " + exception.getMessage();
        StringBuilder currentLevelPrint = new StringBuilder("\n%s %.3fms %s%s".formatted(indent, millis, trimmedDescription, exceptionPart));

        for (LoadingTask nested : subtasks) {
            Optional.ofNullable(nested.print(minDurationToPrint)).ifPresent(currentLevelPrint::append);
        }

        return currentLevelPrint.toString();
    }
}

package cz.kpsys.portaro.commons.cache;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.List;

@Tag("ci")
@Tag("unit")
public class StaticCacheFullImplTest {

    @Test
    public void shouldPreserveSorting() {
        final LabeledId<Integer> obj55 = new LabeledId<>(55, Texts.ofNative("Nevim"));
        final LabeledId<Integer> obj22 = new LabeledId<>(22, Texts.ofNative("Brambora"));
        final LabeledId<Integer> obj36 = new LabeledId<>(36, Texts.ofNative("Ezop"));
        StaticCacheFullImpl<LabeledId<Integer>> cache = new StaticCacheFullImpl<>(() -> List.of(obj55, obj22, obj36));
        List<LabeledId<Integer>> result = cache.getAll();
        Assertions.assertArrayEquals(new Object[] {obj55, obj22, obj36}, result.toArray());
    }
    
    
    @Test
    public void shouldGetById() {
        final LabeledId<Integer> obj55 = new LabeledId<>(55, Texts.ofNative("Nevim"));
        final LabeledId<Integer> obj22 = new LabeledId<>(22, Texts.ofNative("Brambora"));
        final LabeledId<Integer> obj36 = new LabeledId<>(36, Texts.ofNative("Ezop"));
        StaticCacheFullImpl<LabeledId<Integer>> cache = new StaticCacheFullImpl<>(() -> List.of(obj55, obj22, obj36));
        LabeledId<Integer> result = cache.findById(36).orElseThrow();
        Assertions.assertEquals(obj36, result);
    }

}
package cz.kpsys.portaro.commons.email;

import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Tag("ci")
@Tag("unit")
class EmailValidatorTest {

    @ParameterizedTest
    @ValueSource(strings = {
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "name@***************",
            "<EMAIL>",
            "\"verylongname\"@domain.com"
    })
    void validEmailAddresses(String email) {
        Function<String, Boolean> validator = new EmailValidator();
        assertTrue(validator.apply(email));
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "test.io.com",
            "test@<EMAIL>",
            "test(io\"epam)example]com",
            "test\"io\"epam.com",
            "@domain.com",
            "<EMAIL>"
    })
    void notValidEmailAddresses(String email) {
        Function<String, Boolean> validator = new EmailValidator();
        assertFalse(validator.apply(email));
    }
}
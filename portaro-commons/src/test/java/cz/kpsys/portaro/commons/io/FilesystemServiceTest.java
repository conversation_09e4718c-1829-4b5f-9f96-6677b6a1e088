package cz.kpsys.portaro.commons.io;

import cz.kpsys.portaro.commons.licence.FeatureNotEnabledException;
import cz.kpsys.portaro.commons.object.ConvertingCachingProvider;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@Tag("ci")
@Tag("unit")
class FilesystemServiceTest {

    @JimfsTempDir
    static Path sharedTempBlobDir;

    void successScenario(Path blobDir) throws IOException {
        FilesystemService.BlobDir bd = new FilesystemService.BlobDir(blobDir);
        final FilesystemService service = new FilesystemService(
                StaticProvider.of(bd), StaticProvider.of(true)
        );

        assertThrows(ItemNotFoundException.class, () -> {
            service.getValidatedFile("nonexistent-file");
        });

        // Save
        byte[] data = new byte[] {1, 2, 3};
        ByteArrayInputStream in = new ByteArrayInputStream(data);
        String storedPath = service.save(in, "1d50e5ff-6321-4a3f-9bd7-4ccf19da1343").getRelativeFilePath();
        assertEquals("data/1d/50/e5/1d50e5ff-6321-4a3f-9bd7-4ccf19da1343", storedPath);

        // Disallowed overwrite
        assertThrows(FileAlreadyExistsException.class, () -> {
            service.save(new ByteArrayInputStream(data), "1d50e5ff-6321-4a3f-9bd7-4ccf19da1343");
        });

        // Retrieve
        String relFilePath = "data\\1d\\50\\e5\\1d50e5ff-6321-4a3f-9bd7-4ccf19da1343";
        var file = service.getValidatedFile(relFilePath);

        assertTrue(Files.isRegularFile(file.path));
        assertEquals(data.length, file.size());

        // Load
        try (InputStream is = file.getInputStream(null)) {
            byte[] read = is.readAllBytes();
            assertArrayEquals(data, read);
        }

        // Regresní test k #21643
        in = new ByteArrayInputStream(data);
        String storedPath2 = service.save(in, "1d50e5ff-6321-4a3f-9bd7-4ccf19da1344").getRelativeFilePath();
        assertEquals("data/1d/50/e5/1d50e5ff-6321-4a3f-9bd7-4ccf19da1344", storedPath2);

        // Delete
        service.deleteBlob(file);
        assertThrows(NoSuchFileException.class, () -> service.deleteBlob(file));
        assertTrue(service.deleteBlobQuietly(file));
        assertFalse(Files.exists(file.path));

        // Modification disabled
        final FilesystemService readOnlyService = new FilesystemService(
                StaticProvider.of(bd), StaticProvider.of(false)
        );
        var file2 = readOnlyService.getFile(storedPath2);
        assertThrows(FeatureNotEnabledException.class, () -> {
            readOnlyService.save(new ByteArrayInputStream(data), "1d50e5ff-6321-4a3f-9bd7-4ccf19da1344");
        });
        assertThrows(FeatureNotEnabledException.class, () -> readOnlyService.deleteBlob(file2));
        assertTrue(Files.exists(file2.path));
        assertTrue(Files.isRegularFile(file2.path));
    }

    @Test
    void test() throws IOException {
        successScenario(sharedTempBlobDir);
    }

    @Test
    void testWinPaths(@JimfsTempDir(filesystemType = JimfsTempDir.FsType.WINDOWS) Path winTempDir) throws IOException {
        successScenario(winTempDir);
    }

    @Test
    void errors() {
        FilesystemService badRelativePathService = new FilesystemService(
                new ConvertingCachingProvider<>(StaticProvider.of("i-dont-exist"),
                        FilesystemService.BlobDir::new), StaticProvider.of(true)
        );
        assertThrows(IllegalStateException.class, () -> {
            ByteArrayInputStream in = new ByteArrayInputStream(new byte[] {1, 2, 3});
            badRelativePathService.save(in, "1d50e5ff-6321-4a3f-9bd7-4ccf19da1343");
        });

        Path root = sharedTempBlobDir.getRoot();
        if (root != null) {
            Path nonexistent = root.resolve("this-dir-must-not-exist");
            if (!Files.exists(nonexistent)) {
                FilesystemService badNonexistentPathService = new FilesystemService(
                        new ConvertingCachingProvider<>(StaticProvider.of(nonexistent),
                                FilesystemService.BlobDir::new), StaticProvider.of(true)
                );
                assertThrows(IllegalStateException.class, () -> {
                    ByteArrayInputStream in = new ByteArrayInputStream(new byte[] {1, 2, 3});
                    badNonexistentPathService.save(in, "1d50e5ff-6321-4a3f-9bd7-4ccf19da1343");
                });
            }
        }
    }

    @Test
    void testTmpfile() throws IOException {
        FilesystemService.BlobDir bd = new FilesystemService.BlobDir(sharedTempBlobDir);
        final FilesystemService service = new FilesystemService(
                StaticProvider.of(bd), StaticProvider.of(true)
        );

        final long fileSize = 123;
        final var uuid = UUID.fromString("1d50e5ff-6321-4a3f-9bd7-4ccf19da1343");
        final String suffix = "_test_123";
        final String data = "test_data";

        {
            var newFile = service.createTmpFile(uuid, fileSize, suffix);
            assertEquals(fileSize, Files.size(newFile.getPath()));
            assertEquals(uuid + suffix, newFile.getFilename());
            assertEquals(uuid.toString(), newFile.getUuidPart());
            assertEquals(suffix, newFile.getSuffixPart());

            try (var writer = Files.newBufferedWriter(newFile.getPath(), StandardCharsets.UTF_8, StandardOpenOption.WRITE)) {
                writer.write(data);
            }
        }

        {
            var foundFile = service.findTmpFile(uuid.toString()).orElseThrow();
            assertEquals(fileSize, Files.size(foundFile.getPath()));
            assertEquals(uuid + suffix, foundFile.getFilename());
            assertEquals(uuid.toString(), foundFile.getUuidPart());
            assertEquals(suffix, foundFile.getSuffixPart());

            try (var reader = Files.newBufferedReader(foundFile.getPath(), StandardCharsets.UTF_8)) {
                String read = reader.readLine();
                assertTrue(read.startsWith(data));
            }
        }

        var notFoundFile = service.findTmpFile("1d50e5ff-6321-4a3f-9bd7-4ccf19da1340");
        assertTrue(notFoundFile.isEmpty());

        // Too short UUID
        assertThrows(IllegalArgumentException.class, () -> service.findTmpFile("1d50e5ff-6321-4a3f-9bd7"));
    }

}
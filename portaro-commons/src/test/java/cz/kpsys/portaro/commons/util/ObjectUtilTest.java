package cz.kpsys.portaro.commons.util;

import cz.kpsys.portaro.id.UuidGenerator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

@Tag("ci")
@Tag("unit")
public class ObjectUtilTest {

    @Test
    public void testGenerateUUIDShortcut() {
        String uuid1 = UuidGenerator.forIdentifierShortcut();
        System.out.println("uuid 1 shortcut " + uuid1);
        Assertions.assertNotNull(uuid1);
        Assertions.assertTrue(uuid1.length() < 30);

        String uuid2 = UuidGenerator.forIdentifierShortcut();
        System.out.println("uuid 2 shortcut " + uuid2);
        Assertions.assertNotNull(uuid2);
        Assertions.assertTrue(uuid2.length() < 30);

        Assertions.assertNotEquals(uuid1, uuid2);
    }
}
package cz.kpsys.portaro.commons.validation;

import cz.kpsys.portaro.commons.validation.phonenumber.PhoneNumber;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.hibernate.validator.messageinterpolation.ParameterMessageInterpolator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

@Tag("ci")
@Tag("unit")
public class PhoneNumberConstraintValidatorTest {

    private Validator validator;

    @BeforeEach
    public void setUp() {
        // we could use Validation.buildDefaultValidatorFactory(), but then we would need jakarta.el dependency, so we configure lightweight ParameterMessageInterpolator
        ValidatorFactory factory = Validation.byDefaultProvider()
                .configure()
                .messageInterpolator(new ParameterMessageInterpolator())
                .buildValidatorFactory();
        try (factory) {
            validator = factory.getValidator();
        }
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "+44 7733 123456",
            "+420 607 877 426",
            "+420 607877426",
            "+420607877426",
            "607877426",
            "607 877 426",
    })
    public void violationsShouldBeEmpty(String phoneNumber) {
        PhoneNumberTestHelper phoneNumberTestHelper = new PhoneNumberTestHelper(phoneNumber);

        Set<ConstraintViolation<PhoneNumberTestHelper>> violations = validator.validate(phoneNumberTestHelper);
        assertTrue(violations.isEmpty());
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "+44 7733 123",
            "+44 9999 123456",
            "+4206078457814",
            "+420 ************",
    })
    public void violationsShouldNotBeEmptyAndMatchMessageTemplate(String phoneNumber) {
        PhoneNumberTestHelper phoneNumberTestHelper = new PhoneNumberTestHelper(phoneNumber);

        Set<ConstraintViolation<PhoneNumberTestHelper>> violations = validator.validate(phoneNumberTestHelper);
        assertFalse(violations.isEmpty());
        assertEquals("{jakarta.validation.constraints.PhoneNumber.message}", violations.stream().findFirst().get().getMessageTemplate());
    }

    public record PhoneNumberTestHelper(
            @PhoneNumber String phoneNumber
    ) {}
}

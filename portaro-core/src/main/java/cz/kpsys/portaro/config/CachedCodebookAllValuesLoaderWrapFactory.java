package cz.kpsys.portaro.config;

import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.cache.StaticCacheFactory;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.repo.CachedCodebook;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Set;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CachedCodebookAllValuesLoaderWrapFactory<ITEM extends Identified<ID>, ID> implements Function<AllValuesProvider<ITEM>, CachedCodebook<ITEM, ID>> {

    @NonNull CacheService cacheService;
    @NonNull StaticCacheFactory staticCacheFactory;
    @NonNull Set<String> clearedCaches;

    @Override
    public CachedCodebook<ITEM, ID> apply(AllValuesProvider<ITEM> allValuesProvider) {
        CachedCodebook<ITEM, ID> cachedCodebook = new CachedCodebook<>(staticCacheFactory.createStaticCache(allValuesProvider));
        clearedCaches.forEach(cacheName -> cacheService.registerCleaner(cacheName, cachedCodebook));
        return cachedCodebook;
    }
}

package cz.kpsys.portaro.config;

import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.cache.StaticCacheFactory;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.repo.CachedCodebook;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CachedCodebookAssembly {

    @NonNull CacheService cacheService;
    @NonNull StaticCacheFactory staticCacheFactory;

    public <ITEM extends Identified<ID>, ID> CachedCodebook<ITEM, ID> createStaticCached(
            @NonNull AllValuesProvider<ITEM> allValuesProvider,
            @NonNull Set<String> clearedCaches) {

        CachedCodebook<ITEM, ID> cachedCodebook = new CachedCodebook<>(staticCacheFactory.createStaticCache(allValuesProvider));
        clearedCaches.forEach(cacheName -> cacheService.registerCleaner(cacheName, cachedCodebook));
        return cachedCodebook;
    }

    public <ITEM extends Identified<ID>, ID> CachedCodebook<ITEM, ID> createStaticCached(
            @NonNull AllValuesProvider<ITEM> allValuesProvider,
            @NonNull String clearedCaches) {

        return createStaticCached(allValuesProvider, Set.of(clearedCaches));
    }

}

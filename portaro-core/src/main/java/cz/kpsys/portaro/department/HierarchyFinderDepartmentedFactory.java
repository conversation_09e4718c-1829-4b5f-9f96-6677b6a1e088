package cz.kpsys.portaro.department;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.hierarchy.HierarchyFinder;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class HierarchyFinderDepartmentedFactory {

    public static HierarchyFinder<Department> ofDepartment(@NonNull ByIdLoadable<Department, Integer> departmentLoader) {
        return new HierarchyFinder<>(currentDepartment -> Optional.ofNullable(currentDepartment.getParentId()).map(departmentLoader::getById));
    }

    public static HierarchyFinder<Integer> ofDepartmentId(@NonNull ByIdLoadable<Department, Integer> departmentLoader) {
        return new HierarchyFinder<>(currentDepId -> Optional.ofNullable(departmentLoader.getById(currentDepId).getParentId()));
    }
}

package cz.kpsys.portaro.event;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.auth.BasicUserAuthentication;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.Department;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class OnlyCreatingEventer implements Eventer {

    @NonNull ObjectMapper objectMapper;

    @Override
    public @NonNull Event save(@NonNull String code, @NonNull BasicUserAuthentication currentAuth, @NonNull Department department, @Nullable Object data) {
        return Event.createNew(
                code,
                null,
                currentAuth.getActiveUser(),
                department,
                serializeData(data),
                null
        );
    }

    @Override
    public @NonNull Event save(@NonNull UUID id, @NonNull String code, @NonNull BasicUserAuthentication currentAuth, @NonNull Department department, @org.springframework.lang.Nullable Object data) {
        return Event.createNew(
                id,
                code,
                null,
                currentAuth.getActiveUser(),
                department,
                serializeData(data),
                null
        );
    }

    @Override
    public @NonNull Event save(@NonNull String code, @NonNull BasicUserAuthentication currentAuth, @NonNull Department department, @Nullable Object data, @Nullable UUID subjectId) {
        return Event.createNew(
                code,
                null,
                currentAuth.getActiveUser(),
                department,
                serializeData(data),
                subjectId
        );
    }

    private String serializeData(@Nullable Object data) {
        if (data == null) {
            return null;
        }
        try {
            return StringUtil.limitCharsAndTrimWithoutEllipsis(objectMapper.writeValueAsString(data), Event.DATA_MAX_LENGTH, true);
        } catch (JsonProcessingException e) {
            log.error("Cannot serialize event data {}", data, e);
            return null;
        }
    }
}

package cz.kpsys.portaro.localization;

import cz.kpsys.portaro.department.Department;
import lombok.NonNull;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.Locale;
import java.util.Map;

public record Localization(

        @NotBlank
        @NonNull
        String code,

        @NonNull
        Department department,

        @NotEmpty
        @NonNull
        Map<Locale, String> translations
) {
}

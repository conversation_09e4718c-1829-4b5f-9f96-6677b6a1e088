package cz.kpsys.portaro.localization;

import cz.kpsys.portaro.CoreConstants.Locales;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.util.LinkedCaseInsensitiveMap;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static cz.kpsys.portaro.databasestructure.LocalizationsDb.LOKALIZACE.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbLocalizationLoader implements AllValuesProvider<Localization>, RowMapper<Localization> {

    public static final String VALUE_COLUMNS_PREFIX_LOWERCASED = "text_";
    public static final Map<String, String> ISO_TO_COLUMN_LANGUAGES_MAP;
    public static final Map<String, String> COLUMN_TO_ISO_LANGUAGE_MAP;

    static {
        ISO_TO_COLUMN_LANGUAGES_MAP = new HashMap<>();
        ISO_TO_COLUMN_LANGUAGES_MAP.put(Locales.CS.getLanguage(), TEXT_CZE);
        ISO_TO_COLUMN_LANGUAGES_MAP.put(Locales.EN.getLanguage(), TEXT_ENG);
        ISO_TO_COLUMN_LANGUAGES_MAP.put(Locales.DE.getLanguage(), TEXT_GER);
        ISO_TO_COLUMN_LANGUAGES_MAP.put(Locales.FR.getLanguage(), TEXT_FRA);
        ISO_TO_COLUMN_LANGUAGES_MAP.put(Locales.SK.getLanguage(), TEXT_SVK);
        ISO_TO_COLUMN_LANGUAGES_MAP.put(Locales.ES.getLanguage(), TEXT_ESP);
        ISO_TO_COLUMN_LANGUAGES_MAP.put(Locales.IT.getLanguage(), TEXT_ITA);
        ISO_TO_COLUMN_LANGUAGES_MAP.put(Locales.HU.getLanguage(), TEXT_HUN);
        ISO_TO_COLUMN_LANGUAGES_MAP.put(Locales.PL.getLanguage(), TEXT_POL);
        ISO_TO_COLUMN_LANGUAGES_MAP.put(Locales.BG.getLanguage(), TEXT_BGR);
        COLUMN_TO_ISO_LANGUAGE_MAP = new LinkedCaseInsensitiveMap<>();
        COLUMN_TO_ISO_LANGUAGE_MAP.put(TEXT_CZE, Locales.CS.getLanguage());
        COLUMN_TO_ISO_LANGUAGE_MAP.put(TEXT_ENG, Locales.EN.getLanguage());
        COLUMN_TO_ISO_LANGUAGE_MAP.put(TEXT_GER, Locales.DE.getLanguage());
        COLUMN_TO_ISO_LANGUAGE_MAP.put(TEXT_FRA, Locales.FR.getLanguage());
        COLUMN_TO_ISO_LANGUAGE_MAP.put(TEXT_SVK, Locales.SK.getLanguage());
        COLUMN_TO_ISO_LANGUAGE_MAP.put(TEXT_ESP, Locales.ES.getLanguage());
        COLUMN_TO_ISO_LANGUAGE_MAP.put(TEXT_ITA, Locales.IT.getLanguage());
        COLUMN_TO_ISO_LANGUAGE_MAP.put(TEXT_HUN, Locales.HU.getLanguage());
        COLUMN_TO_ISO_LANGUAGE_MAP.put(TEXT_POL, Locales.PL.getLanguage());
        COLUMN_TO_ISO_LANGUAGE_MAP.put(TEXT_BGR, Locales.BG.getLanguage());
    }

    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull Converter<String, Locale> stringToLocaleConverter;

    @Override
    public List<Localization> getAll() {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(TABLE);
        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
    }

    @Override
    public Localization mapRow(ResultSet rs, int rowNum) throws SQLException {
        String localizationCode = rs.getString(ID_LOKALIZACE);
        Integer depId = rs.getInt(FK_PUJC);
        Map<Locale, String> translationsByLangs = new HashMap<>(COLUMN_TO_ISO_LANGUAGE_MAP.size());

        ResultSetMetaData rsMetaData = rs.getMetaData();
        int numberOfColumns = rsMetaData.getColumnCount();

        // get the column names; column indexes start from 1
        for (int i = 1; i < numberOfColumns + 1; i++) {
            String columnInRS = rsMetaData.getColumnLabel(i);
            if (columnInRS.toLowerCase().startsWith(VALUE_COLUMNS_PREFIX_LOWERCASED)) {
                String isoLang = COLUMN_TO_ISO_LANGUAGE_MAP.get(columnInRS);
                if (isoLang != null) {
                    Locale isoLangToLocal = stringToLocaleConverter.convert(isoLang);
                    String text = rs.getString(columnInRS);
                    translationsByLangs.put(isoLangToLocal, text);
                }
            }
        }

        Department department = departmentLoader.getById(depId);

        return new Localization(localizationCode, department, translationsByLangs);
    }
}

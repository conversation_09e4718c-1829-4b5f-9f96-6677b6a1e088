package cz.kpsys.portaro.object;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.BasicNamedLabeledIdentified;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AcceptableValueGroupItem extends BasicNamedLabeledIdentified<String> {

    @Getter(onMethod_ = @JsonIgnore)
    @NonNull
    String groupId;

    public AcceptableValueGroupItem(@NonNull String id, @Nullable String name, @NonNull String groupId) {
        super(id, name);
        this.groupId = groupId;
    }

    public static @NonNull AcceptableValueGroupItem createUnknown(@NonNull String id, @NonNull String groupId) {
        return new AcceptableValueGroupItem(id, id, groupId);
    }

    @Override
    public Text getText() {
        return Texts.ofColumnMessageCodedOrNative(getName(), "DEF_VAL", "POPIS", groupId, getId());
    }
}

package cz.kpsys.portaro.prop;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CompositeObjectPropertiesGenerator<OBJ> implements ObjectPropertiesGenerator<OBJ> {

    @NonNull List<ObjectPropertiesGenerator<OBJ>> generators;

    public static <OBJ> CompositeObjectPropertiesGenerator<OBJ> of(@NonNull List<ObjectPropertiesGenerator<OBJ>> generators) {
        return new CompositeObjectPropertiesGenerator<OBJ>(generators);
    }

    public static <OBJ> CompositeObjectPropertiesGenerator<OBJ> emptyModifiable() {
        return of(new ArrayList<>());
    }

    public CompositeObjectPropertiesGenerator<OBJ> add(ObjectPropertiesGenerator<OBJ> generator) {
        this.generators.add(generator);
        return this;
    }

    @NonNull
    @Override
    public ObjectProperties generate(@NonNull OBJ record) {
        Set<ObjectProperty<?>> props = new HashSet<>();

        for (ObjectPropertiesGenerator<OBJ> generator : generators) {
            ObjectProperties partialProps = generator.generate(record);
            for (ObjectProperty<?> prop : partialProps.getProps()) {
                props.removeIf(existingProp -> existingProp.keyEquals(prop));
                props.add(prop);
            }
        }

        return ObjectProperties.of(props);
    }

}

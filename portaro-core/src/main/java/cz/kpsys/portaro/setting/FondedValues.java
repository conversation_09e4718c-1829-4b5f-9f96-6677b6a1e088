package cz.kpsys.portaro.setting;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.lang.Nullable;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FondedValues<V> {

    @NonFinal V noFondValue = null;
    @NonFinal boolean noFondedValueExist = false;
    @NonFinal @NonNull Map<@NonNull Integer, V> fondedValueMap = Map.of(); // Empty map for efficiency (most of ini keys are no-fonded)

    public Optional<V> findForSpecific(@NonNull Integer fondId) {
        return Optional.ofNullable(fondedValueMap.get(fondId));
    }

    public Optional<V> findFor(@NonNull Integer fondId) {
        Optional<V> fondedValue = findForSpecific(fondId);
        if (fondedValue.isPresent()) {
            return fondedValue;
        }
        if (noFondedValueExist) {
            return Optional.ofNullable(noFondValue);
        }
        return Optional.empty();
    }

    public Optional<V> getFor(@NonNull Integer fondId) throws FondedValueNotSetException {
        V fondedValue = fondedValueMap.get(fondId);
        if (fondedValue != null) {
            return Optional.of(fondedValue);
        }
        if (noFondedValueExist) {
            return Optional.ofNullable(noFondValue);
        }
        throw new FondedValueNotSetException(fondId);
    }

    public V getNoFonded() throws NofondedValueNotSetException {
        if (!noFondedValueExist) {
            throw new NofondedValueNotSetException();
        }
        return noFondValue;
    }

    public void set(@Nullable Integer fondId, V value) {
        if (fondId == null) {
            setNoFonded(value);
        } else {
            setFonded(fondId, value);
        }
    }

    public void setFonded(@NonNull Integer fondId, V value) {
        if (fondedValueMap.isEmpty()) {
            fondedValueMap = new HashMap<>(); // if map is empty means that map is unmodifiable Map.of() so we need to create new modifiable map
        }
        fondedValueMap.put(fondId, value);
    }

    public void setNoFonded(V value) {
        noFondValue = value;
        noFondedValueExist = true;
    }

    @Override
    public String toString() {
        return "FondedValues{noFondValue=%s, fondedValues=%s}".formatted(noFondValue, fondedValueMap);
    }
}

package cz.kpsys.portaro.setting;

import cz.kpsys.portaro.commons.object.Provider;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.JdbcOperations;


@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RelogUserProcedureTransactionAuthenticator implements TransactionAuthenticator {

    @NonNull JdbcOperations jdbcTemplate;
    @NonNull Provider<@NonNull Integer> userIdProvider;

    @Override
    public void authenticate() {
        jdbcTemplate.execute("execute procedure relog_user(%s)".formatted(userIdProvider.get()));
    }

}

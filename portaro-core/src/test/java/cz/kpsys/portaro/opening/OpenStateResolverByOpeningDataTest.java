package cz.kpsys.portaro.opening;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.object.StaticProvider;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static cz.kpsys.portaro.opening.OpeningData.OpeningTimeRange;

@Tag("ci")
@Tag("unit")
public class OpenStateResolverByOpeningDataTest {

    public static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("d.M.yyyy HH:mm");
    public static final ZoneId RANGES_ZONE_ID = ZoneId.of("Europe/Prague");
    public static final ZoneId ZONE_ID = RANGES_ZONE_ID;

    private final OpeningData emptyData = new OpeningData(StaticProvider.of(RANGES_ZONE_ID), List.of());
    private final OpeningData unsortedData = new OpeningData(StaticProvider.of(RANGES_ZONE_ID), List.of(
            OpeningTimeRange.of(DayOfWeek.MONDAY, "06:00", "11:00"), OpeningTimeRange.of(DayOfWeek.MONDAY, "13:00", "18:00"),
            OpeningTimeRange.of(DayOfWeek.TUESDAY, "14:30", "15:00"),
            OpeningTimeRange.of(DayOfWeek.FRIDAY, "06:00", "12:00")
    ));

    private Instant instant(String date) {
        if (date == null) {
            return null;
        }
        LocalDateTime parse = LocalDateTime.parse(date, DATETIME_FORMATTER);
        return parse.atZone(ZONE_ID).toInstant();
    }

    private void test(OpeningData unsortedData, String nowDate, boolean expectedOpened, String expectedCurrentPeriodFromDate, String expectedCurrentPeriodToDate) {
        OpenStateResolverByOpeningData opening = new OpenStateResolverByOpeningData(StaticProvider.of(ZONE_ID), StaticProvider.of(CoreConstants.Locales.CS), unsortedData);

        Instant now = instant(nowDate);

        OpeningPeriodState openState = opening.getOpenState(now);
        System.out.println(openState);
        Assertions.assertEquals(OpeningPeriodState.ofKnown(expectedOpened, new DateRange(instant(expectedCurrentPeriodFromDate), instant(expectedCurrentPeriodToDate))), openState);
    }

    @Test
    public void shouldBeClosedOnMondayEvening() {
        test(unsortedData, "22.1.2018 20:39", false, "22.1.2018 18:00", "23.1.2018 14:30");
    }

    @Test
    public void shouldBeOpen() {
        test(unsortedData, "23.1.2018 14:43", true, "23.1.2018 14:30", "23.1.2018 15:00");
    }

    @Test
    public void shouldWorkWithDateBeforeFirstRange() {
        test(unsortedData, "22.1.2018 04:21", false, "22.1.2018 04:21", "22.1.2018 06:00");
    }

    @Test
    public void shouldWorkWithDateAfterLastRange() {
        test(unsortedData, "27.1.2018 16:21", false, "26.1.2018 12:00", "29.1.2018 06:00");
    }

    @Test
    public void shouldWorkWithNoDataSet() {
        OpenStateResolverByOpeningData opening = new OpenStateResolverByOpeningData(StaticProvider.of(ZONE_ID), StaticProvider.of(CoreConstants.Locales.CS), emptyData);
        OpeningPeriodState openState = opening.getOpenState(instant("27.1.2018 16:21"));
        Assertions.assertEquals(OpeningPeriodState.ofUnknown(), openState);
    }
}
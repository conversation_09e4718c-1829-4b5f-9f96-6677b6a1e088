package cz.kpsys.portaro.opening;

import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.object.StaticProvider;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static cz.kpsys.portaro.opening.OpeningData.OpeningTimeRange;

@Tag("ci")
@Tag("unit")
public class OpeningDataTest {

    public static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("d.M.yyyy HH:mm");
    public static final ZoneOffset UTC = ZoneOffset.UTC;

    private Instant instant(String date) {
        LocalDateTime parse = LocalDateTime.parse(date, DATETIME_FORMATTER);
        return parse.toInstant(UTC);
    }

    @Test
    public void shouldSortRanges() {
        OpeningData unsortedData = new OpeningData(StaticProvider.of(UTC), List.of(
                OpeningTimeRange.of(DayOfWeek.MONDAY, "13:00", "18:00"),
                OpeningTimeRange.of(DayOfWeek.TUESDAY, "14:30", "15:00"),
                OpeningTimeRange.of(DayOfWeek.MONDAY, "06:00", "11:00")
        ));
        Instant startOfLastMondayOrToday = instant("22.1.2018 00:00");
        System.out.println(startOfLastMondayOrToday);

        List<DateRange> sortedOpeningPeriods = unsortedData.getSortedOpeningPeriods(startOfLastMondayOrToday.atZone(UTC));
        System.out.println(sortedOpeningPeriods);

        Assertions.assertArrayEquals(sortedOpeningPeriods.toArray(), new DateRange[] {
                new DateRange(instant("22.1.2018 06:00"), instant("22.1.2018 11:00")),
                new DateRange(instant("22.1.2018 13:00"), instant("22.1.2018 18:00")),
                new DateRange(instant("23.1.2018 14:30"), instant("23.1.2018 15:00"))
        });
    }

}
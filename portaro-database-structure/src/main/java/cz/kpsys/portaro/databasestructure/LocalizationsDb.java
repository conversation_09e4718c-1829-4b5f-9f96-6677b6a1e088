package cz.kpsys.portaro.databasestructure;

@SuppressWarnings("TypeName")
public class LocalizationsDb {

    public static class LOKALIZACE {
        public static final String TABLE = "lokalizace";
        public static final String ID_LOKALIZACE = "id_lokalizace";
        public static final String FK_PUJC = "fk_pujc";
        public static final String TEXT_CZE = "text_cze";
        public static final String TEXT_ENG = "text_eng";
        public static final String TEXT_GER = "text_ger";
        public static final String TEXT_FRA = "text_fra";
        public static final String TEXT_SVK = "text_svk";
        public static final String TEXT_ESP = "text_spa";
        public static final String TEXT_ITA = "text_ita";
        public static final String TEXT_HUN = "text_hun";
        public static final String TEXT_POL = "text_pol";
        public static final String TEXT_BGR = "text_bgr";
        public static final String JE_ZMENA = "je_zmena";
        public static final String DATCAS = "datcas";
    }

}

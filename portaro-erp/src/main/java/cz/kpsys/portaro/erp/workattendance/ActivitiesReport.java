package cz.kpsys.portaro.erp.workattendance;

import cz.kpsys.portaro.erp.workattendance.web.DayEnrichedActivities;
import lombok.NonNull;

import java.util.List;

public record ActivitiesReport(

        @NonNull ActivitiesReportHeader header,

        @NonNull ActivitiesSummary summary,

        @NonNull DayEnrichedActivities enrichedActivities

) {

    public static @NonNull ActivitiesReport compute(@NonNull List<Activity> activities, @NonNull EnrichedLocalDates days, @NonNull WorkCommitment workCommitment) {
        var enrichedActivities = DayEnrichedActivities.fromActivities(activities, days);
        var header = ActivitiesReportHeader.compute(enrichedActivities, days, workCommitment);
        return new ActivitiesReport(
                header,
                ActivitiesSummary.summarize(header, enrichedActivities),
                enrichedActivities
        );
    }

}

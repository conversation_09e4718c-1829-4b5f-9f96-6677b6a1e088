package cz.kpsys.portaro.erp.workattendance;

import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import lombok.NonNull;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Optional;
import java.util.UUID;

public record Activity(

        @NonNull
        UUID id,

        @NonNull
        UUID userId,

        @NonNull
        LocalDate startDay,

        @NonNull
        Instant startDate,

        @NonNull
        Duration regularDuration,

        @NonNull
        Duration overtimeDuration,

        @NonNull
        Duration onCallDutyDuration,

        @NonNull
        Duration absenceDuration,

        @NonNull
        Optional<AbsenceReason> absenceReason

) implements IdentifiedRecord<UUID> {

    public static Activity newWork(@NonNull UUID id,
                                   @NonNull UUID userId,
                                   @NonNull LocalDate startDay,
                                   @NonNull Duration regularDuration,
                                   @NonNull Duration overtimeDuration,
                                   @NonNull Provider<ZoneId> timeZoneProvider) {
        return new Activity(
                id,
                userId,
                startDay,
                startDay.atStartOfDay().atZone(timeZoneProvider.get()).toInstant(),
                regularDuration,
                overtimeDuration,
                Duration.ZERO,
                Duration.ZERO,
                Optional.empty()
        );
    }

    public static Activity newOnCall(@NonNull UUID id,
                                     @NonNull UUID userId,
                                     @NonNull LocalDate startDay,
                                     @NonNull Duration onCallDutyDuration,
                                     @NonNull Provider<ZoneId> timeZoneProvider) {
        return new Activity(
                id,
                userId,
                startDay,
                startDay.atStartOfDay().atZone(timeZoneProvider.get()).toInstant(),
                Duration.ZERO,
                Duration.ZERO,
                onCallDutyDuration,
                Duration.ZERO,
                Optional.empty()
        );
    }

    public static Activity newAbsence(@NonNull UUID id,
                                      @NonNull UUID userId,
                                      @NonNull LocalDate startDay,
                                      @NonNull Duration absenceDuration,
                                      @NonNull AbsenceReason absenceReason,
                                      @NonNull Provider<ZoneId> timeZoneProvider) {
        return new Activity(
                id,
                userId,
                startDay,
                startDay.atStartOfDay().atZone(timeZoneProvider.get()).toInstant(),
                Duration.ZERO,
                Duration.ZERO,
                Duration.ZERO,
                absenceDuration,
                Optional.of(absenceReason)
        );
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equalsIdentified(this, o, Activity.class);
    }

    @Override
    public int hashCode() {
        return id().hashCode();
    }
}

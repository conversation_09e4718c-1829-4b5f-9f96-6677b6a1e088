package cz.kpsys.portaro.exemplar;

import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.volume.Volume;
import cz.kpsys.portaro.record.Record;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.List;

public interface ExemplarLoader extends IdAndIdsLoadable<Exemplar, Integer> {

    List<Department> ALL_DEPARTMENTS = null;
    
    List<Exemplar> getAllByVolume(@NonNull Volume v, @Nullable List<Department> departments);
    
    List<Exemplar> getAllByBundle(@NonNull Exemplar bundle);
    
    List<@NonNull String> getAllNonNullSignaturesByDocument(@NonNull Record record);

}

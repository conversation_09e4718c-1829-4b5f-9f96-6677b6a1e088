package cz.kpsys.portaro.exemplar.delete;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.NonNull;
import lombok.Value;

import java.io.Serializable;

@JacksonXmlRootElement(localName = "smaz_exemp")
@Value
public class ExemplarDeletionAppserverRequest implements Serializable {

    @NonNull
    @JacksonXmlProperty(localName = "ID_EX")
    Integer exemplarId;

    @JacksonXmlProperty(localName = "ZRUS_VYPUC")
    Boolean ignoreLoansHistoryDeletion;

    @JacksonXmlProperty(localName = "ZRUS_VYMFOND")
    Boolean ignoreExchangeSetsHistoryDeletion;

    @JacksonXmlProperty(localName = "ZRUS_DODISS")
    Boolean ignoreSuppliedIssuesHistoryDeletion;
}

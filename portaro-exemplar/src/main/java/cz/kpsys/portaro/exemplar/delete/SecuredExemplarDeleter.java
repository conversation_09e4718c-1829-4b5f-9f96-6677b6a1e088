package cz.kpsys.portaro.exemplar.delete;

import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.exemplar.ExemplarSecurityActions;
import cz.kpsys.portaro.security.SecurityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SecuredExemplarDeleter implements ExemplarDeleter {

    @NonNull ExemplarDeleter delegate;
    @NonNull SecurityManager securityManager;

    @Override
    public ActionResponse deleteExemplars(@NonNull ExemplarsDeletionCommand command) {
        command.exemplars().forEach(exemplar -> securityManager.throwIfCannot(ExemplarSecurityActions.EXEMPLAR_DELETE, command.currentAuth(), command.ctx(), exemplar));
        return delegate.deleteExemplars(command);
    }
}

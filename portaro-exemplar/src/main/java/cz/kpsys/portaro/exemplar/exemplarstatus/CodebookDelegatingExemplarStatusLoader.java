package cz.kpsys.portaro.exemplar.exemplarstatus;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.DefaultProvider;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.dao.EmptyResultDataAccessException;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CodebookDelegatingExemplarStatusLoader implements ExemplarStatusLoader {

    @NonNull Codebook<ExemplarStatus, Integer> codebook;
    @NonNull ContextualProvider<Department, Integer> defaultStatusesByDepartments;
    @NonNull Provider<Department> rootDepartmentProvider;

    @Override
    public List<ExemplarStatus> getAll() {
        return codebook.getAll();
    }

    @Override
    public ExemplarStatus getById(@NonNull Integer id) throws ItemNotFoundException {
        return codebook.getById(id);
    }
    
    public ExemplarStatus getDefault() {
        Integer id = defaultStatusesByDepartments.getOn(rootDepartmentProvider.get());
        if (id != null) {
            try {
                return getById(id);
            } catch (EmptyResultDataAccessException | ItemNotFoundException ex) {
                //continue if not exists
            }
        }
        return DefaultProvider.byFirst(this).get();
    }
    
    @Override
    public ExemplarStatus getDefault(List<Department> departments) {
        for (Department dep : departments) {
            Integer id = defaultStatusesByDepartments.getOn(dep);
            if (id != null) {
                try {
                    return getById(id);
                } catch (EmptyResultDataAccessException | ItemNotFoundException ex) {
                    //continue if not exists
                }
            }
        }
        return getDefault();
    }
}

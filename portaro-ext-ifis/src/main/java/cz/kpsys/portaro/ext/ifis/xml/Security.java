package cz.kpsys.portaro.ext.ifis.xml;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.NonNull;

public record Security(

        @JacksonXmlProperty(namespace = "http://schemas.xmlsoap.org/soap/envelope/", localName = "mustUnderstand", isAttribute = true)
        @NonNull
        Integer mustUnderstand,

        @JacksonXmlProperty(namespace = "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd", localName = "UsernameToken")
        @NonNull
        UsernameToken usernameToken

) {

        public static final Integer MUST_UNDERSTAND = 1;

}

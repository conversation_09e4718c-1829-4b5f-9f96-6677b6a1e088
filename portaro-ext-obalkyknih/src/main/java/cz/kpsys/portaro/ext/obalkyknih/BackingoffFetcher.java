package cz.kpsys.portaro.ext.obalkyknih;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.time.Duration;
import java.time.Instant;
import java.time.InstantSource;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.function.Supplier;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class BackingoffFetcher<T> {

    @NonNull InstantSource instantSource;
    @NonNull Duration backoffPeriodOnEmptyData;
    @NonNull Supplier<T> fetcher;
    @NonNull Predicate<T> emptyChecker;

    @NonFinal @NonNull Instant lastFetch = Instant.MIN;
    @NonFinal @NonNull Instant nextFetch = Instant.MIN;

    public BackingoffFetcher(@NonNull Duration backoffPeriodOnEmptyData,
                             @NonNull Supplier<T> fetcher,
                             @NonNull Predicate<T> emptyChecker) {
        this(InstantSource.system(), backoffPeriodOnEmptyData, fetcher, emptyChecker);
    }

    public Optional<T> tryFetch() {
        var now = instantSource.instant();
        if (isReady(now)) {
            lastFetch = now;
            var ret = fetcher.get();
            if (ret == null || emptyChecker.test(ret)) {
                scheduleNextStartingFrom(now);
                return Optional.empty();
            }
            return Optional.of(ret);
        }
        return Optional.empty();
    }

    public boolean isReady() {
        return isReady(instantSource.instant());
    }

    public void backoff() {
        scheduleNextStartingFrom(lastFetch);
    }

    private boolean isReady(Instant now) {
        return now.isAfter(nextFetch);
    }

    private void scheduleNextStartingFrom(Instant fromStart) {
        nextFetch = fromStart.plus(backoffPeriodOnEmptyData);
    }

}

package cz.kpsys.portaro.ext.report.server;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.setting.SettingKey;

public class ReportServerSettingsKeys {

    public static final String SECTION_INTEG_REPORT_SERVER = "integ.reportserver";
    public static final SettingKey<Boolean> REPORT_SERVER_ENABLED = new SettingKey<>(SECTION_INTEG_REPORT_SERVER, "enabled");
    public static final SettingKey<@NullableNotBlank String> REPORT_SERVER_API_URL = new SettingKey<>(SECTION_INTEG_REPORT_SERVER, "apiUrl");
    public static final SettingKey<@NullableNotBlank String> REPORT_SERVER_ROOT_FOLDER = new SettingKey<>(SECTION_INTEG_REPORT_SERVER, "rootDirectory");
    public static final SettingKey<@NullableNotBlank String> REPORT_SERVER_READER_CLIENT_ID = new SettingKey<>(SECTION_INTEG_REPORT_SERVER, "apiReaderClientId");
    public static final SettingKey<@NullableNotBlank String> REPORT_SERVER_READER_CLIENT_TOKEN = new SettingKey<>(SECTION_INTEG_REPORT_SERVER, "apiReaderClientToken");
    public static final SettingKey<@NullableNotBlank String> REPORT_SERVER_EDITOR_CLIENT_ID = new SettingKey<>(SECTION_INTEG_REPORT_SERVER, "apiEditorClientId");
    public static final SettingKey<@NullableNotBlank String> REPORT_SERVER_EDITOR_CLIENT_TOKEN = new SettingKey<>(SECTION_INTEG_REPORT_SERVER, "apiEditorClientToken");
    public static final SettingKey<@NullableNotBlank String> REPORT_SERVER_RECORD_ROOT_FOLDER = new SettingKey<>(SECTION_INTEG_REPORT_SERVER, "recordRootDirectory");
}
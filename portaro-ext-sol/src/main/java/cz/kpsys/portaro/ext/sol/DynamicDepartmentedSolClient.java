package cz.kpsys.portaro.ext.sol;


import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.web.HttpHeaderConstants.ContentType;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.sol.datatypes.SolUser;
import cz.kpsys.portaro.ext.sol.datatypes.request.ExportPersonBodyRequest;
import cz.kpsys.portaro.ext.sol.datatypes.request.ExportPersonLoginRequest;
import cz.kpsys.portaro.ext.sol.datatypes.response.ExportedPersonBodyResponse;
import cz.kpsys.portaro.integ.feign.HeaderAddingRequestInterceptor;
import cz.kpsys.portaro.soap.Envelope;
import cz.kpsys.portaro.soap.HeadlessW3SoapEnvelope;
import feign.Feign;
import feign.Logger;
import feign.RequestInterceptor;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.slf4j.Slf4jLogger;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import org.springframework.cloud.openfeign.support.SpringMvcContract;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DynamicDepartmentedSolClient implements SolDepartmentedClient {

    @NonNull ContextualProvider<Department, @NonNull String> apiUrl;
    @NonNull ContextualProvider<Department, @NonNull String> apiUser;
    @NonNull ContextualProvider<Department, @NonNull String> apiPassword;
    @NonNull RequestInterceptor userAgentAddingInterceptor;
    @NonNull ObjectMapper xmlMapper;

    @SneakyThrows
    @Override
    public List<SolUser> getSolPersons(Department department) {
        HeadlessW3SoapEnvelope<ExportPersonBodyRequest> envelopeRequest = new HeadlessW3SoapEnvelope<>(
                new ExportPersonBodyRequest(new ExportPersonLoginRequest(apiUser.getOn(department), apiPassword.getOn(department)))
        );

        Envelope<ExportedPersonBodyResponse> envelopeResponse = getClientByDepartment(department).getExportPerson(envelopeRequest);
        String result = envelopeResponse.body().getExportedPersonResponse().getResult().replaceAll("<xs:schema[\\s\\S]*?</xs:schema>", "");
        return xmlMapper.readValue(result, new TypeReference<>() {});
    }

    private SolClient getClientByDepartment(Department department) {
        return Feign.builder()
                .encoder(new JacksonEncoder(xmlMapper))
                .decoder(new JacksonDecoder(xmlMapper))
                .logger(new Slf4jLogger(SolClient.class))
                .logLevel(Logger.Level.FULL)
                .requestInterceptor(userAgentAddingInterceptor)
                .requestInterceptor(HeaderAddingRequestInterceptor.ofStaticValue(ContentType.NAME, ContentType.Value.SOAP_XML))
                .contract(new SpringMvcContract())
                .target(SolClient.class, apiUrl.getOn(department));
    }
}

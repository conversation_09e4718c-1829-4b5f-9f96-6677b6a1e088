package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public enum PracInvaliditaKat implements Identified<Integer> {

    NOT_DEFINED(1, "---"),
    EMPTY(2, "nevypněno"),
    PRVNI_STUPEN(3, "I. stupeň"),
    DRUHY_STUPEN(4, "II. stupeň"),
    NE(5, "NE"),
    DUCHODCE(6, "Důchodce"),
    ZPT(7, "ZTP"),
    TRETI_STUPEN(8, "III. stupeň");

    public static final Codebook<PracInvaliditaKat, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer sutorId;
    @NonNull String value;

    public @NonNull Integer getId() {
        return sutorId;
    }

    public @NonNull String portaroVal() {
        return sutorId.toString();
    }

}

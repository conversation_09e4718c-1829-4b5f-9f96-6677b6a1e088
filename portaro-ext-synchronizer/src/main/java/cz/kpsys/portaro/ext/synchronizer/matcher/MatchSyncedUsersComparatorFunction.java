package cz.kpsys.portaro.ext.synchronizer.matcher;

import cz.kpsys.portaro.user.Person;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MatchSyncedUsersComparatorFunction<CTX, EXT> implements SyncedUsersComparatorFunction<CTX, EXT> {

    @NonNull List<SyncedUsersEqualFunction<CTX, EXT>> delegates;

    @Override
    public boolean areEqual(@NonNull EXT externalUser, @NonNull Person databasedUser, @NonNull CTX ctx) {
        return delegates.stream().anyMatch(syncedUsersEqualFunction -> syncedUsersEqualFunction.areEqual(externalUser, databasedUser, ctx));
    }

    @Override
    public Optional<EXT> getBestMatch(@NonNull List<EXT> externalUsers, @NonNull Person databasedUser, @NonNull CTX ctx) {
        return externalUsers.stream()
                .max(Comparator.comparingLong(externalUser -> getMatchCount(databasedUser, ctx, externalUser)));
    }

    private long getMatchCount(Person databasedUser, CTX ctx, EXT externalUser) {
        return delegates.stream()
                .filter(syncedUsersEqualFunction -> syncedUsersEqualFunction.areEqual(externalUser, databasedUser, ctx))
                .count();
    }
}

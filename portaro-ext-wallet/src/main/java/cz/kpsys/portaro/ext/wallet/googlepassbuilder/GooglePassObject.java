package cz.kpsys.portaro.ext.wallet.googlepassbuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import cz.kpsys.portaro.ext.wallet.CardLocalisation;
import lombok.Builder;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.function.Function;

@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public record GooglePassObject(
        @NonNull String id,
        @NonNull String classId,
        @NonNull GPState state,
        @NonNull GPLocalizedString cardTitle,
        @NonNull GPLocalizedString subheader,
        @NonNull GPLocalizedString header,
        @Nullable List<GPTextModule> textModulesData,
        @Nullable GPBarcode barcode,
        @NonNull String hexBackgroundColor,
        @Nullable GPImage logo,
        @Nullable GPImage heroImage
) {

    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record GPBarcode(
            @NonNull GPBarcodeType type,
            @NonNull String value,
            @NonNull String alternateText
    ) {}

    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record GPImage(
            @NonNull GPSourceUri sourceUri,
            @NonNull GPLocalizedString contentDescription
    ) {

        @Builder
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public record GPSourceUri(
                @NonNull String uri
        ) {}
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record GPLocalizedString(
            @NonNull LangString defaultValue,
            @Nullable List<LangString> localizedValue
    ) {

        public static GPLocalizedString createDefault(String value) {
            return new GPLocalizedString(
                    new LangString("en", value),
                    null
            );
        }

        public static GPLocalizedString createLocalised(String defaultValue, Map<Locale, CardLocalisation> locales, Function<CardLocalisation, String> valueExtractor) {
            List<LangString> localisedValues = locales.entrySet().stream()
                    .filter((entry -> !entry.getKey().getLanguage().equals("en")))
                    .map(entry -> new LangString(entry.getKey().getLanguage(), valueExtractor.apply(entry.getValue())))
                    .toList();

            return new GPLocalizedString(
                    new LangString("en", defaultValue),
                    localisedValues
            );
        }

        public static GPLocalizedString createDefault(String language, String value) {
            return new GPLocalizedString(
                    new LangString(language, value),
                    null
            );
        }

        @JsonInclude(JsonInclude.Include.NON_NULL)
        public record LangString(
                @NonNull String language,
                @NonNull String value
        ) {}
    }

    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record GPTextModule(
            @NonNull String id,
            @NonNull GPLocalizedString localizedHeader,
            @NonNull GPLocalizedString localizedBody
    ) {}

    public enum GPState {
        ACTIVE,
        EXPIRED
    }

    public enum GPBarcodeType {
        CODE_128,
        QR_CODE
    }
}
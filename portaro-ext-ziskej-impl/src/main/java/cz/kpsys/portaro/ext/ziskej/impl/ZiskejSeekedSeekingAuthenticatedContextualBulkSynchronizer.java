package cz.kpsys.portaro.ext.ziskej.impl;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.context.AuthenticatedSubjectUpdater;
import cz.kpsys.portaro.commons.object.repo.ByIdOptLoadable;
import cz.kpsys.portaro.commons.sync.SyncItem;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.synchronizer.AuthenticatedContextualBulkSynchronizer;
import cz.kpsys.portaro.ext.synchronizer.result.BulkSyncResult;
import cz.kpsys.portaro.ext.synchronizer.result.ItemSyncResult;
import cz.kpsys.portaro.ext.synchronizer.result.SyncResolveMode;
import cz.kpsys.portaro.ext.ziskej.ContextualZiskejClient;
import cz.kpsys.portaro.ext.ziskej.model.TicketType;
import cz.kpsys.portaro.ext.ziskej.model.ZiskejTicket;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.loan.ill.Seeking;
import cz.kpsys.portaro.loan.ill.SeekingConstants;
import cz.kpsys.portaro.search.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ZiskejSeekedSeekingAuthenticatedContextualBulkSynchronizer implements AuthenticatedContextualBulkSynchronizer<Seeking>, AuthenticatedSubjectUpdater<Seeking, Department> {

    @NonNull ParameterizedSearchLoader<MapBackedParams, Seeking> seekingSearchLoader;
    @NonNull ByIdOptLoadable<Seeking, String> seekingByZiskejTicketIdLoader;
    @NonNull ContextualZiskejClient<Department> contextualZiskejClient;
    @NonNull ZiskejTicketSeekingSyncHandler ziskejTicketSeekingSyncHandler;
    @NonNull TransactionTemplate transactionTemplate;
    @NonNull HierarchyLoader<Department> departmentAccessor;

    @Override
    public BulkSyncResult<Seeking> synchronizeAllOn(Department ctx, UserAuthentication currentAuth) {
        StaticParamsModifier params = StaticParamsModifier.of(
                SeekingConstants.SearchParams.INCLUDE_SEEKERS_VIEW, true,
                SeekingConstants.SearchParams.INCLUDE_PROVIDERS_VIEW, false,
                SeekingConstants.SearchParams.INCLUDE_STANDALONE, false,
                SeekingConstants.SearchParams.INCLUDE_NOT_ACCEPTED, false,
                SeekingConstants.SearchParams.INCLUDE_RETURNED, false,
                SeekingConstants.SearchParams.INCLUDE_CANCELLED, false,
                CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.SUBTREE)
        );
        List<Seeking> ziskejConnectedSeekings = seekingSearchLoader.getContent(RangePaging.forAll(), params);

        List<ZiskejTicket> openZiskejTickets = contextualZiskejClient.getTickets(ctx, true);
        ListUtil.Splitted<List<ZiskejTicket>, List<ZiskejTicket>> openZiskejTicketsSplit = ListUtil.split(openZiskejTickets, ziskejTicket -> ziskejTicket.ticketType() == TicketType.MVS);
        if (!openZiskejTicketsSplit.unmatched().isEmpty()) {
            log.warn("There are Ziskej tickets with other than MVS type, they will not be synced: {}", openZiskejTicketsSplit.unmatched().stream().map(zt -> zt.id() + " is with type " + zt.ticketType()).toList());
        }
        List<ZiskejTicket> openMvsZiskejTickets = openZiskejTicketsSplit.matched();

        List<SyncItem<Seeking, ZiskejTicket>> syncItems = SyncItem.merge(ziskejConnectedSeekings, openMvsZiskejTickets, (seeking, ziskejTicket) -> seeking.getZiskejTicketId().equals(ziskejTicket.id()));
        List<ItemSyncResult<Seeking, ?>> syncResults = ListUtil.convert(syncItems, syncItem -> syncSingleInTransaction(syncItem, ctx, currentAuth));

        return new BulkSyncResult<>(syncResults);
    }

    @Override
    public @NonNull Seeking update(@NonNull Seeking seeking, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        if (!seeking.isSeekersView()) {
            return seeking;
        }
        ZiskejTicket ziskejTicket = contextualZiskejClient.getTicket(ctx, seeking.getZiskejTicketId());
        ItemSyncResult<Seeking, ?> syncResult = syncSingleInTransaction(SyncItem.ofBothExisting(seeking, ziskejTicket), ctx, currentAuth);
        return syncResult.item().orElseThrow();
    }

    private ItemSyncResult<Seeking, ?> syncSingleInTransaction(SyncItem<Seeking, ZiskejTicket> syncItem, Department ctx, @NonNull UserAuthentication currentAuth) {
        try {
            return transactionTemplate.execute(txStatus -> syncSingle(syncItem, ctx, currentAuth));
        } catch (Exception e) {
            if (false) {
                log.error("Failed to sync seeking {}: {}", syncItem, e.getMessage(), e);
                return ItemSyncResult.failed(ctx, syncItem, e);
            }
            // currently, we will throw exception untill testing
            throw e;
        }
    }

    private ItemSyncResult<Seeking, ?> syncSingle(SyncItem<Seeking, ZiskejTicket> syncItem, Department ctx, @NonNull UserAuthentication currentAuth) {
        return switch (syncItem.mode()) {
            case APPEARED -> handleSeekingAppeared(syncItem.addingExternal(), ctx, currentAuth);
            case REMAINING -> handleSeekingRemaining(syncItem.remainingInternal(), syncItem.remainingExternal(), ctx, currentAuth);
            case DISAPPEARED -> handleSeekingDisappeared(syncItem.removingInternal(), ctx, currentAuth);
        };
    }

    private ItemSyncResult<Seeking, ?> handleSeekingAppeared(ZiskejTicket ziskejTicket, Department ctx, @NonNull UserAuthentication currentAuth) {
        // first, check, if seeking is still active on Ziskej, but already closed/hidden in DB
        Optional<Seeking> existingButHidden = seekingByZiskejTicketIdLoader.findById(ziskejTicket.id());
        if (existingButHidden.isPresent()) {
            return handleSeekingRemaining(existingButHidden.get(), ziskejTicket, ctx, currentAuth);
        }
        if (ziskejTicket.reader() == null) {
            log.warn("Field reader in ZiskejTicket is null - usually means that reader identifier was not (yet) filled via Ziskej portal ({})", ziskejTicket);
            return ItemSyncResult.skipped(ctx, ziskejTicket);
        }
        Seeking saved = ziskejTicketSeekingSyncHandler.handleNew(ziskejTicket, ctx, currentAuth);
        return ItemSyncResult.success(ctx, saved, SyncResolveMode.CREATED, null);
    }

    private ItemSyncResult<Seeking, ?> handleSeekingRemaining(Seeking seeking, ZiskejTicket ziskejTicket, Department ctx, @NonNull UserAuthentication currentAuth) {
        Seeking saved = ziskejTicketSeekingSyncHandler.handleExisting(ziskejTicket, seeking, ctx, currentAuth);
        return ItemSyncResult.success(ctx, saved, SyncResolveMode.UPDATED, null);
    }

    private ItemSyncResult<Seeking, ?> handleSeekingDisappeared(Seeking seeking, Department ctx, @NonNull UserAuthentication currentAuth) {
        ZiskejTicket closedZiskejTicket = contextualZiskejClient.getTicket(ctx, seeking.getZiskejTicketId());
        Seeking saved = ziskejTicketSeekingSyncHandler.handleExisting(closedZiskejTicket, seeking, ctx, currentAuth);
        return ItemSyncResult.success(ctx, saved, SyncResolveMode.UPDATED, null);
    }

}

package cz.kpsys.portaro.ext.ziskej.model;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import lombok.NonNull;

public record ZiskejUser(

        /**
         * ID čtenáře v Získej
         * nullable: true (ve swaggeru)
         */
        @NullableNotBlank
        String id,

        /**
         * ID čtenáře podle knihovny, například č<PERSON> (knihovna volí, jakou hodnotu v tomto atributu udržuje).
         * nullable: true (ve swaggeru)
         */
        @NonNull
        String readerLid,

        /**
         * Eppn čtenáře v CPK
         * nullable: true (ve swaggeru, v realu)
         */
        @NullableNotBlank
        String eppn,

        /**
         * ID čtenáře v AKS
         * nullable: true (ve swaggeru)
         */
        @NullableNotBlank
        String readerAksId,

        /**
         * ID čtenáře v AKS podle CPK
         * nullable: true (ve swaggeru)
         */
        @NullableNotBlank
        String readerLibraryId

) {}

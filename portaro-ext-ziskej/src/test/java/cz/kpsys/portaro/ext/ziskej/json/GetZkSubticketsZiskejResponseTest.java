package cz.kpsys.portaro.ext.ziskej.json;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.ext.ziskej.ZiskejClientObjectMapperFactory;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

@Tag("ci")
@Tag("unit")
public class GetZkSubticketsZiskejResponseTest {

    private static ObjectMapper objectMapper() {
        return new ZiskejClientObjectMapperFactory().get();
    }

    @Test
    public void shouldDeserialize() throws JsonProcessingException {
        String json = """
                {
                  "subtickets": [
                    {
                      "cond_a_send_date_value": null,
                      "cond_reason": "edition",
                      "accepted_date": "2024-02-05",
                      "updated_datetime": "2024-02-07T17:49:17+01:00",
                      "back_date": "2024-03-04",
                      "is_open": true,
                      "subticket_url": "https://ziskej-test.techlib.cz/tickets/986093c453b1415a/ef23e476d61243b9",
                      "cond_a_edition_value": "vydani 2005",
                      "refuse_reason": "",
                      "closed_date": null,
                      "created_datetime": "2024-02-05T16:04:47+01:00",
                      "ticket_id": "986093c453b1415a",
                      "sent_back_date": null,
                      "status": "sent",
                      "rfid": null,
                      "sent_date": "2024-02-05",
                      "is_snail_mail": true,
                      "sigla_dk": "fmg002",
                      "count_messages_unread": 0,
                      "barcode": null,
                      "hid": 100661,
                      "cond_a_return_d_value": null,
                      "cond_accepted_date": "2024-02-05",
                      "bibliographic_reference": "VOSS, Christopher; RAZ, Tahl; NEVRL\\u00c1, Eva. Nikdy ned\\u011blej kompromis, aneb, Vyjedn\\u00e1vej tak, jako by ti \\u0161lo o \\u017eivot. Vyd\\u00e1n\\u00ed prvn\\u00ed. 280 stran. ISBN 978-80-7555-002-6.",
                      "fee_dk": 70,
                      "status_label": "Odeslan\\u00fd",
                      "cond_a_fee_value": null,
                      "subticket_id": "ef23e476d61243b9",
                      "count_messages": 0,
                      "subticket_type": "mvs",
                      "doc_id": null,
                      "doc_dk_id": null
                    }
                  ]
                }
                """;

        GetZkSubticketsZiskejResponse response = objectMapper().readValue(json, GetZkSubticketsZiskejResponse.class);

        ZkSubticketZiskejResponse subticket = response.subtickets().getFirst();
        assertEquals("ef23e476d61243b9", subticket.subticketId());
        assertEquals("fmg002", subticket.siglaDk());
        assertEquals(100661, subticket.hid());
        assertEquals(SubticketStatusZiskejResponse.SENT, subticket.status());
        assertEquals(70, subticket.feeDk());
        assertEquals(ConditionReasonZiskejRequestResponse.EDITION, subticket.condReason());
        assertNull(subticket.refuseReason());
        assertEquals(Instant.parse("2024-02-05T15:04:47Z"), subticket.createdDatetime());
        assertEquals(Instant.parse("2024-02-07T16:49:17Z"), subticket.updatedDatetime());
    }
}

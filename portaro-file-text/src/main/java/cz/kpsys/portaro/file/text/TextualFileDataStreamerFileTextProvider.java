package cz.kpsys.portaro.file.text;

import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.io.FileDataStreamerHelper;
import cz.kpsys.portaro.file.IdentifiedFile;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class TextualFileDataStreamerFileTextProvider implements FileTextProvider {

    @NonNull FileDataStreamer textualFileDataStreamer;

    @Override
    public String getText(@NonNull IdentifiedFile file) {
        return FileDataStreamerHelper.loadDataToString(textualFileDataStreamer, file.getId());
    }
}

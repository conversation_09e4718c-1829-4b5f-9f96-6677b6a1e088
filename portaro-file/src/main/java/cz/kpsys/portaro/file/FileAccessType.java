package cz.kpsys.portaro.file;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.BasicNamedLabeledIdentified;

public class FileAccessType extends BasicNamedLabeledIdentified<Integer> {

    public static final Integer PERMIT_ID = 0;
    public static final Integer ADMINISTRATOR_ONLY = 3001;
    public static final Integer READ_ONLY = 4001;
    public static final Integer LOGGED_READ_ONLY = 4011;
    public static final Integer EDITOR_ONLY = 4012;
    public static final Integer THREAD_PARTICIPANT_ONLY = 4013;


    public FileAccessType(Integer id, String name) {
        super(id, name);
    }

    public static FileAccessType createPermitting() {
        return new FileAccessType(0, "Accessible");
    }

    @Override
    public Text getText() {
        return Texts.ofColumnMessageCodedOrNative(getName(), "DEF_FULLTEXT_PRISTUP", "POPIS", getId());
    }
}

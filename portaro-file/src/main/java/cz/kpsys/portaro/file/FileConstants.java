package cz.kpsys.portaro.file;

import cz.kpsys.portaro.datatype.ScalarDatatype;
import org.springframework.web.util.UriTemplate;

import static cz.kpsys.portaro.datatype.Datatype.scalar;

public class FileConstants {

    public static class Datatype {
        public static final ScalarDatatype FILE = scalar("FILE");
        public static final ScalarDatatype DIRECTORY = scalar("DIRECTORY");
        public static final ScalarDatatype FILE_CATEGORY = scalar("FILE_KAT");
        public static final ScalarDatatype FILE_PROCESSING_STATE = scalar("FILE_PROCESSING_STATE");
    }

    public static class Web {

        public static final String FILE_DOWNLOAD_PATH_PREFIX = "/files";

        /**
         * Template with "fileId" placeholder
         */
        public static final UriTemplate FILE_DOWNLOAD_URI_TEMPLATE = new UriTemplate(FILE_DOWNLOAD_PATH_PREFIX + "/{fileId}");

        public static final String MEDIA_VIEWER_PATH = "/media-viewer";
        public static final String MEDIA_VIEWER_ROOT_DIR_PARAM = "rootDirectory";
        public static final String MEDIA_VIEWER_FILE_PARAM = "file";
        public static final String MEDIA_VIEWER_ORIGIN_PARAM = "origin";
        public static final String MEDIA_VIEWER_EDIT_PARAM = "edit";
    }

    public static class FileConstraint {
        public static final int NAME_MAX_LENGTH = 100;
        public static final int FILENAME_MAX_LENGTH = 100;
    }

}

package cz.kpsys.portaro.file;

import cz.kpsys.portaro.commons.object.BasicIdentified;
import cz.kpsys.portaro.commons.object.Identified;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.util.Optional;

public class IdentifiedData<DATA> extends BasicIdentified<Long> implements Identified<Long> {

    @Getter
    @Nullable private final DATA data;
    @Nullable private FileProcessingState newProcessingState = null;

    public IdentifiedData(Long id, DATA data) {
        super(id);
        this.data = data;
    }

    public static <DATA> IdentifiedData<DATA> of(Long id, DATA data) {
        return new IdentifiedData<>(id, data);
    }

    public static <DATA> IdentifiedData<DATA> ofNullData(Long id) {
        return new IdentifiedData<>(id, null);
    }

    public IdentifiedData<DATA> withNewProcessState(FileProcessingState newProcessingState) {
        this.newProcessingState = newProcessingState;
        return this;
    }

    public Optional<FileProcessingState> getNewProcessingState() {
        return Optional.ofNullable(newProcessingState);
    }
}

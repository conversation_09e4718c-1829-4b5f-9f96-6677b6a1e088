package cz.kpsys.portaro.file;

import cz.kpsys.portaro.appserver.dml.TableWrite;
import cz.kpsys.portaro.appserver.dml.TableWriteGenerator;
import cz.kpsys.portaro.databasestructure.RecordDb.KAT1_4;
import cz.kpsys.portaro.databasestructure.RecordDb.KATAUT_4;
import cz.kpsys.portaro.databasestructure.RecordDb.RECORD;
import cz.kpsys.portaro.id.IDdFile;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class IdentifiedFileDeletionTableWriteGenerator implements TableWriteGenerator<IDdFile> {

    @Override
    public List<TableWrite> generateWrites(IDdFile f) {
        TableWrite fileDeletion = TableWrite.createUpdate(TABLE)
                .addWhereCol(ID_FULLTEXT, f.getId())
                .addCol(IS_INDEX_LUCENE, FileProcessingState.READY_TO_DELETE.getId());

        // Delete file links proactively before appserver indexer kicks in
        TableWrite documentPrimaryImage = TableWrite.createUpdate(KAT1_4.TABLE)
                .addWhereCol(KAT1_4.FK_FULLTEXT_IMAGE, f.getId())
                .addCol(KAT1_4.FK_FULLTEXT_IMAGE, null);

        TableWrite authorityPrimaryImage = TableWrite.createUpdate(KATAUT_4.TABLE)
                .addWhereCol(KATAUT_4.FK_FULLTEXT_IMAGE, f.getId())
                .addCol(KATAUT_4.FK_FULLTEXT_IMAGE, null);

        TableWrite recordPrimaryImage = TableWrite.createUpdate(RECORD.TABLE)
                .addWhereCol(RECORD.PRIMARY_FILE_ID, f.getId())
                .addCol(RECORD.PRIMARY_FILE_ID, null);

        return List.of(fileDeletion, documentPrimaryImage, authorityPrimaryImage, recordPrimaryImage);
    }
}

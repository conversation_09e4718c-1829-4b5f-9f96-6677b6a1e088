package cz.kpsys.portaro.file;

import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.UpdateQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.function.Consumer;

import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.*;

/**
 * Uz se nepouziva (ani v AS)
 */
@Deprecated
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SpringDbFileProcessingStateSaver implements Saver<FileProcessingStateSaveRequest, Long> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull Consumer<Long> beforeSaveListener;

    @Override
    public @NonNull Long save(@NonNull FileProcessingStateSaveRequest request) {
        Long fileId = request.getId();

        UpdateQuery uq = queryFactory.newUpdateQuery();
        uq.update(TABLE);
        uq.set().add(IS_INDEX_LUCENE, request.getFileProcessingState().getId());
        uq.where().eq(ID_FULLTEXT, fileId);
        beforeSaveListener.accept(fileId);
        jdbcTemplate.update(uq.getSql(), uq.getParamMap());

        log.debug("File processing state of file {} saved", fileId);
        return fileId;
    }
}

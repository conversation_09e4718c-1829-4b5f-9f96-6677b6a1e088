package cz.kpsys.portaro.file.custom;

import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.io.FileDataStreamerHelper;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.core.io.AbstractResource;
import org.springframework.lang.Nullable;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.Instant;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CustomFileResource extends AbstractResource {

    @NonNull String filePath;
    @NonNull ByIdLoadable<CustomFile, String> customFileLoader;
    @NonNull FileDataStreamer fileDataStreamer;
    @NonNull Instant timestamp;

    @Nullable
    @NonFinal
    private byte[] loadedData;

    @Override
    public String getDescription() {
        return "Custom file resource [" + this.filePath + "]";
    }

    @Override
    public InputStream getInputStream() throws IOException {
        if (loadedData == null) {
            loadedData = loadCustomFileByteArray();
        }
        return new ByteArrayInputStream(loadedData);
    }

    @Override
    public long lastModified() {
        return timestamp.toEpochMilli();
    }

    private byte[] loadCustomFileByteArray() {
        CustomFile customFile = customFileLoader.getById(filePath);
        Long fileId = customFile.getFileId();
        return FileDataStreamerHelper.loadDataToBytes(fileDataStreamer, fileId);
    }
}

package cz.kpsys.portaro.file.directory;

import cz.kpsys.portaro.file.IdentifiedFile;

import java.util.List;

/**
 * Created by janpa on 28.06.2017.
 */
public class DirectoryInsight {

    private final boolean withSubdirectories;
    private final boolean withNonCoverFiles;
    private final List<IdentifiedFile> sampleFiles;

    public DirectoryInsight(boolean withSubdirectories, boolean withNonCoverFiles, List<IdentifiedFile> sampleFiles) {
        this.withSubdirectories = withSubdirectories;
        this.withNonCoverFiles = withNonCoverFiles;
        this.sampleFiles = sampleFiles;
    }

    public boolean isWithSubdirectories() {
        return withSubdirectories;
    }

    public boolean isWithNonCoverFiles() {
        return withNonCoverFiles;
    }

    public List<IdentifiedFile> getSampleFiles() {
        return sampleFiles;
    }
}

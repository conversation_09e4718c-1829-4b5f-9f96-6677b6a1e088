package cz.kpsys.portaro.file.directory;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.file.FileAccessType;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DirectoryNode implements ParentableDirectory, AddableChildrenable<DirectoryNode> {

    @NonNull ParentableDirectory directory;
    @NonNull List<DirectoryNode> children = new ArrayList<>();

    @Override
    public Integer getId() {
        return directory.getId();
    }

    @Override
    public void setId(Integer id) {
        directory.setId(id);
    }

    @Override
    public Text getText() {
        return directory.getText();
    }

    @Override
    public Optional<Integer> getParentDirectoryId() {
        return directory.getParentDirectoryId();
    }

    @Override
    public String getName() {
        return directory.getName();
    }

    @Override
    public Integer getOrder() {
        return directory.getOrder();
    }

    @Override
    public FileAccessType getAccessType() {
        return directory.getAccessType();
    }

    @Override
    public void addChild(DirectoryNode child) {
        children.add(child);
    }

    @Override
    public void addChildren(@NonNull List<DirectoryNode> otherChildren) {
        children.addAll(otherChildren);
    }

    @JsonProperty
    @Override
    public List<DirectoryNode> children() {
        return children;
    }

    public boolean isLeaf() {
        return children.isEmpty();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Directory that)) {
            return false;
        }
        return Objects.equals(directory, that);
    }

    @Override
    public int hashCode() {
        return Objects.hash(directory, children());
    }

    @JsonIgnore
    public List<DirectoryNode> getAllDescendants() {
        List<DirectoryNode> descendants = new ArrayList<>();
        addDescendants(this, descendants);
        return descendants;
    }

    @JsonIgnore
    private void addDescendants(DirectoryNode node, List<DirectoryNode> descendants) {
        for (DirectoryNode child : node.children()) {
            descendants.add(child);
            addDescendants(child, descendants);
        }
    }
}

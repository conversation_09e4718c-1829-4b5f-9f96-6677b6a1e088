package cz.kpsys.portaro.file.web;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.current.CurrentAuth;
import cz.kpsys.portaro.commons.io.BytesRange;
import cz.kpsys.portaro.commons.io.JavaTempFileService;
import cz.kpsys.portaro.commons.io.JimfsTempDir;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.LoadedIdentifiedFile;
import cz.kpsys.portaro.file.directory.BasicDirectory;
import cz.kpsys.portaro.file.security.IdentifiedFileSaveCommand;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.role.editor.EditLevel;
import cz.kpsys.portaro.user.role.editor.EditorAccount;
import org.junit.jupiter.api.Test;
import org.springframework.transaction.support.TransactionOperations;

import java.nio.ByteBuffer;
import java.nio.file.Path;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;

class AppserverChunkedFileUploadServiceTest {

    private static UserAuthentication createAuth() {
        User user = Person.testing(123)
                .addUserRole(new EditorAccount(
                        123,
                        123,
                        false,
                        "**********",
                        null,
                        EditLevel.WORST,
                        true)
                );
        return CurrentAuth.createWithAbsoluteAuthenticity(user);
    }

    @JimfsTempDir
    static Path sharedTempBlobDir;


    @Test
    void testChunkedFileUpload() {
        String filename = "test金.jpeg";
        int directoryId = 1;
        byte[] data = new byte[123];
        ByteBuffer bb = ByteBuffer.wrap(data);
        Arrays.fill(data, (byte) 0xAA);

        final JavaTempFileService filesystemService = new JavaTempFileService(sharedTempBlobDir);
        final Saver<IdentifiedFileSaveCommand, IdentifiedFile> fileSaver = command -> {
            var file = (LoadedIdentifiedFile) command.file();
            assertEquals(filename, file.getFilename());
            assertEquals(directoryId, file.getDirectory().getId());
            assertArrayEquals(data, file.getData());

            return file;
        };
        final AppserverChunkedFileUploadService service = new AppserverChunkedFileUploadService(
                filesystemService,
                TransactionOperations.withoutTransaction(),
                BasicDirectory::testing,
                fileSaver
        );
        final UserAuthentication currentAuth = createAuth();
        final Department ctx = Department.testing(1);

        var uploadHandle = service.initChunkFileUpload(filename, 123, directoryId);

        service.writeTo(uploadHandle, new BytesRange(0, 99), bb.limit(100));
        service.writeTo(uploadHandle, new BytesRange(100, 122), bb.position(100).limit(123));

        var file = service.finishWriting(uploadHandle, ctx, currentAuth);
        assertEquals(filename, file.getFilename());
        assertEquals(directoryId, file.getDirectory().getId());
    }

}
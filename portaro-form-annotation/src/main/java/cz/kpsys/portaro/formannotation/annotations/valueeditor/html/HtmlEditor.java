package cz.kpsys.portaro.formannotation.annotations.valueeditor.html;

import cz.kpsys.portaro.formannotation.annotations.valueeditor.EditorOptions;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditor;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.TYPE_USE})
@Retention(RetentionPolicy.RUNTIME)
@ValueEditor
public @interface HtmlEditor {

    EditorOptions editorOptions() default @EditorOptions();
}

package cz.kpsys.portaro.form.spring;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.validation.ValidationUtils;
import jakarta.validation.ConstraintViolation;
import lombok.NonNull;

import java.util.Collection;
import java.util.function.Predicate;

public class ConstraintViolationExceptionRecoverableErrorResolver extends RecoverableErrorResolver<ConstraintViolation<?>> {

    public ConstraintViolationExceptionRecoverableErrorResolver(@NonNull Collection<Predicate<ByFormRecoverableFieldResolverParams>> customByFormRecoverableFieldResolver, @NonNull Collection<Predicate<ByFormRecoverableObjectResolverParams>> customByFormRecoverableObjectResolver) {
        super(customByFormRecoverableFieldResolver, customByFormRecoverableObjectResolver);
    }

    @Override
    protected boolean isRecoverableError(@NonNull Object violatedFormObject, ConstraintViolation<?> constraintViolation) {
        if (ValidationUtils.isClassLevelConstraintViolation(constraintViolation)) {
            return isInvalidValueOfObjectRecoverableByForm(
                    violatedFormObject,
                    constraintViolation.getConstraintDescriptor().getAnnotation().annotationType()
            );
        }

        return isInvalidValueOfFieldRecoverableByForm(
                violatedFormObject,
                getFieldName(constraintViolation),
                ObjectUtil.getFieldValue(constraintViolation.getLeafBean(), getFieldName(constraintViolation)),
                constraintViolation.getConstraintDescriptor().getAnnotation().annotationType());
    }



    /**
     * returns last property path name
     */
    private String getFieldName(@NonNull ConstraintViolation<?> constraintViolation) {
        return ListUtil.iteratorToStream(constraintViolation.getPropertyPath().iterator())
                .reduce((_, second) -> second)
                .orElseThrow()
                .getName();
    }
}

package cz.kpsys.portaro.form.validation;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.formannotation.annotations.validation.IntegrityValidation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validator;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class IntegrityValidationGroupAdhocValidator implements AdhocValidator {

    @NonNull Validator validator;

    @Override
    public <FORM_OBJECT> FORM_OBJECT validate(@NonNull FORM_OBJECT formObject, @NonNull Department currentDepartment) throws ConstraintViolationException {
        var constraintViolations = validator.validate(formObject, IntegrityValidation.class);
        if (!constraintViolations.isEmpty()) {
            throw new ConstraintViolationException(constraintViolations);
        }
        return formObject;
    }

}

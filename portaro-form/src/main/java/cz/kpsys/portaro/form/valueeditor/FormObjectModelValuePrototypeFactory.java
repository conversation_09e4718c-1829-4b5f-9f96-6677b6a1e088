package cz.kpsys.portaro.form.valueeditor;

import cz.kpsys.portaro.commons.util.AnnotationUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.lang.reflect.Constructor;
import java.lang.reflect.Type;
import java.util.Map;
import java.util.UUID;
import java.util.function.Supplier;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FormObjectModelValuePrototypeFactory {

    @NonNull ValueEditorPrototypeProvider valueEditorPrototypeResolverProvider;

    @NonNull Map<Type, Supplier<Object>> predefinedCreators = Map.of(
            UUID.class, () -> ""
    );

    @SneakyThrows
    public Object getPrototypeFromType(Object formObject, String fieldName) {
        Type annotatedType = AnnotationUtil.getAnnotatedType(formObject, fieldName)
                .orElseThrow(() -> new RuntimeException("Cannot create prototype for %s.%s item - item is probably not anotated".formatted(formObject.getClass().getSimpleName(), fieldName)));

        if (predefinedCreators.containsKey(annotatedType)) {
            return predefinedCreators.get(annotatedType).get();
        }

        Assert.isInstanceOf(Class.class, annotatedType, "Cannot create prototype for %s.%s item - annotated item type is not a class".formatted(formObject.getClass().getSimpleName(), fieldName));
        Constructor<?> constructor;

        try {
            constructor = ((Class<?>) annotatedType).getConstructor();
        } catch (NoSuchMethodException e) {
            throw new RuntimeException("Class does not have default constructor, use parameter ‘prototypeBeanName‘ in editor annotation");
        }

        return constructor.newInstance();
    }

    public Object getPrototypeFromBeanName(String prototypeBeanName, Object formObject) {
        return valueEditorPrototypeResolverProvider.getPrototype(prototypeBeanName, formObject);
    }
}

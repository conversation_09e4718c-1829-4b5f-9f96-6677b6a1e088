package cz.kpsys.portaro.form.valueeditor.bool;

import cz.kpsys.portaro.form.valueeditor.ValueEditorValidations;
import cz.kpsys.portaro.form.validation.ValueEditorAsyncValidation;
import lombok.Value;
import lombok.With;

import java.util.Optional;

@With
@Value
public class BooleanValueEditorValidations implements ValueEditorValidations {

    public static BooleanValueEditorValidations getEmptyValidations() {
        return new BooleanValueEditorValidations(null, null, null, null);
    }

    Boolean required;

    ValueEditorAsyncValidation async;

    Boolean assertTrue;

    Boolean assertFalse;

    @Override
    public Optional<Boolean> getRequired() {
        return Optional.ofNullable(required);
    }

    @Override
    public Optional<ValueEditorAsyncValidation> getAsync() {
        return Optional.ofNullable(async);
    }

    public Optional<Boolean> getAssertTrue() {
        return Optional.ofNullable(assertTrue);
    }

    public Optional<Boolean> getAssertFalse() {
        return Optional.ofNullable(assertFalse);
    }

}

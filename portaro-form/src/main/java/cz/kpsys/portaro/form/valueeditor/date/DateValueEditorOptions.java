package cz.kpsys.portaro.form.valueeditor.date;

import cz.kpsys.portaro.form.valueeditor.ValueEditorOptions;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.date.DateValueEditorGranularity;
import lombok.Value;
import lombok.With;

import java.util.Optional;

@With
@Value
public class DateValueEditorOptions implements ValueEditorOptions {

    public static DateValueEditorOptions getEmptyOptions() {
        return new DateValueEditorOptions(null, null, null);
    }

    DateValueEditorGranularity maximumGranularity;

    String viewFormat;

    Boolean onlyDate;

    public Optional<DateValueEditorGranularity> getMaximumGranularity() {
        return Optional.ofNullable(maximumGranularity);
    }

    public Optional<String> getViewFormat() {
        return Optional.ofNullable(viewFormat);
    }

    public Optional<Boolean> getOnlyDate() {
        return Optional.ofNullable(onlyDate);
    }
}

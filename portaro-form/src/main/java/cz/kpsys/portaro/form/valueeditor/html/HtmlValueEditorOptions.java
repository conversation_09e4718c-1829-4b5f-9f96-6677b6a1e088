package cz.kpsys.portaro.form.valueeditor.html;

import cz.kpsys.portaro.form.valueeditor.ValueEditorOptions;
import lombok.Value;
import lombok.With;

import java.util.Optional;

@With
@Value
public class HtmlValueEditorOptions  implements ValueEditorOptions {

    Object editorOptions;

    Boolean sanitizeInput;

    public Optional<Object> getEditorOptions() {
        return Optional.ofNullable(editorOptions);
    }

    public Optional<Boolean> getSanitizeInput() {
        return Optional.ofNullable(sanitizeInput);
    }
}

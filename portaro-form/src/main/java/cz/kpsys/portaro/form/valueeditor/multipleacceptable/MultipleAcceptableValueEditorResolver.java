package cz.kpsys.portaro.form.valueeditor.multipleacceptable;

import cz.kpsys.portaro.commons.util.AnnotationUtil;
import cz.kpsys.portaro.commons.util.NumberUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.valueeditor.AnnotationsAwareValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import cz.kpsys.portaro.form.valueeditor.ValueEditorDescriptionResolver;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.EditorOptions;
import cz.kpsys.portaro.form.valueeditor.AcceptableValuesProvider;
import cz.kpsys.portaro.form.validation.ValidationsResolver;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.multipleacceptable.MultipleAcceptableEditor;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Optional;

import static cz.kpsys.portaro.formannotation.constants.ValueEditorsConstants.DEFAULT_SWITCH_TO_INLINE_MODE_THRESHOLD;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MultipleAcceptableValueEditorResolver implements AnnotationsAwareValueEditorResolver {

    @NonNull ValidationsResolver validationsResolver;
    @NonNull AcceptableValuesProvider<Department> acceptableValuesProvider;

    @Override
    public Optional<MultipleAcceptableValueEditor<?>> resolve(Object formObject, String fieldName, Department currentDepartment) {
        return resolveEditor(formObject, fieldName, currentDepartment, false);
    }

    @Override
    public Optional<MultipleAcceptableValueEditor<?>> resolveByTypeAnnotation(Object formObject, String fieldName, Department currentDepartment) {
        return resolveEditor(formObject, fieldName, currentDepartment, true);
    }

    private Optional<MultipleAcceptableValueEditor<?>> resolveEditor(Object formObject, String fieldName, Department currentDepartment, boolean searchInTypeAnnotation) {
        if (!AnnotationUtil.hasAnnotationIncludingTypeAnnotations(formObject, fieldName, MultipleAcceptableEditor.class, searchInTypeAnnotation)) {
            return Optional.empty();
        }

        MultipleAcceptableEditor annotation = AnnotationUtil.findAnnotationIncludingTypeAnnotations(formObject, fieldName, MultipleAcceptableEditor.class, searchInTypeAnnotation).orElseThrow();

        MultipleValueEditorOptions<Object> acceptableValueEditorOptions = MultipleValueEditorOptions
                .getEmptyOptions(acceptableValuesProvider.getAcceptableValues(annotation.valuesSourceBean(), formObject, currentDepartment));

        int showFirstCount = annotation.showFirstCount();
        boolean visibleIfSingleValue = annotation.visibleIfSingleValue();
        int switchToInlineModeThreshold = annotation.switchToInlineModeThreshold();
        boolean emptyAsNull = annotation.emptyAsNull();

        EditorOptions editorOptions = annotation.editorOptions();

        acceptableValueEditorOptions = acceptableValueEditorOptions
                .withShowFirstCount(NumberUtil.nullIfZero(showFirstCount))
                .withSwitchToInlineModeThreshold(switchToInlineModeThreshold == DEFAULT_SWITCH_TO_INLINE_MODE_THRESHOLD ? null : switchToInlineModeThreshold)
                .withEmptyAsNull(emptyAsNull);

        MultipleAcceptableValueEditor<Object> acceptableValueEditor = MultipleAcceptableValueEditor
                .getEmptyEditor(List.of())
                .withEditorName(fieldName)
                .withOptions(acceptableValueEditorOptions)
                .withVisibleIfSingleValue(visibleIfSingleValue);

        acceptableValueEditor = validationsResolver.processCommonValueEditorValidations(formObject, fieldName, acceptableValueEditor, searchInTypeAnnotation);

        acceptableValueEditor = ValueEditor.fillValueEditorWithOptions(acceptableValueEditor, editorOptions);

        acceptableValueEditor = ValueEditorDescriptionResolver.processDescription(acceptableValueEditor, formObject, fieldName, searchInTypeAnnotation);

        return Optional.of(acceptableValueEditor);
    }
}

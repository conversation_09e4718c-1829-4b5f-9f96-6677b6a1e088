package cz.kpsys.portaro.loan.ill;

import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import lombok.NonNull;

import java.time.Instant;

public record LaterSendDateAcceptCondition(

        @NonNull
        Instant sendDate

) implements AcceptCondition {

    @Override
    public @NonNull Text text() {
        return MultiText.ofTexts(SeekingProvisionAcceptConditionType.LATER_SEND_DATE.getText(), Texts.ofDateWithoutTime(sendDate)).withDelimiter(": ");
    }
}

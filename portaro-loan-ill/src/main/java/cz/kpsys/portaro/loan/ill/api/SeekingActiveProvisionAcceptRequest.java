package cz.kpsys.portaro.loan.ill.api;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualFunction;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.validation.AfterIntegrityValidationViolation;
import cz.kpsys.portaro.formannotation.annotations.validation.IntegrityValidation;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.singleacceptable.SingleAcceptableEditor;
import cz.kpsys.portaro.loan.ill.Seeking;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.With;
import lombok.experimental.FieldDefaults;

@Form(id = "seekingActiveProvisionAccept")
@FormSubmit(path = SeekingApiController.ACCEPT_ACTIVE_PROVISION_PATH)
@AfterIntegrityValidationViolation(bean = "seekingActiveProvisionAcceptRequestDefaulter")
@With
public record SeekingActiveProvisionAcceptRequest(

        @Schema(implementation = String.class, example = Seeking.SCHEMA_EXAMPLE_ID)
        @NotNull
        Seeking id,

        @Schema(implementation = Integer.class, example = Department.SCHEMA_EXAMPLE_ID)
        @FormPropertyLabel("Zpracovávat na")
        @SingleAcceptableEditor(valuesSourceBean = "editableDepartmentsAuthenticatedContextualProvider", switchToInlineModeThreshold = 3, visibleIfSingleValue = true)
        @NotNull(groups = IntegrityValidation.class)
        Department department

) {

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class SeekingActiveProvisionAcceptRequestDefaulter implements TypedAuthenticatedContextualObjectModifier<SeekingActiveProvisionAcceptRequest> {

        @NonNull AuthenticatedContextualFunction<Seeking, Department, Department> processDepartmentDefaultContextualProvider;

        @Override
        public SeekingActiveProvisionAcceptRequest modify(SeekingActiveProvisionAcceptRequest request, Department ctx, UserAuthentication currentAuth) {
            Seeking seeking = request.id();
            if (request.department() == null) {
                request = request.withDepartment(processDepartmentDefaultContextualProvider.getOn(seeking, currentAuth, ctx));
            }
            return request;
        }
    }
}

package cz.kpsys.portaro.loan.ill.process;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.loan.ill.Seeking;
import lombok.NonNull;
import lombok.With;

import java.time.Instant;

@With
public record SeekedSeekingEditationCommand(

        @NonNull
        Seeking seeking,

        @NonNull
        Boolean eventuallyPhotocopyable,

        @NonNull
        Boolean eventuallyReservable,

        @NonNull
        Boolean eventuallyOnSiteLendable,

        @NonNull
        Boolean eventuallyAbroadDeliverable,

        @NullableNotBlank
        String note,

        @NullableNotBlank
        String seekerReferenceId,

        @NonNull
        Instant requesterObtainDeadlineDate

) {}

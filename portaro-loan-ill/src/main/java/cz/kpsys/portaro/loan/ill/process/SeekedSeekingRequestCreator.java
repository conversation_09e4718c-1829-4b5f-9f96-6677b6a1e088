package cz.kpsys.portaro.loan.ill.process;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.DataAccessException;
import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.loan.ill.IllSeekingProvisionList;
import cz.kpsys.portaro.loan.ill.Seeking;
import cz.kpsys.portaro.loan.ill.SeekingDesiredExemplar;
import cz.kpsys.portaro.loan.request.LoanRequestItem;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.user.Institution;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.Instant;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SeekedSeekingRequestCreator implements Saver<SeekedSeekingRequestCreationCommand, Seeking> {

    @NonNull ContextualProvider<Department, @NonNull Integer> seekingIdGenerator;
    @NonNull ContextualProvider<Department, Institution> nullableInstitutionContextualProvider;
    @NonNull Saver<Seeking, ?> seekingSaver;
    @NonNull RecordEditationFactory recordEditationFactory;

    @Transactional
    @Override
    public @NonNull Seeking save(@NonNull SeekedSeekingRequestCreationCommand command) throws DataAccessException {
        Assert.state(command.getItems().size() == 1, "Only one item in seeked seeking tentative request creation is allowed");

        command = saveRecordIfNotPersisted(command);

        Seeking seeking = createSeeking(command);
        seekingSaver.save(seeking);
        return seeking;
    }

    private SeekedSeekingRequestCreationCommand saveRecordIfNotPersisted(@NonNull SeekedSeekingRequestCreationCommand command) {
        Record document = command.getSingleDocument();
        if (!DataUtils.isPersistedId(document.getKindedId())) {
            RecordEditation editation = recordEditationFactory
                    .on(command.getCtx())
                    .ofExisting(document)
                    .build(command.getCurrentAuth());
            editation.saveIfModified(command.getCtx(), command.getCurrentAuth());
            command = command.withItems(List.of(new LoanRequestItem(editation.getRecord())));
        }
        return command;
    }

    private Seeking createSeeking(SeekedSeekingRequestCreationCommand command) {
        Integer seekingId = seekingIdGenerator.getOn(command.getCtx());

        return new Seeking(
                seekingId,
                seekingId,
                false,
                command.getRequester(),
                getMeAsSeeker(command.getCtx()),
                null,
                new SeekingDesiredExemplar(
                        command.getSingleDocument(),
                        command.getVolume(),
                        command.getPage(),
                        command.getArticle()
                ),
                command.isEventuallyPhotocopyable(),
                command.isEventuallyReservable(),
                command.isEventuallyOnSiteLendable(),
                command.isEventuallyAbroadDeliverable(),
                command.getNote(),
                null,
                command.getRequesterObtainDeadlineDate(),
                command.getCtx(),
                Instant.now(),
                null,
                false,
                null,
                IllSeekingProvisionList.ofEmpty(seekingId)
        );
    }

    @Nullable
    private Institution getMeAsSeeker(Department ctx) {
        return nullableInstitutionContextualProvider.getOn(ctx);
    }

}

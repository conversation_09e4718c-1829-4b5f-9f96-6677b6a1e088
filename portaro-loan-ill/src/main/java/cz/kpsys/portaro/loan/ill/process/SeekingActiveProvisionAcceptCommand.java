package cz.kpsys.portaro.loan.ill.process;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.loan.ill.AcceptCondition;
import cz.kpsys.portaro.loan.ill.Seeking;
import jakarta.annotation.Nullable;
import lombok.NonNull;

import java.time.Instant;

public record SeekingActiveProvisionAcceptCommand(

        @NonNull
        Seeking seeking,

        @NonNull
        Department department,

        @Nullable
        AcceptCondition acceptCondition,

        @NonNull
        Instant date,

        @NonNull
        Department ctx

) {}

package cz.kpsys.portaro.loan.ill.process;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.DataAccessException;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.loancategory.LoanCategory;
import cz.kpsys.portaro.finance.Price;
import cz.kpsys.portaro.loan.ill.IllSeekingProvisionList;
import cz.kpsys.portaro.loan.ill.SeekingProvision;
import cz.kpsys.portaro.loan.resolving.LoanItem;
import cz.kpsys.portaro.loan.resolving.LoanRulePriceResolver;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserByBasicUserLoader;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.annotation.Transactional;

import java.util.function.Consumer;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SeekingProvisionCreator implements Consumer<SeekingProvisionCreationCommand> {

    @NonNull Saver<IllSeekingProvisionList, IllSeekingProvisionList> seekingProvisionListSaver;
    @NonNull UserByBasicUserLoader userLoader;
    @NonNull LoanRulePriceResolver loanPriceResolver;
    @NonNull ContextualProvider<Department, @NonNull LoanCategory> defaultIllLoanCategoryProvider;

    @Transactional
    @Override
    public void accept(@NonNull SeekingProvisionCreationCommand command) throws DataAccessException {
        command.seeking().canCreateProvision().throwIfCannot();

        IllSeekingProvisionList provisionList = command.seeking().getProvisionList();
        provisionList.addPendingProvision(createNewSeekingProvision(command));
        seekingProvisionListSaver.save(provisionList);
    }

    @NonNull
    private SeekingProvision createNewSeekingProvision(@NonNull SeekingProvisionCreationCommand command) {
        User requester = userLoader.getUser(command.seeking().getRequester());
        ReaderRole requesterReader = requester.roleStreamOn(ReaderRole.class, command.ctx())
                .findFirst().orElseThrow();
        Price loanPrice = loanPriceResolver.resolveOrZero(requester, requesterReader,
                        new LoanItem(defaultIllLoanCategoryProvider.getOn(command.ctx())));
        return SeekingProvision.createNew(
                command.id(),
                command.seeking().getId(),
                command.provider(),
                command.deliveryChannel(),
                loanPrice
        );
    }

}

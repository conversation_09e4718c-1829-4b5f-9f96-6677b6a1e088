package cz.kpsys.portaro.loan;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Set;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum LoanState implements LabeledIdentified<String> {

    /**
     * Rezervace v poradi
     */
    ON_WAITING_LIST(Texts.ofMessageCoded("loan.state.OnWaitingList"), Set.of()),
    
    /**
     * Neodeslaná rezervace
     */
    UNSENT_RESERVATION(Texts.ofMessageCoded("loan.state.UnsentReservation"), Set.of(
            ON_WAITING_LIST
    )),
    
    /**
     * Odeslana rezervace
     */
    SENT_RESERVATION(Texts.ofMessageCoded("loan.state.SentReservation"), Set.of(
            UNSENT_RESERVATION
    )),
    
    /**
     * Nevyrizena objednavka
     */
    UNPROCESSED_ORDER(Texts.ofMessageCoded("loan.state.UnprocessedOrder"), Set.of(
            UNSENT_RESERVATION
    )),
    
    /**
     * Vyrizena objednavka
     */
    PROCESSED_ORDER(Texts.ofMessageCoded("loan.state.ProcessedOrder"), Set.of(
            UNPROCESSED_ORDER
    )),

    /**
     * Pripravena pasivni MVS (cekajici na vyzvednuti ctenarem)
     */
    PICKUP_READY_SEEKED_FOREIGN_EXEMPLAR(Texts.ofMessageCoded("loan.state.PickupReadySeekedForeignExemplar"), Set.of()),
    
    /**
     * Vypujcka
     */
    LENT(Texts.ofMessageCoded("loan.state.Lent"), Set.of(
            SENT_RESERVATION,
            PROCESSED_ORDER,
            PICKUP_READY_SEEKED_FOREIGN_EXEMPLAR
    )),
    
    /**
     * Vracena vypujcka
     */
    RETURNED(Texts.ofMessageCoded("loan.state.Returned"), Set.of(
            LENT
    )),
    
    /**
     * Zrusena rezervace
     */
    CANCELLED_RESERVATION(Texts.ofMessageCoded("loan.state.Cancelled"), Set.of(
            ON_WAITING_LIST,
            UNSENT_RESERVATION,
            SENT_RESERVATION,
            UNPROCESSED_ORDER,
            PROCESSED_ORDER,
            PICKUP_READY_SEEKED_FOREIGN_EXEMPLAR
    ));


    public static final Codebook<LoanState, String> CODEBOOK = new StaticCodebook<>(values());

    @NonNull String id = this.name();
    @NonNull Text text;

    @Getter(AccessLevel.NONE) @NonNull Set<LoanState> possiblePreviousStates;

    public Integer toCisReze() {
        return switch (this) {
            case LENT -> Loan.CIS_REZE_LOAN;
            case UNSENT_RESERVATION -> Loan.CIS_REZE_UNSENT_RESERVATION;
            case SENT_RESERVATION -> Loan.CIS_REZE_SENT_RESERVATION;
            case UNPROCESSED_ORDER -> Loan.CIS_REZE_UNPROCESSED_ORDER;
            case PROCESSED_ORDER -> Loan.CIS_REZE_PROCESSED_ORDER;
            case PICKUP_READY_SEEKED_FOREIGN_EXEMPLAR -> Loan.CIS_REZE_PICKUP_READY_SEEKED_FOREIGN_EXEMPLAR;
            default -> throw new IllegalArgumentException("Cannot convert LoanState(%s) to Integer (CIS_REZE)".formatted(this));
        };
    }

    public boolean isEqualOrAfter(LoanState other) {
        if (this == other) {
            return true;
        }
        return isAfter(other);
    }

    public boolean isAfter(LoanState other) {
        if (this == other) {
            return false;
        }
        for (LoanState previousState : possiblePreviousStates) {
            if (previousState == other) {
                return true;
            }
            if (previousState.isAfter(other)) {
                return true;
            }
        }
        return false;
    }

    public boolean isBefore(LoanState other) {
        return other.isAfter(this);
    }


    public static List<LoanState> allWaiting() {
        return List.of(
                ON_WAITING_LIST,
                UNSENT_RESERVATION,
                SENT_RESERVATION,
                UNPROCESSED_ORDER,
                PROCESSED_ORDER,
                PICKUP_READY_SEEKED_FOREIGN_EXEMPLAR
        );
    }

    public static List<LoanState> allExemplarAssignedActive() {
        return List.of(
                UNPROCESSED_ORDER,
                PROCESSED_ORDER,
                SENT_RESERVATION,
                UNSENT_RESERVATION,
                PICKUP_READY_SEEKED_FOREIGN_EXEMPLAR,
                LENT
        );
    }

    public boolean isFinished() {
        return List.of(
                RETURNED,
                CANCELLED_RESERVATION
        ).contains(this);
    }

    public static List<LoanState> allNonRelic() {
        return List.of(
                ON_WAITING_LIST,
                UNSENT_RESERVATION,
                SENT_RESERVATION,
                UNPROCESSED_ORDER,
                PROCESSED_ORDER,
                PICKUP_READY_SEEKED_FOREIGN_EXEMPLAR,
                LENT
        );
    }

    public static List<LoanState> toBeProcessed() {
        return List.of(UNPROCESSED_ORDER, UNSENT_RESERVATION);
    }

    public static List<LoanState> readyToPickup() {
        return List.of(SENT_RESERVATION, PROCESSED_ORDER, PICKUP_READY_SEEKED_FOREIGN_EXEMPLAR);
    }

    public static List<LoanState> unavailableForLending() {
        return List.of(LENT, SENT_RESERVATION, PROCESSED_ORDER, PICKUP_READY_SEEKED_FOREIGN_EXEMPLAR);
    }

    public static List<LoanState> processedOrReadyToProcess() {
        return List.of(
                UNSENT_RESERVATION,
                SENT_RESERVATION,
                UNPROCESSED_ORDER,
                PROCESSED_ORDER,
                PICKUP_READY_SEEKED_FOREIGN_EXEMPLAR
        );
    }

    public static List<LoanState> notReadyWaiting() {
        return ListUtil.removeAll(allWaiting(), readyToPickup());
    }

    public static List<LoanState> renewable() {
        return List.of(LENT);
    }

}

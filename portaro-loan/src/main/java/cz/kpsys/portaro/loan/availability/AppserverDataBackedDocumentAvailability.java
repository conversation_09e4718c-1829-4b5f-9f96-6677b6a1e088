package cz.kpsys.portaro.loan.availability;

import cz.kpsys.portaro.appserver.AppserverError;
import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Labeled;
import cz.kpsys.portaro.commons.object.Ordered;
import cz.kpsys.portaro.commons.util.DateUtils;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.web.Link;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.loan.LoanSetting;
import cz.kpsys.portaro.loan.availability.appserver.DocumentAvailabilityAppserverData;
import cz.kpsys.portaro.loan.availability.appserver.VisitOrderCapabilityAppserverData;
import cz.kpsys.portaro.loan.availability.capability.*;
import cz.kpsys.portaro.user.User;
import lombok.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.*;

public class AppserverDataBackedDocumentAvailability extends AbstractKnownAvailability implements DocumentAvailability, Serializable, Identified<Object> {

    private static final Map<Integer, String> MESSAGE_CODES = Map.of(
            DocumentAvailabilityAppserverData.ERR_NUM_NOT_EXISTING_RECORD_ID, "loan.ZaznamSTimtoIdNeexistuje",
            DocumentAvailabilityAppserverData.ERR_NUM_BLOCKED_TRANSACTIONS, "loan.CtenarMaBlokovanyTransakce",
            DocumentAvailabilityAppserverData.ERR_NUM_EXCLUDED_OR_DELETED_READER, "loan.CtenarJeVyrazenNeboSmazan",
            DocumentAvailabilityAppserverData.ERR_NUM_READER_REGISTRATION_EXCEEDED, "loan.NelzeVypujcitProslaRegistrace"
    );

    private final DocumentAvailabilityAppserverData data;
    private Integer reservationsInQueueCount;
    private Integer sentOrUnsentReservationsCount;
    private Date lastUserLoanDate = null;
    private Date lastReturnDate = null;
    private List<Link> downloadLinks = new ArrayList<>();



    public AppserverDataBackedDocumentAvailability(@NonNull String id, @NonNull LoanSetting loanSetting, @NonNull User requester, @Nullable DocumentAvailabilityAppserverData data) {
        super(id, loanSetting, requester);
        sentOrUnsentReservationsCount = 0;
        this.data = data;
    }





    public static String createId(UUID recordId, Integer userId, DateRange dateRange) {
        return String.format("%s:%s:%s:%s",
                recordId,
                userId,
                dateRange.fromDate() == null ? "-" : dateRange.fromDate().getEpochSecond(),
                dateRange.toDate() == null ? "-" : dateRange.toDate().getEpochSecond());
    }


    @Override
    public Text getText() {
        //TODO upravit hlasky - obtainable by melo znamenat ze lze polozku nejak ziskat (ale i napr. rezervaci)
        return Texts.ofMessageCoded(isObtainable() ? "loan.Available" : "loan.Unavailable");
    }



    /**
     * Zda ma dostupnost smysl. <br/>
     * Napriklad, je-li to dostupnost u periodika a pouziva se nas system
     * pujcovani, smysl to nema. Pokud se jedna o periodika a pouziva se
     * pujcovani pres email, smysl to ma.
     */
    @Override
    public boolean isReasonable() {
        if (!getCapabilities().isEmpty()) {
            return true;
        }
        return data != null && loanSetting().isEnabled();
    }



    /**
     * Vrati zda je dokument/exemplar volny (tzn zda neni rezervovany ani
     * vypujceny a zaroven je vypujcitelny at uz absencne nebo prezencne).
     */
    @Override
    public boolean isFree() {
        return getFreeItemsCount() > 0;
    }



    /**
     * Pocet exmplaru, ktere jsou volne, prezencne nebo absencne.
     */
    @Override
    public Integer getFreeItemsCount() {
        if (data == null || data.getAbs().getObtainableItemsCount().isEmpty() || data.getPrez().getObtainableItemsCount().isEmpty()) {
            return -1;
        }
        return data.getAbs().getFreeItemsCount().get() + data.getPrez().getFreeItemsCount().get();
    }



    @Override
    public Integer getFreeTakeawayItemsCount() {
        return getFreeItemsCount() - getFreeVisitableItemsCount();
    }



    /**
     * Pocet exemplaru, ktere lze jakymkoliv zpusobem (vyzvednutim, objednavkou, rezervaci)
     * vypujcit at uz abs. nebo prez. <br/>
     * Tato hodnota se pouziva v zobrazeni "prezencne X a absencne Y z
     * [obtainableItemsCount] exemplaru". <br/>
     * Je-li zaporne, informaci nezname.
     */
    @Override
    public Integer getObtainableItemsCount() {
        if (data == null || data.getAbs().getObtainableItemsCount().isEmpty() || data.getPrez().getObtainableItemsCount().isEmpty()) {
            return -1;
        }
        int obtainableItemsCount = data.getAbs().getObtainableItemsCount().get() + data.getPrez().getObtainableItemsCount().get();
        Assert.state(obtainableItemsCount >= getFreeItemsCount(), "obtainableItemsCount must be gt or equal than freeItemsCount");
        return obtainableItemsCount;
    }



    /**
     * pocet rezervaci na dokument, ktere jsou ve fronte (nejsou v neodeslanych a odeslanych reze.)
     */
    @Override
    public Integer getReservationsInQueueCount() {
        return reservationsInQueueCount;
    }



    @Override
    public Integer getReservationsCount() {
        return reservationsInQueueCount + sentOrUnsentReservationsCount;
    }



    @Override
    public Integer getFreeVisitableItemsCount() {
        if (data == null || data.getPrez().getFreeItemsCount().isEmpty()) {
            return -1;
        }
        return data.getPrez().getFreeItemsCount().get();
    }






    public void setSentOrUnsentReservationsCount(Integer sentOrUnsentReservationsCount) {
        this.sentOrUnsentReservationsCount = sentOrUnsentReservationsCount;
    }



    public void setReservationsInQueueCount(Integer reservationsInQueueCount) {
        this.reservationsInQueueCount = reservationsInQueueCount;
    }


    void setLastReturnDate(Date lastReturnDate) {
        this.lastReturnDate = lastReturnDate;
    }



    public void setDownloadLinks(List<Link> downloadLinks) {
        this.downloadLinks = ListUtil.notNullList(downloadLinks);
    }





    @Override
    public Set<Capability> getCapabilities() {
        Set<Capability> res = new LinkedHashSet<>();

        //mvs - knihovna
        if (data != null && getMvsLoanRequestPermission().canOrAfterUpgradeOrMaybeAfterLogin()) {
            boolean loginRequired = getMvsLoanRequestPermission() == Permission.MAYBE_AFTER_LOGIN;

            // aktivni mvs
            if (data.getAbs().hasAnyPickupOrOrderDepartment() || data.getPrez().hasAnyPickupOrOrderDepartment()) {
                res.add(new SeekingProvisionImmediatelyProvidability(loginRequired));
            } else if (data.getAbs().hasAnyReserveDepartment() || data.getPrez().hasAnyReserveDepartment()) {
                res.add(new SeekingProvisionReservability(loginRequired));
            }
        }

        //vyzvedavani
        if (data != null && loanSetting().isPickingEnabled() && getPickupLoanRequestPermitted().canOrAfterUpgradeOrMaybeAfterLogin()) {
            boolean loginRequired = getPickupLoanRequestPermitted() == Permission.MAYBE_AFTER_LOGIN;

            //abs
            if (!data.getAbs().getBuildingsToPickupOn().isEmpty()) {
                res.add(new Pickupability(loginRequired, data.getAbs().getBuildingsToPickupOn()));
            }
            //prez pro budovy, ktere nejsou v abs
            List<Department> presPickupBuildings = ListUtil.removeAll(data.getPrez().getBuildingsToPickupOn(), data.getAbs().getBuildingsToPickupOn());
            if (!presPickupBuildings.isEmpty()) {
                res.add(new VisitPickupability(loginRequired, presPickupBuildings));
            }
        }

        //standardne - ctenar + anonym
        if (data != null && getStandardLoanRequestPermission().canOrAfterUpgradeOrMaybeAfterLogin()) {
            boolean loginRequired = getStandardLoanRequestPermission() == Permission.MAYBE_AFTER_LOGIN;

            //objednavani
            if (loanSetting().isStandardOrderingEnabled()) {
                //abs
                if (data.getAbs().hasAnyOrderDepartment()) {
                    res.add(new StandardOrderability(loginRequired, data.getAbs().getBuildingsToOrderOn()));
                }
                //prez pro budovy, ktere nejsou v abs
                List<Department> presOrderBuildings = ListUtil.removeAll(data.getPrez().getBuildingsToOrderOn(), data.getAbs().getBuildingsToOrderOn());
                if (!presOrderBuildings.isEmpty()) {
                    res.add(new StandardVisitOrderability(loginRequired, presOrderBuildings));
                }
            }

            //rezervace
            if (loanSetting().isStandardReservingEnabled()) {
                //abs
                if (data.getAbs().hasAnyReserveDepartment()) {
                    res.add(new StandardReservability(loginRequired, data.getAbs().getBuildingsToReserveOn()));
                }
                //prez pro budovy, ktere nejsou v abs
                List<Department> presReserveBuildings = ListUtil.removeAll(data.getPrez().getBuildingsToReserveOn(), data.getAbs().getBuildingsToReserveOn());
                if (!presReserveBuildings.isEmpty()) {
                    res.add(new StandardVisitReservability(loginRequired, presReserveBuildings));
                }
            }
        }

        //slotove
        if (data != null && data.getSlots().enabled()) {
            //objednavani
            if (loanSetting().isStandardSlotOrderingEnabled()) {
                List<Department> departments = data.getSlots().slots().stream()
                        .flatMap(slotAppserverData -> slotAppserverData.getCapabilities().stream().map(VisitOrderCapabilityAppserverData::department)) // zatim neresime spravne rozdeleni na departmenty
                        .distinct()
                        .toList();
                List<SlotVisitOrderability.TimeslotRequestability> slots = data.getSlots().slots().stream()
                        .map(slotAppserverData -> {
                            boolean orderCapable = !slotAppserverData.getCapabilities().isEmpty() && slotAppserverData.getCapabilities().stream().anyMatch(VisitOrderCapabilityAppserverData::capable);
                            Labeled nonCapableReason = orderCapable ? null : slotAppserverData.getCapabilities().stream()
                                    .map(VisitOrderCapabilityAppserverData::nonCapableReason)
                                    .filter(Objects::nonNull)
                                    .findFirst()
                                    .orElse(null);
                            return new SlotVisitOrderability.TimeslotRequestability(slotAppserverData.getId(), slotAppserverData.getPeriod(), orderCapable, nonCapableReason);
                        })
                        .toList();
                res.add(new SlotVisitOrderability(true, departments, slots));
            }
        }

        //mailem - ctenar + anonym
        if (data != null && getMailLoanRequestPermission().canOrAfterUpgradeOrMaybeAfterLogin()) {
            boolean loginRequired = getMailLoanRequestPermission() == Permission.MAYBE_AFTER_LOGIN;
            Assert.state(!loginRequired, "Mail requestability should not require login");

            if (loanSetting().isMailOrderingEnabled() && (data.getAbs().hasAnyOrderDepartment() || data.getPrez().hasAnyOrderDepartment() || loanSetting().isMailOrderableWhenNoExemplars())) {
                //objednavani mailem
                res.add(new MailRequestability(loginRequired));
            } else if (loanSetting().isMailReservingEnabled() && (data.getAbs().hasAnyReserveDepartment() || data.getPrez().hasAnyReserveDepartment())) {
                //rezervace mailem
                res.add(new MailRequestability(loginRequired));
            }
        }

        //prime stazeni se znamou url adresou
        if (!downloadLinks.isEmpty()) {
            res.add(new Downloadability(downloadLinks));
        }

        //externi sluzby
        if (data != null && getStandardLoanRequestPermission().canOrAfterUpgradeOrMaybeAfterLogin()) {
            data.getPalmknihyLoanability().ifPresent(res::add);
            data.getPalmknihyAudioLoanability().ifPresent(res::add);
            data.getPalmknihyPdfLoanability().ifPresent(res::add);
            data.getFlexibooksLoanability().ifPresent(res::add);
            data.getLevnaKnihovnaLoanability().ifPresent(res::add);
        }

        return Collections.unmodifiableSet(res);
    }


    public void setLastUserLoanDate(Date lastUserLoanDate) {
        this.lastUserLoanDate = lastUserLoanDate;
    }


    @Override
    public List<Department> freeItemsDepartments() {
        if (data == null) {
            return List.of();
        }
        var departments = ListUtil.union(
                data.getAbs().getBuildingsToOrderOn(),
                data.getAbs().getBuildingsToPickupOn(),
                data.getPrez().getBuildingsToPickupOn(),
                data.getPrez().getBuildingsToPickupOn()
        );

        return ListUtil.sort(departments, Ordered.COMPARATOR);
    }

    @Override
    public List<Department> reservableItemsDepartments() {
        if (data == null) {
            return List.of();
        }
        var departments = ListUtil.union(
                data.getAbs().getBuildingsToReserveOn(),
                data.getPrez().getBuildingsToReserveOn()
        );

        return ListUtil.sort(departments, Ordered.COMPARATOR);
    }


    public boolean isError() {
        if (data == null) {
            return false;
        }
        if (data.getError().isPresent() && data.getError().get().errorNumber() == DocumentAvailabilityAppserverData.ERR_NUM_BLOCKED_TRANSACTIONS) { //nebudeme hlasit, ze ma ctenar zablokovany transakce
            return false;
        }
        return data.getError().isPresent();
    }


    public int getErrorNumber() {
        if (data == null || data.getError().isEmpty()) {
            return -1;
        }
        return data.getError().get().errorNumber();
    }


    public Text getErrorText() {
        if (data == null || !isError() || data.getError().isEmpty()) {
            return null;
        }
        AppserverError error = data.getError().get();
        if (MESSAGE_CODES.containsKey(error.errorNumber())) {
            return Texts.ofMessageCoded(MESSAGE_CODES.get(error.errorNumber()));
        }
        return Texts.ofNative(error.errorMessage());
    }


    @Override
    public boolean isReturnedToday() {
        return lastReturnDate != null && DateUtils.isSameDay(lastReturnDate, new Date());
    }


    @Override
    public Date getLastUserLoanDate() {
        return lastUserLoanDate;
    }


    public DocumentAvailabilityAppserverData getAppserverData() {
        return data;
    }
}

package cz.kpsys.portaro.loan.availability.timeslot;

import cz.kpsys.portaro.loan.availability.resolver.BooleanCapabilityResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.time.Clock;
import java.time.Instant;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class PresentOrFutureSlotCapabilityResolver implements BooleanCapabilityResolver<SlottedCapabilityResolveCommand> {

    @NonNull @NonFinal Clock clock = Clock.systemUTC();

    public PresentOrFutureSlotCapabilityResolver withCustomClock(@NonNull Clock customClock) {
        this.clock = customClock;
        return this;
    }

    @Override
    public boolean resolve(@NonNull SlottedCapabilityResolveCommand command) {
        Instant slotEndDate = command.getDateRange().toDate();
        Instant now = Instant.now(clock);
        return slotEndDate.isAfter(now);
    }
}

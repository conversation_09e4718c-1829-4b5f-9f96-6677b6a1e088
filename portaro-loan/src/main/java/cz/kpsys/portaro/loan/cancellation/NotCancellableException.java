package cz.kpsys.portaro.loan.cancellation;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.commons.object.SeveritedException;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class NotCancellableException extends RuntimeException implements SeveritedException, UserFriendlyException {


    @Getter
    int severity = SEVERITY_WARNING;

    @Getter
    @NonNull
    Text text = Texts.ofMessageCoded("loan.CannotBeCancelled");

    public NotCancellableException(String message) {
        super(message);
    }
}

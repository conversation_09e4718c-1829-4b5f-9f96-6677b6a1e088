package cz.kpsys.portaro.loan.lending.command;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.Exemplar;
import cz.kpsys.portaro.user.BasicUser;
import lombok.NonNull;
import lombok.With;
import org.springframework.lang.Nullable;

import java.io.Serializable;

@With
public record InternalLendingCommand(

        @NonNull Exemplar exemplar,
        @NonNull BasicUser lender,
        @NonNull BasicUser actioner, // Ten, kdo provádí výpůjčku. Obvykle knihovník (nebo čtenář v případě self-checku).
        @NonNull Department department,
        @NonNull Department ctx,
        @NonNull UserAuthentication currentAuth,
        @NonNull Integer quantity,
        @Nullable String userPassword,
        @NonNull Boolean cancelShipmentItem,

        @Nullable Boolean ignoreAllForced,
        @Nullable Boolean ignoreUserHasDebt,
        @Nullable Boolean ignoreUserHasNotValidRegistration,
        @Nullable Boolean ignoreDocumentWasLoanedInPast,
        @Nullable Boolean ignoreSpecialInformationOnLocation,
        @Nullable Boolean ignoreSpecialInformationOnStatus,
        @Nullable Boolean ignoreSpecialInformationOnCategory,
        @Nullable Boolean ignoreLendingOnDifferentDepartment,
        @Nullable Boolean ignoreLendingOnDifferentLocation,
        @Nullable Boolean ignoreSpecialInformationOnHelperField,
        @Nullable Boolean ignoreDocumentHasAttachment,
        @Nullable Boolean ignoreOverdueNotices,
        @Nullable Boolean ignoreBlockedTransactions,
        @Nullable Boolean ignoreOtherUserUnprocessedOrder,
        @Nullable Boolean ignoreOtherUserUnsentReservation,
        @Nullable Boolean ignoreExceededLoanLimit,
        @Nullable Boolean ignoreExceededLoanLimitInCategory,
        @Nullable Boolean ignoreDocumentIsAlreadyLentBySameUser,
        @Nullable Boolean ignoreReaderHasReservationOnAnotherItem,
        @Nullable Boolean ignoreDocumentInBackwardCataloging


) implements Serializable, LendingCommand {

    public static InternalLendingCommand ofForced(
            @NonNull Exemplar exemplar,
            @NonNull BasicUser lender,
            @NonNull BasicUser actioner,
            @NonNull Department department,
            @NonNull Department ctx,
            @NonNull UserAuthentication currentAuth,
            @NonNull Integer quantity,
            boolean cancelShipmentItem) {
        return new InternalLendingCommand(
                exemplar,
                lender,
                actioner,
                department,
                ctx,
                currentAuth,
                quantity,
                null,
                cancelShipmentItem,
                true,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null
        );
    }

    public String designation() {
        return exemplar.getDesignation();
    }

}

package cz.kpsys.portaro.loan.lending.exceptions;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.conversation.FieldEnablingExceptionContinuation;
import cz.kpsys.portaro.exception.AdditionalFieldsException;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

import java.util.Map;

import static cz.kpsys.portaro.conversation.ConversationConstants.CONTINUATION_FIELD_NAME;
import static cz.kpsys.portaro.loan.lending.webapi.LendingRequest.Fields.ignoreDocumentWasLoanedInPast;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DocumentWasLoanedInPastResponseException extends RuntimeException implements SeveritedException, AdditionalFieldsException {

    String date;

    public DocumentWasLoanedInPastResponseException(String message, String date) {
        super(message);
        this.date = date;
    }

    @Override
    public int getSeverity() {
        return SEVERITY_WARNING;
    }

    @Override
    public Map<String, Object> getAdditionalFields() {
        return Map.of(
                "date", date,
                CONTINUATION_FIELD_NAME, new FieldEnablingExceptionContinuation(Texts.ofArgumentedMessageCoded("loan.OpravduVypujcitDokumentVypujcenVMinulostiNaposledyX", date), ignoreDocumentWasLoanedInPast));
    }
}

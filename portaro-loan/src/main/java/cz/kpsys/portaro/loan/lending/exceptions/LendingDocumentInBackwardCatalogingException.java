package cz.kpsys.portaro.loan.lending.exceptions;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.conversation.FieldEnablingExceptionContinuation;
import cz.kpsys.portaro.exception.AdditionalFieldsException;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

import java.util.Map;

import static cz.kpsys.portaro.conversation.ConversationConstants.CONTINUATION_FIELD_NAME;
import static cz.kpsys.portaro.loan.lending.webapi.LendingRequest.Fields.ignoreDocumentInBackwardCataloging;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class LendingDocumentInBackwardCatalogingException extends RuntimeException implements SeveritedException, AdditionalFieldsException {

    public LendingDocumentInBackwardCatalogingException(String message) {
        super(message);
    }

    @Override
    public int getSeverity() {
        return SEVERITY_WARNING;
    }

    @Override
    public Map<String, Object> getAdditionalFields() {
        return Map.of(CONTINUATION_FIELD_NAME, new FieldEnablingExceptionContinuation(Texts.ofMessageCoded("loan.OpravduVypujcitStatusJeZpetnaKatalogizace"), ignoreDocumentInBackwardCataloging));
    }
}

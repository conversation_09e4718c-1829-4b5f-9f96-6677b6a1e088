package cz.kpsys.portaro.loan.lending.exceptions;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.conversation.FieldEnablingExceptionContinuation;
import cz.kpsys.portaro.exception.AdditionalFieldsException;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

import java.util.Map;

import static cz.kpsys.portaro.conversation.ConversationConstants.CONTINUATION_FIELD_NAME;
import static cz.kpsys.portaro.loan.lending.webapi.LendingRequest.Fields.ignoreUserHasDebt;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class UserHasDebtResponseException extends RuntimeException implements SeveritedException, AdditionalFieldsException {

    Double debt;

    public UserHasDebtResponseException(String message, Double debt) {
        super(message);
        this.debt = debt;
    }

    @Override
    public int getSeverity() {
        return SEVERITY_WARNING;
    }

    @Override
    public Map<String, Object> getAdditionalFields() {
        return Map.of(
                "debt", debt,
                CONTINUATION_FIELD_NAME, new FieldEnablingExceptionContinuation(Texts.ofArgumentedMessageCoded("loan.OpravduVypujcitCtenarMaDluhX", debt), ignoreUserHasDebt)
        );
    }
}

package cz.kpsys.portaro.loan.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.User;

import java.io.Serializable;
import java.util.List;

public class StandardOrderRequestImpl extends StandardLoanRequestImpl implements StandardLoanRequest, Serializable {

    public StandardOrderRequestImpl(@JsonProperty(PROPERTY_REQUESTER) User requester,
                                    @JsonProperty(PROPERTY_ITEMS) List<LoanRequestItem> items) {
        super(requester, items);
    }

    public static final String PROPERTY_DESIRED_BUILDING = "desiredBuilding";

    public Department getDesiredBuilding() {
        return ListUtil.firstOrNull(getDesiredBuildings());
    }

    public void setDesiredBuilding(Department desiredBuilding) {
        setDesiredBuildings(List.of(desiredBuilding));
    }
}

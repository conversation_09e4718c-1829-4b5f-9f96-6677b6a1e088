package cz.kpsys.portaro.messages.db.entity;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.messages.dto.MessageSendingEmailAddress;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.MessageDb.MESSAGE_SENDING_EMAIL_ADDRESS.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class MessageSendingEmailAddressEntity implements Identified<UUID> {

    @Id
    @Column(name = ID)
    @EqualsAndHashCode.Include
    @NonNull
    UUID id;

    @Column(name = MESSAGE_SENDING_ID)
    @NonNull
    UUID messageSendingId;

    @Column(name = EMAIL_ADDRESS)
    @Size(min = 1, max = MessageSendingEmailAddress.EMAIL_ADDRESS_MAX_LENGTH)
    @NonNull
    @NotBlank
    String emailAddress;

    @Column(name = ADDRESS_TYPE)
    @Size(min = 1, max = 50)
    @NonNull
    @NotBlank
    String addressType;

}

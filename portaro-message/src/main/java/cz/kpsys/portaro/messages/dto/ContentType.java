package cz.kpsys.portaro.messages.dto;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum ContentType implements Identified<String> {
    HTML("html"), MARKDOWN("markdown");

    public static final Codebook<ContentType, String> CODEBOOK = new StaticCodebook<>(values());

    @NonNull String id;
}

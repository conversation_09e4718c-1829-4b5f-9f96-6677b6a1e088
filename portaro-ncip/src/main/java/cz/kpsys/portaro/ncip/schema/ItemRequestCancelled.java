
package cz.kpsys.portaro.ncip.schema;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElementRef;
import jakarta.xml.bind.annotation.XmlElementRefs;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}InitiationHeader" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}UserId"/>
 *         &lt;choice>
 *           &lt;element ref="{http://www.niso.org/2008/ncip}ItemId"/>
 *           &lt;sequence>
 *             &lt;element ref="{http://www.niso.org/2008/ncip}RequestId"/>
 *             &lt;element ref="{http://www.niso.org/2008/ncip}ItemId" minOccurs="0"/>
 *           &lt;/sequence>
 *         &lt;/choice>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}RequestType"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}RequestScopeType" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}FiscalTransactionInformation" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}ItemOptionalFields" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}UserOptionalFields" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}Ext" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "content"
})
@XmlRootElement(name = "ItemRequestCancelled")
@Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
public class ItemRequestCancelled {

    @XmlElementRefs({
        @XmlElementRef(name = "RequestId", namespace = "http://www.niso.org/2008/ncip", type = RequestId.class, required = false),
        @XmlElementRef(name = "Ext", namespace = "http://www.niso.org/2008/ncip", type = Ext.class, required = false),
        @XmlElementRef(name = "RequestScopeType", namespace = "http://www.niso.org/2008/ncip", type = JAXBElement.class, required = false),
        @XmlElementRef(name = "ItemOptionalFields", namespace = "http://www.niso.org/2008/ncip", type = ItemOptionalFields.class, required = false),
        @XmlElementRef(name = "UserOptionalFields", namespace = "http://www.niso.org/2008/ncip", type = UserOptionalFields.class, required = false),
        @XmlElementRef(name = "InitiationHeader", namespace = "http://www.niso.org/2008/ncip", type = InitiationHeader.class, required = false),
        @XmlElementRef(name = "RequestType", namespace = "http://www.niso.org/2008/ncip", type = JAXBElement.class, required = false),
        @XmlElementRef(name = "ItemId", namespace = "http://www.niso.org/2008/ncip", type = ItemId.class, required = false),
        @XmlElementRef(name = "UserId", namespace = "http://www.niso.org/2008/ncip", type = UserId.class, required = false),
        @XmlElementRef(name = "FiscalTransactionInformation", namespace = "http://www.niso.org/2008/ncip", type = FiscalTransactionInformation.class, required = false)
    })
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected List<Object> content;

    /**
     * Gets the rest of the content model. 
     * 
     * <p>
     * You are getting this "catch-all" property because of the following reason: 
     * The field name "ItemId" is used by two different parts of a schema. See: 
     * line 1066 of file:/E:/Dropbox/Job/kpsys/PortaroJ/portaro-ncip/ncip_v2_02.xsd
     * line 1063 of file:/E:/Dropbox/Job/kpsys/PortaroJ/portaro-ncip/ncip_v2_02.xsd
     * <p>
     * To get rid of this property, apply a property customization to one 
     * of both of the following declarations to change their names: 
     * Gets the value of the content property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the content property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getContent().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link RequestId }
     * {@link Ext }
     * {@link JAXBElement }{@code <}{@link SchemeValuePair }{@code >}
     * {@link ItemOptionalFields }
     * {@link InitiationHeader }
     * {@link UserOptionalFields }
     * {@link ItemId }
     * {@link JAXBElement }{@code <}{@link SchemeValuePair }{@code >}
     * {@link UserId }
     * {@link FiscalTransactionInformation }
     * 
     * 
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public List<Object> getContent() {
        if (content == null) {
            content = new ArrayList<Object>();
        }
        return this.content;
    }

}

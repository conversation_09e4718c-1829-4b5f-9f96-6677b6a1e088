package cz.kpsys.portaro.oai.provider.impl;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.SystemInstitution;
import cz.kpsys.portaro.oai.model.DeletedRecordResponse;
import cz.kpsys.portaro.oai.model.GranularityResponse;
import cz.kpsys.portaro.oai.model.IdentifyResponse;
import cz.kpsys.portaro.oai.provider.handler.OaiCommand;
import cz.kpsys.portaro.oai.provider.handler.VerbHandler;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.Instant;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class IdentifyHandler implements VerbHandler<IdentifyResponse> {

    @NonNull ContextualProvider<Department, @NonNull SystemInstitution> systemInstitutionProvider;

    @Override
    public @NonNull IdentifyResponse handle(@NonNull OaiCommand command) {
        SystemInstitution systemInstitution = systemInstitutionProvider.getOn(command.department());
        return new IdentifyResponse(
                systemInstitution.notBlankName(),
                command.baseUrl(),
                IdentifyResponse.DEFAULT_PROTOCOL_VERSION,
                ListUtil.singletonListOrEmptyIfNull(systemInstitution.email()),
                Instant.ofEpochMilli(0),
                DeletedRecordResponse.PERSISTENT,
                GranularityResponse.YYYY_MM_DD,
                IdentifyResponse.DEFAULT_COMPRESSION,
                List.of()
        );
    }
}

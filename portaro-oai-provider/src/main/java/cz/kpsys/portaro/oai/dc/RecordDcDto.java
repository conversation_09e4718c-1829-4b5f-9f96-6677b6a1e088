package cz.kpsys.portaro.oai.dc;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.NonNull;
import lombok.Value;

import java.util.List;

@JacksonXmlRootElement(localName = "dc", namespace = "http://www.openarchives.org/OAI/2.0/oai_dc/")
@Value
public class RecordDcDto {

    @JacksonXmlProperty(localName = "title", namespace = "http://purl.org/dc/elements/1.1/")
    @NonNull
    String title;

    @JacksonXmlProperty(localName = "creator", namespace = "http://purl.org/dc/elements/1.1/")
    @JacksonXmlElementWrapper(useWrapping = false)
    @NonNull
    List<String> creators;

    @JacksonXmlProperty(localName = "subject", namespace = "http://purl.org/dc/elements/1.1/")
    @JacksonXmlElementWrapper(useWrapping = false)
    @NonNull
    List<String> subjects;

    @JacksonXmlProperty(localName = "language", namespace = "http://purl.org/dc/elements/1.1/")
    @JacksonXmlElementWrapper(useWrapping = false)
    @NonNull
    List<String> languages;
}

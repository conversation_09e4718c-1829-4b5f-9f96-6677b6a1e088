package cz.kpsys.portaro.oai.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.NonNull;

import java.util.List;

public record ListIdentifiersResponse(

        @JacksonXmlProperty(localName = "header", namespace = "http://www.openarchives.org/OAI/2.0/")
        @JacksonXmlElementWrapper(useWrapping = false)
        @NonNull
        List<HeaderResponse> headers,

        @JacksonXmlProperty(localName = "resumptionToken", namespace = "http://www.openarchives.org/OAI/2.0/")
        @NonNull
        ResumptionTokenResponse resumptionToken

) implements OaiVerbResponse {}

package cz.kpsys.portaro.user.payment.provider.csobgw.datatypes;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.NonNull;
import lombok.Value;
import org.springframework.lang.Nullable;

@Value
public class CsobGwPaymentResponse {

    @JsonProperty("payId")
    @NonNull
    String payId;

    @JsonProperty("dttm")
    @NonNull
    String timestamp;

    @JsonProperty("resultCode")
    @NonNull
    Integer resultCode;

    @JsonProperty("resultMessage")
    @NonNull
    String resultMessage;

    @JsonProperty("paymentStatus")
    @Nullable
    Integer paymentStatus;

    @JsonProperty("authCode")
    @Nullable
    String authCode;

    @JsonProperty("customerCode")
    @Nullable
    String customerCode;

    @JsonProperty("statusDetail")
    @Nullable
    String statusDetail;

    @JsonProperty("signature")
    @NonNull
    String signature;

}

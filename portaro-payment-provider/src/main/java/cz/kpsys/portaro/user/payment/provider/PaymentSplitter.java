package cz.kpsys.portaro.user.payment.provider;

import cz.kpsys.portaro.commons.util.NumberUtil;
import cz.kpsys.portaro.finance.AmountType;
import cz.kpsys.portaro.payment.*;
import cz.kpsys.portaro.user.BasicUser;
import lombok.NonNull;
import lombok.val;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class PaymentSplitter {

    /**
     * Rozpočítání položek PLATBY_PLACENI mezi oddělení, kde je dluženo. Vyhledává se od současného departmentu níž.
     *
     * @param payment zpracovávaná platba
     * @param debts seznam dluhů vlastníků z položek placení
     *
     * @return vytvořené příchozí transakce na základě vstupní platby
     */
    public static List<TransactionCreateCommand> split(@NonNull Payment payment, @NonNull List<DepartmentedTypedUserAmount> debts) {
        List<TransactionCreateCommand> splitCommands = new ArrayList<>();

        // Najít dluhy uživatelů z platby v podřízených departmentech
        Map<DebtKey, ArrayList<DepartmentedTypedUserAmount>> usersDebts = debts.stream().collect(
                Collectors.groupingBy(DebtKey::fromPaymentItem, Collectors.toCollection(ArrayList::new)));

        // Pro každou položku platby ..
        for (TypedUserAmount paymentItem : payment.getItems()) {
            Assert.state(paymentItem.isPositive(), "Payment item must be positive or zero");

            // .. získat všechny dluhy, které by mohly odpovídat dané položce platby na všech odděleních ..
            val bilancesOnDepartments = usersDebts.computeIfAbsent(DebtKey.fromPaymentItem(paymentItem),
                    (k) -> new ArrayList<>());

            // .. připravit hledání od departmentu, kde se platilo ..
            for (int i = 0; i < bilancesOnDepartments.size(); i++) {
                val bilance = bilancesOnDepartments.get(i);
                if (bilance.getDepartment().equals(payment.getDepartment())) {
                    Collections.swap(bilancesOnDepartments, i, 0);
                    break;
                }
            }

            // .. postupně umořit dluhy daného typu napříč odděleními
            BigDecimal remainingAmount = paymentItem.getSum();
            val it = bilancesOnDepartments.iterator();
            while (NumberUtil.isPositive(remainingAmount) && it.hasNext()) {
                val bilance = it.next();
                if (!bilance.isNegative()) {
                    continue;
                }

                BigDecimal debtPaid = remainingAmount.min(bilance.getSum().abs());
                remainingAmount = remainingAmount.subtract(debtPaid);

                TransactionCreateCommand command = new TransactionCreateCommand(
                        bilance.getDepartment(),
                        new UserTypedBilance(paymentItem.getOwner(), debtPaid, paymentItem.getType()),
                        Instant.now(),
                        payment.getCashier(),
                        payment.getDepartment(),
                        payment.getId()
                );
                splitCommands.add(command);
            }

            // všechny dluhy byly už umořeny -> nabít kredit na oddělení, kde bylo placeno
            if (NumberUtil.isPositive(remainingAmount)) {
                TransactionCreateCommand command = new TransactionCreateCommand(
                        payment.getDepartment(),
                        new UserTypedBilance(paymentItem.getOwner(), remainingAmount, paymentItem.getType()),
                        Instant.now(),
                        payment.getCashier(),
                        payment.getDepartment(),
                        payment.getId()
                );
                splitCommands.add(command);
            }
        }

        return splitCommands;
    }

    private record DebtKey(
            BasicUser user,
            AmountType type
    ) {
        public static DebtKey fromPaymentItem(@NonNull TypedUserAmount typedUserAmount) {
            return new DebtKey(typedUserAmount.getOwner(), typedUserAmount.getType());
        }
    }

}

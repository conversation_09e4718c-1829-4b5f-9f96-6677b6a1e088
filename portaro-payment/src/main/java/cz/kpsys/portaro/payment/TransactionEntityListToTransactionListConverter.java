package cz.kpsys.portaro.payment;

import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.BatchFiller;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.finance.AmountType;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class TransactionEntityListToTransactionListConverter implements Converter<List<? extends TransactionEntity>, List<Transaction>> {

    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull AllByIdsLoadable<BasicUser, Integer> basicUsersLoader;
    @NonNull ByIdLoadable<AmountType, Integer> amountTypeLoader;

    @Override
    public List<Transaction> convert(@NonNull List<? extends TransactionEntity> transactionEntities) {
        var entityToOwnerMap = BatchFiller.of(basicUsersLoader).load(transactionEntities, TransactionEntity::getOwnerId);
        var entityToCashierMap = BatchFiller.of(basicUsersLoader).load(transactionEntities, TransactionEntity::getCashierId);

        return ListUtil.convertStrict(transactionEntities, transactionEntity -> new Transaction(
                transactionEntity.getId(),
                transactionEntity.getPaymentId(),
                transactionEntity.getRefundPaymentId(),
                departmentLoader.getById(transactionEntity.getTargetDepartmentId()),
                new UserTypedBilance(
                        entityToOwnerMap.get(transactionEntity),
                        transactionEntity.getSum(),
                        amountTypeLoader.getById(transactionEntity.getPaymentTypeId())
                ),
                transactionEntity.getDate(),
                departmentLoader.getById(transactionEntity.getCashierDepartmentId()),
                entityToCashierMap.get(transactionEntity),
                transactionEntity.getServiceNote(),
                transactionEntity.getNote(),
                transactionEntity.getRefundDate()
        ));
    }

}

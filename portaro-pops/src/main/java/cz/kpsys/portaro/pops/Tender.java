package cz.kpsys.portaro.pops;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.BasicNamedLabeledIdentified;
import cz.kpsys.portaro.commons.object.SoftDeletable;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

import java.time.Instant;
import java.util.Optional;

import static java.time.Instant.now;
import static java.time.temporal.ChronoUnit.DAYS;

/**
 * Created by Jan on 20. 5. 2015.
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class Tender extends BasicNamedLabeledIdentified<Integer> implements SoftDeletable {

    public static final int PHASE_CREATED = 1;
    public static final int PHASE_OPENED = 2;
    public static final int PHASE_STARTED = 3;
    public static final int PHASE_FINISHED = 4;
    public static final int PHASE_EVALUATED = 5;

    TenderData data;

    public Tender(TenderData data) {
        super(data.getId(), data.getName());
        this.data = data;
    }


    public static Tender testing(Integer id, int createDaysFromNow, int openDaysFromNow, int startDaysFromNow, int endDaysFromNow, int evaluationDaysFromNow, BasicUser creator) {
        return new Tender(new TenderData(
                id,
                null,
                now().plus(createDaysFromNow, DAYS),
                now().plus(openDaysFromNow, DAYS),
                now().plus(startDaysFromNow, DAYS),
                now().plus(endDaysFromNow, DAYS),
                now().plus(evaluationDaysFromNow, DAYS),
                null,
                15,
                creator));
    }

    public static Tender testingOpenedButNotStarted(Integer id, BasicUser creator) {
        return testing(id, -90, -60, 30, 60, 90, creator);
    }

    public static Tender testingStarted(Integer id, BasicUser creator) {
        return testing(id, -90, -60, -30, 30, 60, creator);
    }

    public static Tender testingFinished(Integer id, BasicUser creator) {
        return testing(id, -120, -90, -60, -30, 60, creator);
    }


    @Override
    public Text getText() {
        return Texts.ofNative(StringUtil.firstNotBlank(getName(), "#" + getId()));
    }

    @JsonIgnore
    public int getPhase() {
        Instant now = now();
        if (getEvaluationDate() != null && now.isAfter(getEvaluationDate())) {
            return PHASE_EVALUATED;
        }
        if (now.isAfter(getEndDate())) {
            return PHASE_FINISHED;
        }
        if (now.isAfter(getStartDate())) {
            return PHASE_STARTED;
        }
        if (now.isAfter(getOpenDate())) {
            return PHASE_OPENED;
        }
        if (now.isAfter(getCreateDate())) {
            return PHASE_CREATED;
        }
        throw new IllegalStateException(String.format("Tender state is illegal - current time is before this tender (%s) create date (%s)", getId(), getCreateDate()));
    }

    @JsonIgnore
    public boolean isAcceptingOffers() {
        return getPhase() == Tender.PHASE_STARTED;
    }

    @JsonIgnore
    public boolean isAcceptingSignedAgreement() {
        return getPhase() == Tender.PHASE_STARTED;
    }


    public Instant getCreateDate() {
        return data.getCreateDate();
    }

    public Instant getOpenDate() {
        return data.getOpenDate();
    }

    public Instant getStartDate() {
        return data.getStartDate();
    }

    public Instant getEndDate() {
        return data.getEndDate();
    }

    public Instant getEvaluationDate() {
        return data.getEvaluationDate();
    }

    public Optional<Instant> getDeleteDate() {
        return Optional.ofNullable(data.getDeleteDate());
    }

    @Override
    public void setDeleted(Instant when) {
        data.setDeleteDate(when);
    }

    public int getDefaultDaysToSupply() {
        return data.getDefaultDaysToSupply();
    }

    public BasicUser getCreator() {
        return data.getCreator();
    }
}

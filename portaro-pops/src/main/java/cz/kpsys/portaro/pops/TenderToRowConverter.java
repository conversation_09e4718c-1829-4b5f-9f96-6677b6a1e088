package cz.kpsys.portaro.pops;

import org.springframework.core.convert.converter.Converter;

public class TenderToRowConverter implements Converter<Tender, TenderEntity> {

    @Override
    public TenderEntity convert(Tender source) {
        return new TenderEntity(
                source.getId(),
                source.getName(),
                source.getCreateDate(),
                source.getOpenDate(),
                source.getStartDate(),
                source.getEndDate(),
                source.getEvaluationDate(),
                source.getDeleteDate().orElse(null),
                source.getDefaultDaysToSupply(),
                source.getCreator().getId());
    }

}

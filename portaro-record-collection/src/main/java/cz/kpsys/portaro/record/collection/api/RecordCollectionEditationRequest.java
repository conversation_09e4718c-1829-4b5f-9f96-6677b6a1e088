package cz.kpsys.portaro.record.collection.api;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.formannotation.annotations.form.ConfirmableRequest;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.number.NumberEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.singleacceptable.SingleAcceptableEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.text.TextEditor;
import cz.kpsys.portaro.record.collection.recordcollection.RecordCollection;
import cz.kpsys.portaro.record.collection.recordcollectioncategory.RecordCollectionCategory;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.With;
import org.springframework.lang.Nullable;

import static cz.kpsys.portaro.record.collection.api.RecordCollectionApiConstants.COLLECTIONS_URL_PART;
import static cz.kpsys.portaro.record.collection.api.RecordCollectionApiConstants.RECORD_COLLECTIONS_URL_PART;

@Form(id = "recordCollectionEditationRequest", title = "{commons.Uprava}")
@FormSubmit(path = RECORD_COLLECTIONS_URL_PART + COLLECTIONS_URL_PART + "/#{formObject.collection.id}")
@With
public record RecordCollectionEditationRequest(

        Boolean confirmed,

        @NotNull
        RecordCollection collection,

        @FormPropertyLabel("{commons.nazev}")
        @TextEditor
        @Size(min = 1, max = 100)
        @NotBlank
        String name,

        @FormPropertyLabel("{recordCollections.ParentCollection}")
        @Nullable
        @SingleAcceptableEditor(valuesSourceBean = "availableParentCollectionsByUserProvider", allowSelectNull = true, switchToInlineModeThreshold = 1, visibleIfSingleValue = true)
        RecordCollection parentCollection,

        @FormPropertyLabel("{commons.Poradi}")
        @NumberEditor
        @NotNull
        @Min(0)
        Integer order,

        @FormPropertyLabel("{recordCollections.Note}")
        @TextEditor
        @Size(min = 1, max = 512)
        @NullableNotBlank
        String note,

        @FormPropertyLabel("{recordCollections.Category}")
        @SingleAcceptableEditor(valuesSourceBean = "recordCollectionEditationRequestRecordCategoryProvider", visibleIfSingleValue = true)
        @NotNull
        RecordCollectionCategory recordCollectionCategory

) implements ConfirmableRequest<RecordCollectionEditationRequest> {}

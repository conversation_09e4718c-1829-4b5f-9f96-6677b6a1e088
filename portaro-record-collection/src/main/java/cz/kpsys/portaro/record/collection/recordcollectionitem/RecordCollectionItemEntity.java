package cz.kpsys.portaro.record.collection.recordcollectionitem;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Ordered;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.RecordCollectionDb.RECORD_COLLECTION_ITEM.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class RecordCollectionItemEntity implements Serializable, Identified<UUID>, Ordered {

    @Id
    @Column(name = ID)
    @EqualsAndHashCode.Include
    @NonNull
    UUID id;

    @Column(name = RECORD_COLLECTION_ID)
    @NonNull
    UUID recordCollectionId;

    @Column(name = RECORD_ID)
    @NonNull
    UUID recordId;

    @Column(name = ORDER_NUMBER)
    @NonNull
    Integer order;
}

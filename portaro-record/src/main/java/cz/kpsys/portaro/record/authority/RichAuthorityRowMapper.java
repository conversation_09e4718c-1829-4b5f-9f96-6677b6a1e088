package cz.kpsys.portaro.record.authority;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.file.StubFile;
import cz.kpsys.portaro.prop.ObjectProperties;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.RecordIdentifier;
import cz.kpsys.portaro.record.RecordStatus;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.RecordDb.COMMON_KINDED_NAME;
import static cz.kpsys.portaro.databasestructure.RecordDb.KATAUT_4.ID_AUT;
import static cz.kpsys.portaro.databasestructure.RecordDb.RECORD;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RichAuthorityRowMapper implements RowMapper<Record> {

    @NonNull ByIdLoadable<Fond, Integer> fondLoader;
    @NonNull ByIdLoadable<RecordStatus, Integer> recordStatusLoader;

    @Override
    public Record mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
        UUID id = DbUtils.uuidNotNull(rs, RECORD.ID);
        UUID masterRecordId = DbUtils.uuidOrNull(rs, RECORD.MASTER_RECORD_ID);
        UUID creationEventId = DbUtils.uuidNotNull(rs, RECORD.CREATION_EVENT_ID);
        UUID activationEventId = DbUtils.uuidOrNull(rs, RECORD.ACTIVATION_EVENT_ID);
        UUID deletionEventId = DbUtils.uuidOrNull(rs, RECORD.DELETION_EVENT_ID);
        Integer kindedId = DbUtils.getInteger(rs, ID_AUT);
        Integer fondId = DbUtils.getIntegerNotNull(rs, RECORD.FOND_ID);

        RecordIdentifier recordIdentifier = masterRecordId == null
                ? RecordIdentifier.of(id)
                : RecordIdentifier.ofMergedRecord(id, masterRecordId);
        Fond fond = fondLoader.getById(fondId);
        RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(recordIdentifier, fond);

        Long coverFileId = DbUtils.getLong(rs, RECORD.PRIMARY_FILE_ID);
        Integer directoryId = DbUtils.getInteger(rs, RECORD.DIRECTORY_ID);
        String name = StringUtil.notBlankTrimmedString(rs.getString(COMMON_KINDED_NAME));

        final Record a = Record.createAuthority(
                recordIdFondPair,
                kindedId,
                creationEventId,
                false,
                name,
                null,
                ObjectProperties.empty()
        );
        a.setStatus(recordStatusLoader.getById(rs.getInt(RECORD.RECORD_STATUS_ID)));
        a.setCover(coverFileId == null ? null : new StubFile(coverFileId));
        a.setDirectoryId(directoryId);
        a.setActivationEventId(activationEventId);
        a.setDeletionEventId(deletionEventId);

        return a;
    }

}

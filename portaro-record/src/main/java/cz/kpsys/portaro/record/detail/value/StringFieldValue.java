package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.record.detail.Field;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.Optional;

public record StringFieldValue(

    @NonNull
    String value,

    @Nullable
    String label,

    @NonNull
    Text text

) implements ScalarFieldValue<String> {

    public static @NonNull StringFieldValue of(@NonNull String raw) {
        return new StringFieldValue(
                raw,
                StringUtil.notEmptyString(raw),
                getUserFriendlyText(raw)
        );
    }

    public static @NonNull StringFieldValue of(@NonNull String value, @NonNull Text text) {
        return new StringFieldValue(
                value,
                StringUtil.notEmptyString(value),
                text
        );
    }

    public static Optional<String> extract(@NonNull Field<StringFieldValue> field) {
        return Optional.ofNullable(ObjectUtil.elvis((StringFieldValue) field.getValueHolder(), StringFieldValue::value));
    }

    private static Text getUserFriendlyText(String raw) {
        if (StringUtil.isNullOrBlank(raw)) {
            return Texts.ofEmpty();
        }
        return Texts.ofNative(raw);
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, StringFieldValue.class, StringFieldValue::value);
    }

    @Override
    public int hashCode() {
        return value().hashCode();
    }
}

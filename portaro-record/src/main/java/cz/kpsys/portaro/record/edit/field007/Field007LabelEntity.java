package cz.kpsys.portaro.record.edit.field007;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Ordered;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.RecordDb.DEF_KODY007.ID;
import static cz.kpsys.portaro.databasestructure.RecordDb.DEF_POPIS007.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class Field007LabelEntity implements Serializable, Ordered, Identified<UUID> {

    @Column(name = PORADI)
    @NonNull
    Integer order;

    @Column(name = KOD)
    @NonNull
    String documentCategoryCode;

    @Column(name = POPIS)
    @NonNull
    String description;

    @Id
    @Column(name = ID)
    @EqualsAndHashCode.Include
    @NonNull
    UUID id;
}

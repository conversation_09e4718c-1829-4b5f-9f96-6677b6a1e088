package cz.kpsys.portaro.record.edit.view;

import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.Delegate;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class UserInteractionRecordEditation implements RecordEditation {

    @Delegate
    @NonNull RecordEditation target;

    @Nullable Fond rootFond;

    public UserInteractionRecordEditation(@NonNull RecordEditation target,
                                          @Nullable Fond rootFond) {
        this.target = target;
        this.rootFond = rootFond;
    }

    public @Nullable Fond getRootFond() {
        return rootFond;
    }

    @Override
    public String toString() {
        return "UserInteractionRecordEditation{" + target + '}';
    }
}

package cz.kpsys.portaro.record.sec;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EditorPermissionsBypassingCurrentAuthShowableFondsLoader implements CurrentAuthFondsLoader {

    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull AuthenticatedContextualProvider<Department, @NonNull List<Department>> editableDepartmentsAuthenticatedContextualProvider;
    @NonNull AllValuesProvider<Fond> enabledFondsProvider;
    @NonNull ContextualProvider<Department, @NonNull List<Fond>> showableFondsDepartmentedProvider;

    @Override
    public List<Fond> getAllByAuth(@NonNull UserAuthentication auth, @NonNull Department ctx) {
        if (isEditorOnCtx(auth, ctx)) {
            return enabledFondsProvider.getAll();
        }
        return showableFondsDepartmentedProvider.getOn(ctx);
    }

    /**
     * Chtelo by to nekontrolovat celou FAMILY, ale jen PARENTS_CHAIN - takto totiz muze knihovnik s pravy jen na dolni koukat na fondy z kontextu rootu - jenze kvuli MZLU, ktera pouziva jen root katalog, toto nemuzeme. Leda bychom na to udelali ini
     */
    private boolean isEditorOnCtx(@NonNull UserAuthentication auth, @NonNull Department ctx) {
        List<Department> ctxFamily = contextHierarchyLoader.getAllByScope(ctx, HierarchyLoadScope.FAMILY);
        List<Department> editableDepartments = editableDepartmentsAuthenticatedContextualProvider.getOn(auth, ctx);
        return ListUtil.hasIntersection(editableDepartments, ctxFamily);
    }
}

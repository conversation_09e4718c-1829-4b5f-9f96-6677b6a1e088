package cz.kpsys.portaro.record.sec;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.security.permission.ActionPermission;
import cz.kpsys.portaro.security.permission.ActionPermissionAccessor;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.role.editor.LibrarianPrivileges;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UserEditableFondsLoaderByPermissions implements UserEditableFondsLoader {

    @NonNull ActionPermissionAccessor actionPermissionAccessor;
    @NonNull AllValuesProvider<Fond> enabledFondsProvider;
    @NonNull Converter<Fond, Integer> fondToActionIdConverter;


    @Override
    public List<Fond> getAllByUser(BasicUser user) {
        if (!user.isEvided()) {
            return List.of();
        }
        return ListUtil.filter(enabledFondsProvider.getAll(), fond -> isFondPermitted(user, fond));
    }

    private boolean isFondPermitted(BasicUser user, Fond fond) {
        Integer fondActionId = Objects.requireNonNull(fondToActionIdConverter.convert(fond));
        ActionPermission effectivePermission = actionPermissionAccessor.getEffectivePermission(fondActionId, user);
        return effectivePermission.isPermitted();
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class FondToActionIdConverter implements Converter<Fond, Integer> {

        @NonNull Provider<Optional<Fond>> desiderataFondProvider;

        @Override
        public Integer convert(@NonNull Fond fond) {
            Optional<Fond> desiderataFond = desiderataFondProvider.get();
            if (desiderataFond.isPresent() && desiderataFond.get().equals(fond)) {
                return LibrarianPrivileges.ACTION_DESIDERATA_EDIT;
            }
            return LibrarianPrivileges.FOND_EDIT_ID_OFFSET_START + fond.getId();
        }
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class ActionIdToFondConverter implements Converter<Integer, Fond> {

        @NonNull ByIdLoadable<Fond, Integer> fondLoader;
        @NonNull Provider<Optional<Fond>> desiderataFondProvider;

        @Override
        public Fond convert(@NonNull Integer actionId) {
            if (actionId == LibrarianPrivileges.ACTION_DESIDERATA_EDIT) { //deziderata
                return desiderataFondProvider.get().orElseThrow();
            }
            if (actionId >= LibrarianPrivileges.FOND_EDIT_ID_FIRST_POSSIBLE && actionId <= LibrarianPrivileges.FOND_EDIT_ID_LAST_POSSIBLE) {
                int fondId = actionId - LibrarianPrivileges.FOND_EDIT_ID_OFFSET_START;
                return fondLoader.getById(fondId);
            }
            return null;
        }
    }
}

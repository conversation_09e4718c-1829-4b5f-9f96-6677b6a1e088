package cz.kpsys.portaro.resourcesupdate.db;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.resourcesupdate.db.firebird.FirebirdUniqueKeyGenerator;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.ArrayList;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class StandardRichTableGenerator implements Converter<RichTableCreation, List<String>> {

    @NonNull Converter<TableCreation, String> tableGenerator;
    @NonNull Converter<PrimaryKeyCreation, List<String>> primaryKeyGenerator;
    @NonNull Converter<UniqueKeyCreation, String> uniqueKeyGenerator;
    @NonNull Converter<ForeignKeyCreation, String> foreignKeyGenerator;
    @NonNull Converter<GrantCreation, String> grantGenerator;

    public StandardRichTableGenerator(@NonNull Converter<TableCreation, String> tableGenerator, Converter<IdGeneratorCreation, String> autoIncrementerGenerator) {
        this.tableGenerator = tableGenerator;
        this.primaryKeyGenerator = StandardPrimaryKeyGenerator.withSequence(new StandardSequenceGenerator(autoIncrementerGenerator));
        this.uniqueKeyGenerator = new FirebirdUniqueKeyGenerator();
        this.foreignKeyGenerator = new StandardForeignKeyGenerator();
        this.grantGenerator = new StandardGrantGenerator();
    }

    @Override
    public List<String> convert(RichTableCreation source) {
        List<String> sqls = new ArrayList<>();

        sqls.add(tableGenerator.convert(source.getTable()));

        PrimaryKeyCreation primaryKey = source.getPrimaryKey();
        if (primaryKey != null) {
            sqls.addAll(primaryKeyGenerator.convert(primaryKey));
        }

        List<UniqueKeyCreation> uniqueKeys = source.getUniqueKeys();
        if (ListUtil.hasLength(uniqueKeys)) {
            for (UniqueKeyCreation uk : uniqueKeys) {
                sqls.add(uniqueKeyGenerator.convert(uk));
            }
        }

        List<ForeignKeyCreation> foreignKeys = source.getForeignKeys();
        if (ListUtil.hasLength(foreignKeys)) {
            for (ForeignKeyCreation fk : foreignKeys) {
                sqls.add(foreignKeyGenerator.convert(fk));
            }
        }

        List<GrantCreation> grants = source.getGrants();
        if (ListUtil.hasLength(grants)) {
            for (GrantCreation grant : grants) {
                sqls.add(grantGenerator.convert(grant));
            }
        }

        return sqls;
    }

}

package cz.kpsys.portaro.resourcesupdate.db.firebird;

import cz.kpsys.portaro.resourcesupdate.db.IdGeneratorCreation;
import org.springframework.core.convert.converter.Converter;

public class FirebirdUuidIdTriggerGenerator implements Converter<IdGeneratorCreation, String> {

    @Override
    public String convert(IdGeneratorCreation source) {
        return String.format("""
                CREATE OR ALTER TRIGGER %s for %s
                active before insert position 0
                as
                begin
                 if (new.%s is null) then new.%s = lower(uuid_to_char(gen_uuid()));
                end""", source.triggerName(), source.table(), source.column(), source.column());
    }

}

package cz.kpsys.portaro.config;

import com.github.kagkarlsson.scheduler.boot.config.DbSchedulerCustomizer;
import com.github.kagkarlsson.scheduler.jdbc.JdbcCustomization;
import cz.kpsys.portaro.config.scheduling.CustomDbSchedulerAutoConfiguration;
import cz.kpsys.portaro.config.scheduling.FirebirdJdbcCustomization;
import cz.kpsys.portaro.databaseproperties.DatabaseProperties;
import cz.kpsys.portaro.logging.ExecutionTimeLoggingAspect;
import cz.kpsys.portaro.logging.LoggingAspect;
import cz.kpsys.portaro.logging.TraceIdAddingCronAspect;
import cz.kpsys.portaro.logging.TraceIdRepository;
import cz.kpsys.portaro.logging.profiling.ProfilingAdvisor;
import cz.kpsys.portaro.logging.profiling.ProfilingMethodInterceptor;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.task.ThreadPoolTaskSchedulerBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.Optional;

@EnableScheduling
@Configuration
@Import(CustomDbSchedulerAutoConfiguration.class)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SchedulingConfig implements SchedulingConfigurer {

    @NonNull TraceIdRepository traceIdRepository;
    @NonNull DatabaseProperties databaseProperties;

    @Bean
    public LoggingAspect loggingCronAspect() {
        return new LoggingAspect();
    }

    @Bean
    public ExecutionTimeLoggingAspect executionTimeLoggingAspect() {
        return new ExecutionTimeLoggingAspect();
    }

    @Bean
    public ProfilingMethodInterceptor profilingMethodInterceptor() {
        return new ProfilingMethodInterceptor();
    }

    @Bean
    public ProfilingAdvisor profilingAdvisor() {
        return new ProfilingAdvisor(profilingMethodInterceptor());
    }

    @Bean
    public TraceIdAddingCronAspect traceIdAddingCronAspect() {
        return new TraceIdAddingCronAspect(traceIdRepository);
    }

    @Bean
    public TaskScheduler taskScheduler() {
        return new ThreadPoolTaskSchedulerBuilder()
                .poolSize(20)
                .build();
    }

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setTaskScheduler(taskScheduler());
    }

    /*********************************** DB SCHEDULER BEANS **************************/

    @Primary
    @Bean
    public DbSchedulerCustomizer dbSchedulerCustomizer() {
        return new DbSchedulerCustomizer() {
            @Override
            public Optional<JdbcCustomization> jdbcCustomization() {
                if (databaseProperties.getType().equals(DatabaseProperties.DBTYPE_FIREBIRD)) {
                    return Optional.of(new FirebirdJdbcCustomization(true));
                }
                return Optional.empty();
            }
        };
    }
    
}

<script lang="ts">
    import type {DraggableResizerEventData} from './types';
    import {oneWayDragging} from '../../../../media-viewer/actions/use.one-way-dragging';
    import {createEventDispatcher} from 'svelte';

    export let orientation: 'vertical' | 'horizontal';
    export let disabled = false;

    const dispatch = createEventDispatcher<{
        'resize': DraggableResizerEventData,
        'reset': void
    }>();

    let dragging = false;
    let resizedWidth = 0;

    const handleDragStart = () => {
        if (disabled) {
            return;
        }

        dragging = true;
        resizedWidth = 0;

        dispatch('resize', {
            resizing: dragging,
            delta: resizedWidth
        });
    };

    const handleDrag = (event: CustomEvent<number>) => {
        if (disabled) {
            return;
        }

        dragging = true;
        resizedWidth = event.detail;

        dispatch('resize', {
            resizing: dragging,
            delta: resizedWidth
        });
    };

    const handleOnDragEnd = () => {
        dragging = false;

        if (disabled) {
            return;
        }

        dispatch('resize', {
            resizing: dragging,
            delta: resizedWidth
        });

        resizedWidth = 0;
    };
</script>

<div class="draggable-resizer-wrapper orientation-{orientation}" class:resizer-disabled={disabled}>
    <div class="draggable-resizer"
         role="separator"
         class:dragging={dragging}
         use:oneWayDragging="{{orientation}}"
         on:dragging-start={handleDragStart}
         on:dragging-progress={handleDrag}
         on:dragging-end={handleOnDragEnd}
         on:dblclick={() => dispatch('reset')}>
    </div>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    @inactive-color: #AFAFAF;
    @resizer-offset-from-start: 24px;

    .draggable-resizer-wrapper {
        position: relative;
        z-index: 100;
        background-color: transparent;
        transition: 0.2s ease-in-out border-color;

        &.resizer-disabled {
            pointer-events: none;
        }

        &.orientation-vertical {
            height: 100%;
            border-right: 1px solid @inactive-color;

            .draggable-resizer {
                cursor: col-resize;
                transform: translateX(2px);
                right: 0;
                top: 0;
                height: 100%;
                width: 4px;
                border-right: 3px dashed transparent;
            }
        }

        &.orientation-horizontal {
            width: 100%;
            border-bottom: 1px solid @inactive-color;

            .draggable-resizer {
                cursor: row-resize;
                transform: translateY(-2px);
                left: 0;
                top: 0;
                width: 100%;
                height: 4px;
                border-bottom: 3px dashed transparent;
            }
        }

        .draggable-resizer {
            position: absolute;
            background-color: transparent;
            transition: 0.2s ease-in-out border-color;

            &:hover, &.dragging {
                border-color: var(--accent-blue-new);
            }
        }
    }
</style>
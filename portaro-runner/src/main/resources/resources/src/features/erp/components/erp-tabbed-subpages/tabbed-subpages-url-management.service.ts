import type {TabId} from 'shared/ui-widgets/tabset/types';
import type {UrlStateManagerService} from 'shared/state-manager/url-state-manager.service';
import type {Observable} from 'rxjs';
import {firstValueFrom} from 'rxjs';
import {replaceUrlHashbangParameter} from 'shared/utils/url-utils';

export class TabbedSubpagesUrlManagementService {
    public static serviceName = 'tabbedSubpagesUrlManagementService';

    /*@ngInject*/
    constructor(private urlStateManagerService: UrlStateManagerService<TabbedSubpagesUrlState>) {
    }

    public async getCurrentUrlState(): Promise<TabbedSubpagesUrlState> {
        const urlState$ = this.urlStateManagerService.getState$();
        return firstValueFrom(urlState$);
    }

    public setActiveTab(value: TabId): void {
        this.urlStateManagerService.requestChangeStateParameter('activeTab', value, true);
    }

    public getUrlState$(): Observable<TabbedSubpagesUrlState> {
        return this.urlStateManagerService.getState$();
    }

    public getTabHref(tabId: string): string {
        return replaceUrlHashbangParameter(window.location.href, 'activeTab', tabId);
    }
}

class TabbedSubpagesUrlState {
    activeTab?: TabId;
}
import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function facetTypesRoutes($stateProvider: StateProvider) {
    let facetTypesModule: { default: any; };
    $stateProvider
        .state({
            name: 'facet-types',
            url: '/facet-types',
            component:  KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => facetTypesModule.default,
            },
            lazyLoad: async () => {
                facetTypesModule = await import(/* webpackChunkName: "facet-types" */ './KpFacetTypes.svelte');
                return null;
            }
        });
}
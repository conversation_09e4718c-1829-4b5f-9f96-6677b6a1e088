<script lang="ts">
    import type {HistoricalPlace, HistoricalPlacesRequest} from './types';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {KpHistoricalPlacesService} from './kp-historical-places.service';
    import {onMount} from 'svelte';
    import {fly} from 'svelte/transition';
    import {pipe} from 'core/utils';
    import {strParams} from 'shared/utils/pipes';
    import {cloneDeep, isEqual} from 'lodash-es';
    import KpUniversalForm from 'shared/value-editors/kp-universal-form/KpUniversalForm.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import HistoricalPlacesMap from './map/HistoricalPlacesMap.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';
    import KpPageContainer from 'shared/layouts/containers/KpPageContainer.svelte';

    const localize = getLocalization();
    const service = getInjector().getByToken<KpHistoricalPlacesService>(KpHistoricalPlacesService.serviceName);
    const pageFlyInAnimParams = {y: 15, duration: 250};

    let loading = true;
    let loadError = false;
    let formSettings: any;
    let historicalPlaces: HistoricalPlace[];

    let query: HistoricalPlacesRequest = {};
    let lastQuery: HistoricalPlacesRequest = {};
    $: queryChanged = !isEqual(query, lastQuery);

    onMount(async () => {
        formSettings = await service.getSearchFormSettings();
        await searchForHistoricalPlaces();
    });

    async function searchForHistoricalPlaces() {
        loading = true;
        const newHistoricalPlaces = await service.getHistoricalPlaces(query);

        if (!newHistoricalPlaces) {
            loadError = true;
            loading = false;
            return;
        }

        historicalPlaces = newHistoricalPlaces;
        lastQuery = cloneDeep(query);
        loading = false;
    }

    const handleSearchSubmit = async () => {
        await searchForHistoricalPlaces();
    };
</script>

<KpPageContainer id="historical-places" additionalClasses="kp-historical-places-page">
    {#if loading}
        <KpLoadingBlock/>
    {/if}

    {#if loadError}
        <IconedContent icon="exclamation"
                       iconColor="var(--danger-red)"
                       orientation="vertical"
                       align="center"
                       justify="center">

            <span class="load-error-label">Nastala chyba při načítání!</span>
        </IconedContent>
    {/if}

    {#if !loading && !loadError}
        <div class="page-content-container" in:fly={pageFlyInAnimParams}>
            <KpHeading type="h1">Mapa knihoven</KpHeading>

            <div class="map-form-container">
                <div class="map-container">
                    <HistoricalPlacesMap markers="{historicalPlaces.map((place) => service.createMapMarker(place))}"/>
                </div>

                <div class="form-container">
                    <KpUniversalForm {formSettings} bind:model="{query}" on:submit={handleSearchSubmit}>
                        <KpButton isBlock
                                  additionalClasses="search-btn"
                                  buttonStyle="accent-blue-new"
                                  buttonType="submit"
                                  isDisabled="{!queryChanged}"
                                  slot="after-footer">

                            <IconedContent icon="search" on:click={handleSearchSubmit}>
                                {localize(/* @kp-localization commons.Find */ 'commons.Find')}
                            </IconedContent>
                        </KpButton>
                    </KpUniversalForm>

                    <h3>{pipe(localize(/* @kp-localization commons.FoundXRecords */ 'commons.FoundXRecords'), strParams(historicalPlaces.length.toString()))}</h3>
                </div>
            </div>
        </div>
    {/if}
</KpPageContainer>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.media-queries.less";

    .page-content-container {
        display: flex;
        flex-direction: column;
        gap: @spacing-xxl;
        width: 100%;

        .map-form-container {
            display: flex;

            .map-container {
                flex: 2;
            }

            .form-container {
                flex: 1;
                padding: @spacing-l;

                h3 {
                    margin-top: @spacing-ml;
                }
            }

            @media (max-width: @screen-sm) {
                flex-direction: column;

                .map-container,
                .form-container {
                    width: 100%;
                }
            }
        }
    }

    h3 {
        margin: 0;
        padding: 0; // Reset Provenio style
    }

    .load-error-label {
        color: var(--danger-red);
    }

    :global {
        .kp-historical-places-page form {
            // Reset Provenio styles
            fieldset {
                border: none !important;
                padding: 0 !important;
            }
        }
    }
</style>
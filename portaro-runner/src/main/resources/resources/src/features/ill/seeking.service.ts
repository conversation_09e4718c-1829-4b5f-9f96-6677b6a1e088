import type ReloaderService from 'shared/services/reloader.service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type SeekingDataService from './seeking.data-service';
import type {ProvidedSeeking, SeekedSeeking, Seeking, SeekingProvision} from 'typings/portaro.be.types';

export default class SeekingService {
    public static serviceName = 'seekingService';

    /*@ngInject*/
    constructor(
        private seekingDataService: SeekingDataService,
        private finishedResponseInteractionService: FinishedResponseInteractionService,
        private reloader: ReloaderService
    ) {
    }

    public async getById<E extends Seeking>(id: string): Promise<E> {
        return this.finishedResponseInteractionService.getResultOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.getById(id));
    }

    public async createSeekedSeekingAndReload() {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.createSeekedSeeking({}));
        this.reloader.reload();
    }

    public async createProvidedSeekingAndReload() {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.createProvidedSeeking({}));
        this.reloader.reload();
    }

    public async editSeekingData(seeking: SeekedSeeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.editSeekingData(seeking));
    }

    public async cancelSeeking(seeking: Seeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.cancel(seeking));
    }

    public async commenceIllSeeking(seeking: SeekedSeeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.commenceIllSeeking(seeking));
    }

    public async createSeekingProvision(seeking: SeekedSeeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.createSeekingProvision(seeking));
    }

    public async autoSearchSeekingProvisions(seeking: SeekedSeeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.autoSearchSeekingProvisions(seeking));
    }

    public async editSeekingProvision(seeking: SeekedSeeking, seekingProvision: SeekingProvision) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.editSeekingProvision(seeking, seekingProvision));
    }

    public async cancelSeekingProvision(seeking: SeekedSeeking, seekingProvision: SeekingProvision) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.cancelSeekingProvision(seeking, seekingProvision));
    }

    public async editSeekedSeekingActiveProvisionData(seeking: SeekedSeeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.editSeekedSeekingActiveProvisionData(seeking));
    }

    public async editProvidedSeekingActiveProvisionData(seeking: ProvidedSeeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.editProvidedSeekingActiveProvisionData(seeking));
    }

    public async activateSeekingUpcomingProvision(seeking: SeekedSeeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.activateSeekingUpcomingProvision(seeking));
    }

    public async sendSeekingActiveProvisionMessage(seeking: SeekedSeeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.sendSeekingActiveProvisionMessage(seeking));
    }

    public async acceptSeekingActiveProvision(seeking: ProvidedSeeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.acceptSeekingActiveProvision(seeking));
    }

    public async conditionallyAcceptSeekingActiveProvision(seeking: ProvidedSeeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.conditionallyAcceptSeekingActiveProvision(seeking));
    }

    public async acceptSeekingActiveProvisionCondition(seeking: SeekedSeeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.acceptSeekingActiveProvisionCondition(seeking));
    }

    public async sendIllSeekingActiveProvisionExemplar(seeking: ProvidedSeeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.sendIllSeekingActiveProvisionExemplar(seeking));
    }

    public async receiveSeekingActiveProvisionExemplar(seeking: SeekedSeeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.receiveSeekingActiveProvisionExemplar(seeking));
    }

    public async sendBackSeekingActiveProvisionExemplar(seeking: SeekedSeeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.sendBackSeekingActiveProvisionExemplar(seeking));
    }

    public async receiveBackIllSeekingActiveProvisionExemplar(seeking: ProvidedSeeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.receiveBackIllSeekingActiveProvisionExemplar(seeking));
    }

    public async seekerCancelSeekingActiveProvision(seeking: Seeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.seekerCancelSeekingActiveProvision(seeking));
    }

    public async providerCancelSeekingActiveProvision(seeking: Seeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.providerCancelSeekingActiveProvision(seeking));
    }

    public async createSeekingZiskejLink(seeking: SeekedSeeking) {
        await this.finishedResponseInteractionService.showSuccessToastOrFailedModalIgnoringManualInterruption(() => this.seekingDataService.createZiskejLink(seeking));
    }
}
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type {ModalDialogService} from 'shared/modal-dialogs/modal-dialog.service';
import type {MatchDataService} from './match.data-service';
import type {Match} from './types';
import type {LocalizationService} from 'shared/providers/localization.provider';


export class MatchPresenter {
    public static presenterName = 'matchPresenter';

    /*@ngInject*/
    constructor(private finishedResponseInteractionService: FinishedResponseInteractionService,
                private matchDataService: MatchDataService,
                private modalDialogService: ModalDialogService,
                private localizationService: LocalizationService) {
    }

    public async setMatchAsManuallyFixed(match: Match): Promise<Match> {
        try {
            await this.modalDialogService.openModalWindow('confirmationDialog', {text: this.localizationService.get(/* @kp-localization inventory.ReallyManuallyFix */ 'inventory.ReallyManuallyFix')})

            const actionResponse =  await this.matchDataService.manualFix(match);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
            return actionResponse.savedObject;
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async revertMatchBackFromManuallyFixed(match: Match): Promise<Match> {
        try {
            await this.modalDialogService.openModalWindow('confirmationDialog',{text: this.localizationService.get(/* @kp-localization inventory.ReallyRevertManualFix */ 'inventory.ReallyRevertManualFix')})

            const actionResponse =  await this.matchDataService.revertManualFix(match);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
            return actionResponse.savedObject;
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }
}
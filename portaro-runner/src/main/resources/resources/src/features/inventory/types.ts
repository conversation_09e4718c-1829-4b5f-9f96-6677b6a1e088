import type {
    Fond,
    Location,
    Identified,
    Department,
    LabeledIdentified,
    BasicUser,
    Link,
    UUID,
    LoanCategory,
    ThematicGroup,
    ExemplarStatus,
    NamedLabeledIdentified
} from 'typings/portaro.be.types';

export interface Inventory extends NamedLabeledIdentified<number> {
    locations: Location[];
    fond?: Fond;
    department?: Department;
    inventoryState: InventoryState;
    date?: string;
    createDate: string;
    closeDate?: string;
    creator: BasicUser;
    lastEditor: BasicUser;
    includeIssues: boolean;
    signaturePrefix?: string;
    lastSignature?: string;
    firstAccessNumber?: string;
    lastAccessNumber?: string;
    includeExchangeSets: boolean;
}

export type InventoryState = LabeledIdentified<number>;

export interface Capture extends Identified<number> {
    inventory: Inventory;
    identifier: string;
    exemplar: number;
    quantity: number;
    captureWay: number;
}

export interface Match extends Identified<UUID>{
    inventory: Inventory;
    record: Document;
    exemplarId: number;
    matchState: MatchState;
    previousMatchState: MatchState;
    barCode?: string | null;
    signature?: string | null;
    accessNumber?: string | null;
    location: Location;
    loanCategory: LoanCategory;
    thematicGroup: ThematicGroup;
    exemplarStatus: ExemplarStatus;
    customValue?: NamedLabeledIdentified<string> | null;
    chunksDelta: number;
}

export type MatchState = LabeledIdentified<number>;

export interface InventoryDetailViewConfiguration {
    exportsEnabled: boolean;
    exportLinks: Link[];
    printExportLinks: Link[];
}
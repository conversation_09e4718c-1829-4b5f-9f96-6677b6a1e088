<script lang="ts">
    import type {FileExplorerViewType} from '../../../lib/mv-types';
    import {RadioGroup, RadioGroupOption} from '@rgossiaux/svelte-headlessui';
    import {getMediaViewerContext} from '../../../lib/mv-context';
    import {getLocalization} from 'core/svelte-context/context';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let selectableViewTypes: FileExplorerViewType[];

    const context = getMediaViewerContext();
    const viewType = context.fileExplorerViewType;
    const localize = getLocalization();
</script>

<RadioGroup class="mv-view-switcher"
            aria-label="{localize(/* @kp-localization mediaviewer.fileExplorer.FileViewTypeLabel */ 'mediaviewer.fileExplorer.FileViewTypeLabel')}"
            bind:value={$viewType}>

    {#if selectableViewTypes.includes('tree')}
        <RadioGroupOption value="tree" class="{({checked}) => `group-option ${checked ? 'selected' : ''}`}">
            <div class="icon-container">
                <UIcon icon="chart-tree"
                       label="{localize(/* @kp-localization mediaviewer.fileExplorer.TreeViewLabel */ 'mediaviewer.fileExplorer.TreeViewLabel')}"/>
            </div>
        </RadioGroupOption>
    {/if}

    {#if selectableViewTypes.includes('grid')}
        <RadioGroupOption value="grid" class="{({checked}) => `group-option ${checked ? 'selected' : ''}`}">
            <div class="icon-container">
                <UIcon icon="grid"
                       label="{localize(/* @kp-localization mediaviewer.fileExplorer.GridViewLabel */ 'mediaviewer.fileExplorer.GridViewLabel')}"/>
            </div>
        </RadioGroupOption>
    {/if}

    {#if selectableViewTypes.includes('column')}
        <RadioGroupOption value="column" class="{({checked}) => `group-option ${checked ? 'selected' : ''}`}">
            <div class="icon-container">
                <UIcon icon="line-width"
                       label="{localize(/* @kp-localization mediaviewer.fileExplorer.ColumnViewLabel */ 'mediaviewer.fileExplorer.ColumnViewLabel')}"/>
            </div>
        </RadioGroupOption>
    {/if}
</RadioGroup>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    :global {
        .mv-view-switcher {
            width: 100%;
            height: @sidebar-view-switcher-height;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            border-radius: @border-radius-default;
            overflow: hidden;
            border: 1px solid var(--viewer-default-border);

            .group-option {
                flex: 1;
                height: 100%;
                display: flex;
                cursor: pointer;
                align-items: center;
                justify-content: center;
                background-color: var(--viewer-bg);
                border-right: 1px solid var(--viewer-default-border);
                transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out, opacity 0.3s ease-in-out;

                &:not(.selected):hover {
                    opacity: 0.75;

                    .icon-container {
                        transform: translateY(-1px);
                    }
                }

                &:last-child {
                    border-right: none;
                }

                &.selected {
                    color: white;
                    background-color: var(--accent-blue-new);
                }

                .icon-container {
                    transition: transform 0.3s ease-in-out;
                }
            }
        }
    }
</style>
<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import {fly} from 'svelte/transition';
    import {onDestroy, onMount} from 'svelte';
    import {getViewedContentContext} from '../../../lib/mv-viewed-content';
    import {exists} from 'shared/utils/custom-utils';
    import {get} from 'svelte/store';
    import {getLocalization} from 'core/svelte-context/context';
    import {TOGGLE_PDF_PRESENTATION_EVENT} from '../../../lib/mv-constants';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import MvFloatingControls from '../mv-floating-controls/MvFloatingControls.svelte';
    import MvPdfViewer from '../mv-pdf-viewer/MvPdfViewer.svelte';

    export let file: ViewableFile;
    const viewedContent = getViewedContentContext();
    const flyInAnimParams = {y: 20, duration: 250};
    const localize = getLocalization();

    let pdfViewerContainerElement: HTMLDivElement;

    let initialLoad = true;
    let loadError = false;
    let isFullscreen = false;

    onMount(() => {
        viewedContent.eventBus.addEventListener(TOGGLE_PDF_PRESENTATION_EVENT, handleTogglePdfPresentationMode);
    });

    onDestroy(() => {
        viewedContent.eventBus.removeEventListener(TOGGLE_PDF_PRESENTATION_EVENT, handleTogglePdfPresentationMode);
    });

    const handleLoadStart = () => {
        initialLoad = false;
    };

    const handleLoadError = () => {
        loadError = true;
        initialLoad = false;
    };

    const handleTogglePdfPresentationMode = () => {
        if (isFullscreen) {
            document.exitFullscreen();
            return;
        }

        pdfViewerContainerElement.requestFullscreen();
    };

    const handlePdfFullscreenChange = () => {
        isFullscreen = exists(document.fullscreenElement);

        if (!isFullscreen && get(viewedContent.presentationMode)) {
            viewedContent.setPresentationMode(false);
        }
    };
</script>

<div class="mv-pdf-file"
     on:fullscreenchange={handlePdfFullscreenChange}
     bind:this={pdfViewerContainerElement}>

    <MvPdfViewer {file}
                 on:load-start={handleLoadStart}
                 on:load-error={handleLoadError}/>

    {#if isFullscreen}
        <div class="fullscreen-floating-controls-container" in:fly={{y: 30}}>
            <MvFloatingControls/>
        </div>
    {/if}
</div>

{#if initialLoad}
    <div class="loading-container">
        <KpLoadingBlock size="sm"/>
    </div>
{/if}

{#if loadError}
    <div class="loading-container load-error-container" in:fly={flyInAnimParams}>
        <IconedContent icon="exclamation"
                       orientation="vertical"
                       align="center"
                       justify="center"
                       iconColor="var(--danger-red)">
            {localize(/* @kp-localization mediaviewer.content.PdfLoadError */ 'mediaviewer.content.PdfLoadError')}
        </IconedContent>
    </div>
{/if}

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-pdf-file {
        position: relative;
        width: 100%;
        height: 100%;

        .fullscreen-floating-controls-container {
            position: absolute;
            bottom: @spacing-ml;
            left: 50%;
            transform: translateX(-50%);
        }
    }

    .loading-container {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .load-error-container {
        color: var(--danger-red);
    }
</style>
import * as Pdfjs from 'pdfjs-dist';
import type {ViewedContentContext} from '../lib/mv-viewed-content';
import type {Logger} from 'core/logging/types';
import type {Observable} from 'rxjs';
import {MvPdfLinkManager} from '../components/mv-viewed-content/mv-pdf-viewer/mv-pdf-page/mv-pdf-link.manager';
import {MvDownloadManager} from '../components/mv-viewed-content/mv-pdf-viewer/mv-pdf-page/mv-download.manager';
import {BehaviorSubject} from 'rxjs';
import {exists} from 'shared/utils/custom-utils';

export type PdfjsModule = typeof Pdfjs;

export interface PdfRenderedPage {
    imgSrc: string;
    annotationsLayer: HTMLDivElement;
    textLayer: HTMLDivElement;
}

interface CanvasRenderer {
    canvasElement: HTMLCanvasElement;
    renderedPageNum?: number;
}

export class MvPdfRenderService {

    private static maxCanvasRenderers = 3;

    private readonly renderedPages: BehaviorSubject<Record<number, PdfRenderedPage>>;

    private readonly canvasRenderers: CanvasRenderer[] = [];
    private readonly queue: number[] = [];

    constructor(private readonly logger: Logger,
                private readonly pdfDocument: Pdfjs.PDFDocumentProxy,
                private readonly pdfViewport: Pdfjs.PageViewport,
                private readonly viewedContentContext: ViewedContentContext) {

        this.renderedPages = new BehaviorSubject<Record<number, PdfRenderedPage>>({});

        for (let i = 1; i < MvPdfRenderService.maxCanvasRenderers; i++) {
            this.canvasRenderers.push({
                canvasElement: document.createElement('canvas'),
                renderedPageNum: null
            });
        }
    }

    public queuePageForRender(pageNumber: number): void {
        if (exists(this.renderedPages.value[pageNumber]) || exists(this.canvasRenderers.find((renderer) => renderer.renderedPageNum === pageNumber))) {
            // Page already rendered, skip it
            return;
        }

        const availableRenderer = this.canvasRenderers.find((renderer) => !exists(renderer.renderedPageNum));

        if (!exists(availableRenderer)) {
            if (!this.queue.includes(pageNumber)) {
                this.queue.push(pageNumber);
            }

            return;
        }

        availableRenderer.renderedPageNum = pageNumber;

        this.renderPage(pageNumber, availableRenderer).then((renderedPage) => {
            if (exists(renderedPage)) {
                this.renderedPages.next({
                    ...this.renderedPages.value,
                    [pageNumber]: renderedPage
                });
            }

            availableRenderer.renderedPageNum = null;
            this.processQueue();
        });
    }

    public getRenderedPages$(): Observable<Record<number, PdfRenderedPage>> {
        return this.renderedPages.asObservable();
    }

    private processQueue(): void {
        if (this.queue.length > 0) {
            this.queuePageForRender(this.queue.shift());
        }
    }

    private async renderPage(pageNumber: number, availableRenderer: CanvasRenderer): Promise<PdfRenderedPage | null> {
        const page = await this.pdfDocument.getPage(pageNumber);

        availableRenderer.canvasElement.height = this.pdfViewport.height * window.devicePixelRatio;
        availableRenderer.canvasElement.width = this.pdfViewport.width * window.devicePixelRatio;
        availableRenderer.canvasElement.style.width = `${this.pdfViewport.width}px`;
        availableRenderer.canvasElement.style.height = `${this.pdfViewport.height}px`;
        const canvasContext = availableRenderer.canvasElement.getContext('2d');

        canvasContext.scale(window.devicePixelRatio, window.devicePixelRatio);

        const renderContext = {
            enableWebGL: true,
            intent: 'any',
            canvasContext,
            viewport: this.pdfViewport.clone()
        };

        try {
            await page.render(renderContext).promise;
            const renderedPageImageSource = availableRenderer.canvasElement.toDataURL('image/png');

            const renderedPageData = {
                imgSrc: renderedPageImageSource,
                annotationsLayer: await this.createAnnotationsLayerElement(page),
                textLayer: await this.createTextLayerElement(page)
            };

            await this.cleanupPage(page);
            return renderedPageData;
        } catch (e) {
            this.logger.error(`An error occurred while rendering PDF page #${page.pageNumber}`, e);
            return null;
        }
    }

    // Renderers
    private async createAnnotationsLayerElement(page: Pdfjs.PDFPageProxy): Promise<HTMLDivElement> {
        const annotationData = await page.getAnnotations();

        const annotationsLayerElement = document.createElement('div');
        annotationsLayerElement.id = `annotation-layer-${page.pageNumber}`;
        annotationsLayerElement.className = 'annotationLayer';

        const annotationLayer = new Pdfjs.AnnotationLayer({
            viewport: this.pdfViewport.clone(),
            div: annotationsLayerElement,
            structTreeLayer: null,
            accessibilityManager: null,
            annotationCanvasMap: null,
            annotationEditorUIManager: null,
            page
        });

        await annotationLayer.render({
            viewport: this.pdfViewport.clone(),
            div: annotationsLayerElement,
            page,
            annotations: annotationData,
            linkService: new MvPdfLinkManager(this.viewedContentContext, this.pdfDocument),
            downloadManager: new MvDownloadManager(),
            renderForms: false
        });

        annotationsLayerElement.style.width = '100%';
        annotationsLayerElement.style.height = '100%';

        return annotationsLayerElement;
    }

    private async createTextLayerElement(page: Pdfjs.PDFPageProxy): Promise<HTMLDivElement> {
        const textData = await page.getTextContent();

        const textLayerElement = document.createElement('div');
        textLayerElement.id = `text-layer-${page.pageNumber}`;
        textLayerElement.className = 'textLayer';

        const textLayer = new Pdfjs.TextLayer({
            viewport: this.pdfViewport.clone(),
            textContentSource: textData,
            container: textLayerElement
        });

        await textLayer.render();

        textLayerElement.style.width = '100%';
        textLayerElement.style.height = '100%';

        return textLayerElement;
    }

    private cleanupPage(page: Pdfjs.PDFPageProxy, attempts = 0, maxAttempts = 3): Promise<boolean> {
        return new Promise((resolve, reject) => {
            const success = page.cleanup(true);
            if (success) {
                resolve(success);
            } else if (attempts <= maxAttempts) {
                setTimeout(() => {
                    resolve(this.cleanupPage(page, attempts + 1));
                }, 100 * attempts);
            } else {
                reject(new Error('Could not clean up the page'));
            }
        });
    }
}
import {RECORD_COLLECTION_CATEGORY_ID} from './constants';
import type {RecordCollection} from './types';

export function isFavouritesCollection(recordCollection: RecordCollection): boolean {
    return recordCollection.recordCollectionCategory.id === RECORD_COLLECTION_CATEGORY_ID.FAVOURITES;
}

export function isPublicCollection(recordCollection: RecordCollection): boolean {
    return recordCollection.recordCollectionCategory.id === RECORD_COLLECTION_CATEGORY_ID.OTHER;
}

export function isRecordCollectionCategoryEqual(recordCollection1: RecordCollection, recordCollection3: RecordCollection) {
    return recordCollection1.recordCollectionCategory.id === recordCollection3.recordCollectionCategory.id;
}
<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getInjector, getLogger} from 'core/svelte-context/context';
    import KpModalContent from 'src/modals/kp-modal/KpModalContent.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpModalFooterCloseButton from 'src/modals/kp-modal/KpModalFooterCloseButton.svelte';
    import KpModalHeaderCloseButton from 'src/modals/kp-modal/KpModalHeaderCloseButton.svelte';
    import {ModalDialogService} from 'shared/modal-dialogs/modal-dialog.service';
    import type {Field8Model} from 'src/features/record-editation/field7-and-field8-editors/utils/types';
    import type {KpUniversalFormSettings} from 'shared/value-editors/kp-universal-form/types';
    import {exists} from 'shared/utils/custom-utils';
    import {createPayloadActionResponse} from 'src/modals/modal-utils';
    import type {FormGroup} from 'shared/value-editors/internal/forms/form-group';
    import type {Field} from 'node_modules/svelte-forms/types';
    import KpUniversalForm from 'shared/value-editors/kp-universal-form/KpUniversalForm.svelte';
    import {Field8EditorServiceFactory} from './field8-editor-service.factory';
    import {onMount} from 'svelte';
    import {getCodesByPosition, getLabelsAndCodesByDocumentTypeCode} from '../utils/utils';
    import type {Field008Code, Field008Label} from 'typings/portaro.be.types';
    import {IncorrectSectionFormatError, MissingDocumentTypesError, UnknownSectionsCodesError} from './field8-errors';
    import KpModalTitle from 'src/modals/kp-modal/KpModalTitle.svelte';
    import type {Observable} from 'rxjs';
    import type {
        Field8EditorModalModel
    } from 'src/features/record-editation/field7-and-field8-editors/field8-editor-modal/types';

    export let model: Field8EditorModalModel;
    export let modalWindowActions: ModalWindowActions;

    const logger = getLogger();
    const modalDialogService = getInjector().getByClass(ModalDialogService);
    const editorService = getInjector().getByClass(Field8EditorServiceFactory).createService(model.definitions);

    let formModel: Field8Model;
    let formSettings: KpUniversalFormSettings<Field8Model>; // form fields definition
    let possibleFormModels: Map<string, Field8Model>; // MAP documentTypeCode -> formModel for documentTypeCode;
    let lastDocumentTypeCode: string;
    let lastPublicationStatusCode: string;

    let formRef: {getFormController: () => FormGroup<Field8Model>};
    $: if (exists(formRef)) {
        formState$ = formRef.getFormController().getFieldState$();
    }
    let formState$: Observable<Field<any>>;
    $: isFormValid = $formState$?.valid ?? false;

    let error: Error | null = null;
    $: hasError = exists(error);

    onMount(async () => {
        const allowedDocumentTypes = model.definitions.documentTypes
            .filter((documentType) => model.fond.documentTypes.includes(documentType.code));

        if (allowedDocumentTypes.length === 0) {
            error = new Error('Žádné povolené typy dokumentu - změňte nastavení fondu');
            logger.error(error, 'Fond editovaného záznamu nemá nastavené žádné povolené typy dokumentu');
            return; // show error and stop form initialization
        }

        formSettings = editorService.initializeFormsFirstSegment(allowedDocumentTypes);
        try {
            possibleFormModels = editorService.parseCode(model.value); // parse code for every document type
        } catch (err: unknown) {
            await handleParsingErrors(err);
        }

        if (possibleFormModels.size === 0) { // empty or invalid field8 value (because no model was parsed)
            const defaultDocumentType = allowedDocumentTypes.at(0);
            formModel = editorService.createDefaultSharedValuesModel(defaultDocumentType);
            updateFormAndModel(formModel.documentType.code);
        } else { // editing existing field8 value
            const allowedModels = Array.from(possibleFormModels.values())
                .filter((possibleFormModel) => allowedDocumentTypes.includes(possibleFormModel.documentType)); // at least 1 document type is allowed => never empty
            formModel = allowedModels.at(0); // get first available model
            updateForm(formModel.documentType.code);
        }

        lastDocumentTypeCode = formModel.documentType.code;
        lastPublicationStatusCode = formModel.publicationStatus.code;
    })

    function onChange() {
        if (formModel.documentType.code !== lastDocumentTypeCode) { // changed document type
            updateFormAndModel(formModel.documentType.code); // reset form based on new document type
            lastDocumentTypeCode = formModel.documentType.code; // save last document type
        }
        if (formModel.publicationStatus.code !== lastPublicationStatusCode) { // changed publication status
            editorService.setDefaultDatesForPublicationStatusToModel(formModel);
            formSettings = editorService.updateFormsDate2Label(formSettings, formModel.publicationStatus.code);
            lastPublicationStatusCode = formModel.publicationStatus.code; // save last publication status
        }
    }

    function submit() {
        if (!isFormValid || hasError) {
            return;
        }

        modalWindowActions.submitPromise(Promise.resolve(createPayloadActionResponse(editorService.serializeModel(formModel))));
    }

    function updateForm(code: string) {
        const [labels, codes] = getLabelsAndCodesByDocumentTypeCode(model.definitions, code);
        formSettings = editorService.initializeFormsSecondSegment(formSettings, labels, codes); // change form according to document type
        formSettings = editorService.updateFormsDate2Label(formSettings, formModel.publicationStatus.code); // change date2 label according to publication status
    }

    function updateFormAndModel(code: string) {
        const [labels, codes] = getLabelsAndCodesByDocumentTypeCode(model.definitions, code);
        formSettings = editorService.initializeFormsSecondSegment(formSettings, labels, codes); // change form according to document type
        updateModel(labels, codes);
    }

    function updateModel(labels: Field008Label[], codes: Field008Code[]) {
        if (possibleFormModels.has(formModel.documentType.code)) { // get existing model corresponding to document type
            const possibleFormModel = possibleFormModels.get(formModel.documentType.code);
            // copy data from first segment (shared values)
            possibleFormModel.documentType = formModel.documentType;
            possibleFormModel.catalogingSource = formModel.catalogingSource
            possibleFormModel.placeOfPublication = formModel.placeOfPublication
            possibleFormModel.language = formModel.language
            possibleFormModel.date2 = formModel.date2
            possibleFormModel.date1 = formModel.date1
            possibleFormModel.publicationStatus = formModel.publicationStatus
            possibleFormModel.modifiedRecord = formModel.modifiedRecord
            possibleFormModel.dateEnteredOnFile = formModel.dateEnteredOnFile
            formModel = possibleFormModel; // update form model
        } else { // create new model
            formModel = { // take shared values
                dateEnteredOnFile: formModel.dateEnteredOnFile,
                documentType: formModel.documentType,
                catalogingSource: formModel.catalogingSource,
                placeOfPublication: formModel.placeOfPublication,
                language: formModel.language,
                date2: formModel.date2,
                date1: formModel.date1,
                publicationStatus: formModel.publicationStatus,
                modifiedRecord: formModel.modifiedRecord
            };
            labels
                .filter((label) => label.isDefined) // some undefined positions have default values
                .forEach((label) => editorService.setPositionsDefaultValueToModel(formModel, label.order, getCodesByPosition(codes, label.order))); // set defaults in second segment
        }
        possibleFormModels.set(formModel.documentType.code, formModel); // save updated model
    }

    async function handleParsingErrors(err: unknown) {
        logger.error(err);
        if (err instanceof IncorrectSectionFormatError) {
            const errorMessage = `hodnota [${err.sectionValue}] sekce [${err.section}] má chybný formát`;
            await askUserToUseDefaults(errorMessage);
        } else if (err instanceof UnknownSectionsCodesError) {
            const errorMessage = `pro kódy ze sekcí [${err.sectionsWithUnknownCodes.join(', ')}] nebyla nalezena definovaná hodnota`;
            await askUserToUseDefaults(errorMessage);
        } else if (err instanceof MissingDocumentTypesError) {
            const errorMessage = 'chybí definice kódů pro typy dokumentu';
            await askUserToUseDefaults(errorMessage);
        } else {
            throw new Error('Unknown field8 parsing error');
        }
    }

    async function askUserToUseDefaults(errorMessage: string) {
        await modalDialogService.openModalWindow('confirmationDialog', {text: `Pole 008 má chybnou strukturu (${errorMessage}). Chcete nastavit výchozí hodnoty?`})
        possibleFormModels = new Map();
    }
</script>


<KpModalContent {modalWindowActions} additionalClasses="field8-editor-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>

        <KpModalTitle>
            Údaje pevné délky
        </KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        {#if exists(formSettings)}
            <KpUniversalForm formId="{formSettings.id}"
                             {formSettings}
                             bind:model={formModel}
                             on:model-change={onChange}
                             bind:this={formRef}/>
        {/if}

        {#if hasError}
            <div class="alert alert-danger text-center">
                <span>{error.message}</span>
            </div>
        {/if}
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpButton buttonStyle="primary" isDisabled="{!isFormValid || hasError}" on:click={submit}>
            OK
        </KpButton>
        <KpModalFooterCloseButton/>
    </svelte:fragment>
</KpModalContent>


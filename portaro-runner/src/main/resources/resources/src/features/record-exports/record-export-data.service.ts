import {ngAsync} from 'shared/utils/ng-@decorators';
import {transferify} from 'shared/utils/data-service-utils';
import type {ActionResponse, ConfirmableRequest, RecordExport} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';

export class RecordExportDataService {
    public static serviceName = 'recordExportDataService';

    public static readonly ROUTE = 'record-exports';
    public static readonly EDIT_ROUTE = 'record-exports/edit';


    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async getAll(): Promise<RecordExport[]> {
        return this.ajaxService
            .createRequest(`${RecordExportDataService.ROUTE}`)
            .get();
    }

    @ngAsync()
    public async create(recordExportRequest: RecordExport | ConfirmableRequest): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${RecordExportDataService.ROUTE}`)
            .post(transferify(recordExportRequest));
    }

    @ngAsync()
    public async edit(recordExportRequest: RecordExport): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${RecordExportDataService.EDIT_ROUTE}`)
            .post(transferify({...recordExportRequest, confirmed: false}));
    }
}
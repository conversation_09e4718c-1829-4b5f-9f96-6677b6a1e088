import register from '@kpsys/angularjs-register';
import recordExportRoutes from './record-export.routes';
import {RecordExportDataService} from './record-export-data.service';
import {RecordExportService} from './record-export.service';


export default register('portaro.features.record-export')
    .config(recordExportRoutes)
    .service(RecordExportDataService.serviceName, RecordExportDataService)
    .service(RecordExportService.serviceName, RecordExportService)
    .name();
import type {NonEmptyArray} from 'shared/utils/array-utils';
import {exists, isNullOrUndefined} from 'shared/utils/custom-utils';
import type {GridField, SimpleGridFieldValue} from 'src/features/record-grid/lib/types';
import type {FieldIdAndFieldValue} from './types';
import {isErrorGridFieldValue, isScalarGridValue} from 'src/features/record-grid/lib/types-utils';
import type {Datatype, FieldTypeId} from 'typings/portaro.be.types';
import {convertFieldTypeIdToFieldId, splitFieldTypeId} from 'shared/utils/record-field-utils';


export function isInvalid(validatedFields: NonEmptyArray<FieldIdAndFieldValue<GridField<SimpleGridFieldValue>>>, isRequired: boolean) {
    if (!isRequired) {
        return false;
    }
    return validatedFields.some((field) => isInvalidField(field.value))
}

export function isInvalidField(field: GridField<SimpleGridFieldValue>) {
    if (isNullOrUndefined(field)) {
        return true;
    }
    if (isErrorGridFieldValue(field)) {
        return true;
    }
    if (isScalarGridValue(field)) {
        return isNullOrUndefined(field.value);
    }
    return false;
}

export function generateIdOfInitialField(fieldTypeId: FieldTypeId) {
    return convertFieldTypeIdToFieldId(fieldTypeId, splitFieldTypeId(fieldTypeId).map(() => 0));
}

export function isDateOrNumericDatatype(datatype: Datatype | null): boolean {
    return exists(datatype) && (datatype.name === 'DATE' || datatype.name === 'NUMBER' || datatype.name === 'NUMBER_DECIMAL_2');
}
<script lang="ts" generics="ROW">
    import type {Header, Table} from '@tanstack/svelte-table';
    import {getGridContext} from 'shared/ui-widgets/grid/grid.context';

    export let table: Table<ROW>;
    export let header: Header<ROW, unknown>;
    export let isLastColumn: boolean;

    const {settings: {mainGroupsDivided}} = getGridContext();

    $: isColumnGroupBorder = mainGroupsDivided && header.index === header.headerGroup.headers.length - 1;
</script>

{#if header.column.getCanResize()}
    <!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
    <div class="kp-column-resizer"
         role="separator"
         style:transform="{header.column.getIsResizing() ? `translate(${table.getState().columnSizingInfo.deltaOffset}px, -50%)`: 'translateY(-50%)'}"
         class:dragging={header.column.getIsResizing()}
         class:last-column={isLastColumn}
         class:column-group-border={isColumnGroupBorder}
         on:touchstart={header.getResizeHandler()}
         on:mousedown={header.getResizeHandler()}>
    </div>
{/if}

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    @default-border-width: 1px;
    @resizer-hover: #777;
    @base-resizer-width: 10px;

    .kp-column-resizer {
        --resizer-width: calc(@base-resizer-width + var(--col-border-width, @default-border-width)); // 10 + 1
        height: 18px;
        width: var(--resizer-width);
        right: calc(-1 * ((var(--resizer-width) + var(--col-border-width, @default-border-width)) / 2)); // - (11 + 1) / 2
        top: 50%;
        z-index: 1;
        cursor: col-resize;
        border-left: 2px solid transparent;
        border-right: 2px solid transparent;
        position: absolute;
        transition: border-left-color 0.2s ease-in-out, border-right-color 0.2s ease-in-out;

        &.column-group-border {
            --col-border-width: 2px; // TODO: check if it works ok
        }

        &.last-column {
            right: 0;
            width: (@base-resizer-width / 2); // 10 / 2
            border-right: none;
        }

        &:not(.dragging):hover {
            border-left-color: @resizer-hover;
            border-right-color: @resizer-hover;
        }

        &.dragging {
            border-left-color: var(--brand-orange-new);
            border-right-color: var(--brand-orange-new);
        }
    }
</style>
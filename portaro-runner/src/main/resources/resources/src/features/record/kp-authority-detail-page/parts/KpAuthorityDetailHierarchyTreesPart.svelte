<script lang="ts">
    import type {AuthorityDetailReactivePageData, AuthorityDetailStaticPageData, AuthorityHierarchyTree} from '../types';
    import {exists} from 'shared/utils/custom-utils';
    import {onDestroy} from 'svelte';
    import {getPageContext} from 'shared/layouts/page-context';
    import KpRecordHierarchyTrees from '../../kp-record-hierarchy-trees/KpRecordHierarchyTrees.svelte';

    const pageContext = getPageContext<AuthorityDetailStaticPageData, AuthorityDetailReactivePageData>();

    let hierarchyTrees: AuthorityHierarchyTree[] | undefined;
    const reactiveDataUnsubscribe = pageContext.reactiveData.subscribe((data) => hierarchyTrees = data.hierarchyTrees);
    onDestroy(reactiveDataUnsubscribe);
</script>

{#if exists(hierarchyTrees) && hierarchyTrees.length > 0}
    <KpRecordHierarchyTrees {hierarchyTrees}/>
{/if}
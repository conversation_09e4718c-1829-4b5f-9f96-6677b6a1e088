<script lang="ts">
    import type {DocumentDetailReactivePageData, DocumentDetailStaticPageData} from '../types';
    import type ExemplarList from '../../exemplar-list';
    import {exists} from 'shared/utils/custom-utils';
    import {onDestroy} from 'svelte';
    import {getPageContext} from 'shared/layouts/page-context';
    import KpExemplars from '../../kp-exemplars/KpExemplars.svelte';

    const pageContext = getPageContext<DocumentDetailStaticPageData, DocumentDetailReactivePageData>();

    const model = pageContext.staticData.model;
    let exemplarList: ExemplarList | undefined;
    const reactiveDataUnsubscribe = pageContext.reactiveData.subscribe((data) => exemplarList = data.exemplarList);
    onDestroy(reactiveDataUnsubscribe);
</script>

{#if exists(exemplarList) && model.documentTabsConfiguration.exemplars.showable && model.exemplarTabsByBuildingsEnabled}
    <KpExemplars {exemplarList}
                 document="{model.record}"
                 exemplarsListProperties="{model.exemplarsView.exemplarColumns}"
                 exemplarTabsByBuildingsEnabled="{model.exemplarTabsByBuildingsEnabled}"/>
{/if}
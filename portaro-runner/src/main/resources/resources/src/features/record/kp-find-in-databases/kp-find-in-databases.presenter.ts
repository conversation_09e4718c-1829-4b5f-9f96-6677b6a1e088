import type ForeignDocumentDataService from './foreign-document.data-service';
import type {ForeignOccurrenceWithAvailability} from './types';

export class KpFindInDatabasesPresenter {
    public static presenterName = 'kpFindInDatabasesPresenter';

    /*@ngInject*/
    constructor(private foreignDocumentDataService: ForeignDocumentDataService) {
    }

    public async getForeignOccurrences(recordId: string): Promise<ForeignOccurrenceWithAvailability[]> {
        const foreignOccurrences = await this.foreignDocumentDataService.getDocumentOccurrences(recordId) as ForeignOccurrenceWithAvailability[];
        for (const foreignOccurrence of foreignOccurrences) {
            foreignOccurrence.availability = await this.foreignDocumentDataService.getDocumentAvailability(foreignOccurrence.id);
        }
        return foreignOccurrences
    }
}
<script lang="ts">
    import type {SdiRequest, User} from 'typings/portaro.be.types';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {KpSdiRequestsPanelPresenter} from './kp-sdi-requests-panel.presenter';
    import {onMount} from 'svelte';
    import KpSdiRequestListItem from './KpSdiRequestListItem.svelte';
    import KpGenericPanel from 'shared/ui-widgets/panel/KpGenericPanel.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import {removeAll, byReference} from 'shared/utils/array-utils';
    import {flip} from 'svelte/animate';
    import {popInAnim, popOutAnim} from 'shared/animations/pop-animations';

    export let user: User;

    const localize = getLocalization();
    const presenter = getInjector().getByToken<KpSdiRequestsPanelPresenter>(KpSdiRequestsPanelPresenter.presenterName);
    const requestsListFlipAnimationParams = {duration: 250};

    let loading = true;
    let requests: SdiRequest[] = [];
    $: noRequestItems = !loading && requests.length === 0;

    onMount(async () => {
        requests = await presenter.loadRequests(user);
        loading = false;
    });

    const handleRequestRemove = (event: CustomEvent<SdiRequest>) => {
        requests = removeAll(requests, byReference(event.detail));
    }
</script>

<KpGenericPanel additionalClasses="sdi-requests-container"
                hasBodyPadding="{noRequestItems}">

    <svelte:fragment slot="heading">
        {localize(/* @kp-localization sdi.SdiRequests */ 'sdi.SdiRequests')}
    </svelte:fragment>

    <svelte:fragment>
        {#if loading}
            <div class="loading-container">
                <KpLoadingBlock size="sm"/>
            </div>
        {/if}

        {#if noRequestItems}
            <div class="text-muted text-center">
                {localize(/* @kp-localization commons.ZadnePolozky */ 'commons.ZadnePolozky')}
            </div>
        {/if}

        {#if !loading || requests.length > 0}
            <ul class="sdi-requests-list">
                {#each requests as request(request.id)}
                    <li class="sdi-request-list-item"
                        animate:flip={requestsListFlipAnimationParams}
                        in:popInAnim={{key: request.id}}
                        out:popOutAnim={{key: request.id}}>

                        <KpSdiRequestListItem {request}
                                              showUser="{false}"
                                              on:request-remove={handleRequestRemove}/>
                    </li>
                {/each}
            </ul>
        {/if}
    </svelte:fragment>
</KpGenericPanel>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    .loading-container {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .sdi-requests-list {
        display: flex;
        flex-direction: column;

        .sdi-request-list-item {
            border-bottom: 1px solid @panel-default-border;
            padding: @panel-body-padding;

            &:last-child {
                border-bottom: none;
            }
        }
    }
</style>
<script lang="ts" generics="ITEM">
    import type {SearchParams, ViewableSearch} from 'typings/portaro.be.types';
    import type {PaginationData} from '../search-manager/pagination';
    import type {Observable} from 'rxjs';
    import type {SearchManager} from '../search-manager/search-manager';
    import {exists} from 'shared/utils/custom-utils';
    import {createRender, Render} from 'svelte-render';
    import KpSearchResultTableExemplars from './kp-search-result-table-exemplars/KpSearchResultTableExemplars.svelte';
    import KpSearchResultTableLoans from './kp-search-result-table-loans/KpSearchResultTableLoans.svelte';
    import KpSearchResultTableLoanReminders from './kp-search-result-table-loan-reminders/KpSearchResultTableLoanReminders.svelte';
    import KpSearchResultTableMessages from './kp-search-result-table-messages/KpSearchResultTableMessages.svelte';
    import KpSearchItemAuthority from './kp-search-item-authority/KpSearchItemAuthority.svelte';
    import KpSearchItemFile from '../../file/KpSearchItemFile.svelte';
    import KpSearchItemRecordOperation from '../../record/operation/KpSearchItemRecordOperation.svelte';
    import KpSearchItemInventory from '../../inventory/kp-search-item-inventory/KpSearchItemInventory.svelte';
    import KpSearchItemPayment from '../../payment/KpSearchItemPayment.svelte';
    import KpSearchItemDocument from './kp-search-item-document/KpSearchItemDocument.svelte';
    import KpSearchItemCapture from '../../inventory/kp-search-item-capture/KpSearchItemCapture.svelte';
    import KpSearchItemMatch from '../../inventory/kp-search-item-match/KpSearchItemMatch.svelte';
    import SeekedSeekingSearchItem from './seeking-search-item/SeekedSeekingSearchItem.svelte';
    import ProvidedSeekingSearchItem from './seeking-search-item/ProvidedSeekingSearchItem.svelte';
    import KpSearchItemUser from './kp-search-item-user/KpSearchItemUser.svelte';
    import {
        isAuthority,
        isCapture,
        isDocument,
        isFile,
        isInventory,
        isMatch,
        isPayment,
        isProvidedSeeking,
        isRecordOperation,
        isSeekedSeeking,
        isUser,
        SearchResultResolver
    } from './search-result-resolver';

    export let searchManager: SearchManager<any>;
    export let paginationData: PaginationData<any>;

    let lastSearch: Observable<ViewableSearch<SearchParams, ITEM>>;
    $: lastSearch = searchManager.getLastSearch$();

    const resolver = new SearchResultResolver()
        .register(isAuthority, (record) => ({renderConfig: createRender(KpSearchItemAuthority, {record}), cssClass: 'searched-authority'}))
        .register(isDocument, (record) => ({renderConfig: createRender(KpSearchItemDocument, {record, searchManager}), cssClass: 'searched-document'}))
        .register(isUser, (record) => ({renderConfig: createRender(KpSearchItemUser, {user: record, refreshSearchManager: searchManager}), cssClass: 'searched-user'}))
        .register(isFile, (record) => ({renderConfig: createRender(KpSearchItemFile, {file: record}), cssClass: 'searched-file'}))
        .register(isPayment, (record) => ({renderConfig: createRender(KpSearchItemPayment, {payment: record}), cssClass: 'searched-payment'}))
        .register(isRecordOperation, (record) => ({renderConfig: createRender(KpSearchItemRecordOperation, {recordOperation: record}), cssClass: 'searched-record-operation'}))
        .register(isInventory, (record) => ({renderConfig: createRender(KpSearchItemInventory, {inventory: record}), cssClass: 'searched-inventory'}))
        .register(isCapture, (record) => ({renderConfig: createRender(KpSearchItemCapture, {capture: record}), cssClass: 'searched-capture'}))
        .register(isMatch, (record) => ({renderConfig: createRender(KpSearchItemMatch, {match: record}), cssClass: 'searched-match'}))
        .register(isSeekedSeeking, (record) => ({renderConfig: createRender(SeekedSeekingSearchItem, {seeking: record, refreshSearchManager: searchManager}), cssClass: 'seeked-seeking'}))
        .register(isProvidedSeeking, (record) => ({renderConfig: createRender(ProvidedSeekingSearchItem, {seeking: record, refreshSearchManager: searchManager}), cssClass: 'provided-seeking'}));
</script>

{#if exists($lastSearch)}
    {#if $lastSearch.type === 'exemplar-search'}
        <KpSearchResultTableExemplars exemplarsPaginationData="{paginationData}"/>
    {:else if $lastSearch.type === 'loan-search'}
        <KpSearchResultTableLoans loansPaginationData="{paginationData}"/>
    {:else if $lastSearch.type === 'loan-reminder-search'}
        <KpSearchResultTableLoanReminders loanRemindersPaginationData="{paginationData}"/>
    {:else if $lastSearch.type === 'message-search'}
        <KpSearchResultTableMessages messagesPaginationData="{paginationData}"/>
    {:else}
        <ul class="search-result-list">
            {#each paginationData.pages as page(page)}
                {#each page.items as item, index(item.id)}
                    {@const searchItemView = resolver.resolve(item)}

                    <li class="search-item search-item-page-{page.pageNumber} search-item-index-{index}">
                        <div class="record-list-item zaznamListItem {searchItemView.cssClass}">
                            <Render of="{searchItemView.renderConfig}"/>
                        </div>
                    </li>
                {/each}
            {/each}
        </ul>
    {/if}
{/if}

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .search-result-list {
        list-style-type: none;
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: column;
        gap: @spacing-ml;
    }

    :global {
        .zaznamListItem {
            border-radius: @border-radius-large;
            outline: 1px solid transparent;
            transition: outline-color 0.3s ease-in-out;

            &:hover {
                outline-color: var(--accent-blue-new);
            }

            .nazevZaznamu {
                a {
                    color: darkblue;
                    text-decoration: none;
                    font-size: 1.2em;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }

            .record-subtitle {
                display: none;
            }
        }
    }
</style>
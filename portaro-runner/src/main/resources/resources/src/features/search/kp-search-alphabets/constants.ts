import type {Alphabet} from './types';

export const A_Z_SORTING_ID = 'start';
export const ALPHABETS_WITH_DIACRITICAL_LETTERS = ['latin'];

export const ALPHABETS: Alphabet[] = [
    {
        id: A_Z_SORTING_ID,
        value: [
            {id: A_Z_SORTING_ID, text: 'START', value: ''}
        ]
    },
    {
        id: 'numbers',
        value: [
            {id: '1', text: '1', value: '1'},
            {id: '2', text: '2', value: '2'},
            {id: '3', text: '3', value: '3'},
            {id: '4', text: '4', value: '4'},
            {id: '5', text: '5', value: '5'},
            {id: '6', text: '6', value: '6'},
            {id: '7', text: '7', value: '7'},
            {id: '8', text: '8', value: '8'},
            {id: '9', text: '9', value: '9'},
            {id: '0', text: '0', value: '0'},
        ]
    },
    {
        id: 'cyrillic',
        value: [
            {id: 'а', text: 'А', value: 'А'},
            {id: 'б', text: 'Б', value: 'Б'},
            {id: 'в', text: 'В', value: 'В'},
            {id: 'г', text: 'Г', value: 'Г'},
            {id: 'д', text: 'Д', value: 'Д'},
            {id: 'е', text: 'Е', value: 'Е'},
            {id: 'ж', text: 'Ж', value: 'Ж'},
            {id: 'з', text: 'З', value: 'З'},
            {id: 'и', text: 'И', value: 'И'},
            {id: 'й', text: 'Й', value: 'Й'},
            {id: 'к', text: 'К', value: 'К'},
            {id: 'л', text: 'Л', value: 'Л'},
            {id: 'м', text: 'М', value: 'М'},
            {id: 'н', text: 'Н', value: 'Н'},
            {id: 'о', text: 'О', value: 'О'},
            {id: 'п', text: 'П', value: 'П'},
            {id: 'р', text: 'Р', value: 'Р'},
            {id: 'с', text: 'С', value: 'С'},
            {id: 'т', text: 'Т', value: 'Т'},
            {id: 'у', text: 'У', value: 'У'},
            {id: 'ф', text: 'Ф', value: 'Ф'},
            {id: 'х', text: 'Х', value: 'Х'},
            {id: 'ц', text: 'Ц', value: 'Ц'},
            {id: 'ч', text: 'Ч', value: 'Ч'},
            {id: 'ш', text: 'Ш', value: 'Ш'},
            {id: 'щ', text: 'Щ', value: 'Щ'},
            {id: 'ъ', text: 'Ъ', value: 'Ъ'},
            {id: 'ю', text: 'Ю', value: 'Ю'},
            {id: 'я', text: 'Я', value: 'Я'}
        ]
    },
    {
        id: 'latin',
        value: [
            {
                id: 'a', text: 'A', value: 'A', diacriticalLetters: [
                    {id: 'á', text: 'Á', value: 'Á'},
                    {id: 'ä', text: 'Ä', value: 'Ä'},
                    {id: 'à', text: 'À', value: 'À'},
                    {id: 'â', text: 'Â', value: 'Â'},
                    {id: 'ã', text: 'Ã', value: 'Ã'},
                    {id: 'å', text: 'Å', value: 'Å'},
                    {id: 'ā', text: 'Ā', value: 'Ā'}
                ]
            },
            {id: 'b', text: 'B', value: 'B'},
            {
                id: 'c', text: 'C', value: 'C', diacriticalLetters: [
                    {id: 'č', text: 'Č', value: 'Č'},
                    {id: 'ç', text: 'Ç', value: 'Ç'},
                    {id: 'ĉ', text: 'Ĉ', value: 'Ĉ'}
                ]
            },
            {
                id: 'd', text: 'D', value: 'D', diacriticalLetters: [
                    {id: 'ď', text: 'Ď', value: 'Ď'}
                ]
            },
            {
                id: 'e', text: 'E', value: 'E', diacriticalLetters: [
                    {id: 'é', text: 'É', value: 'É'},
                    {id: 'ě', text: 'Ě', value: 'Ě'},
                    {id: 'è', text: 'È', value: 'È'},
                    {id: 'ê', text: 'Ê', value: 'Ê'},
                    {id: 'ë', text: 'Ë', value: 'Ë'},
                    {id: 'ē', text: 'Ē', value: 'Ē'}
                ]
            },
            {id: 'f', text: 'F', value: 'F'},
            {
                id: 'g', text: 'G', value: 'G', diacriticalLetters: [
                    {id: 'ĝ', text: 'Ĝ', value: 'Ĝ'}
                ]
            },
            {
                id: 'h', text: 'H', value: 'H', diacriticalLetters: [
                    {id: 'ĥ', text: 'Ĥ', value: 'Ĥ'}
                ]
            },
            {
                id: 'ch', text: 'Ch', value: 'Ch'
            },
            {
                id: 'i', text: 'I', value: 'I', diacriticalLetters: [
                    {id: 'í', text: 'Í', value: 'Í'},
                    {id: 'ì', text: 'Ì', value: 'Ì'},
                    {id: 'î', text: 'Î', value: 'Î'},
                    {id: 'ï', text: 'Ï', value: 'Ï'},
                    {id: 'ī', text: 'Ī', value: 'Ī'}
                ]
            },
            {
                id: 'j', text: 'J', value: 'J', diacriticalLetters: [
                    {id: 'ĵ', text: 'Ĵ', value: 'Ĵ'}
                ]
            },
            {id: 'k', text: 'K', value: 'K'},
            {
                id: 'l', text: 'L', value: 'L', diacriticalLetters: [
                    {id: 'ĺ', text: 'Ĺ', value: 'Ĺ'},
                    {id: 'ľ', text: 'Ľ', value: 'Ľ'}
                ]
            },
            {id: 'm', text: 'M', value: 'M'},
            {
                id: 'n', text: 'N', value: 'N', diacriticalLetters: [
                    {id: 'ň', text: 'Ň', value: 'Ň'},
                    {id: 'ń', text: 'Ń', value: 'Ń'},
                    {id: 'ñ', text: 'Ñ', value: 'Ñ'}
                ]
            },
            {
                id: 'o', text: 'O', value: 'O', diacriticalLetters: [
                    {id: 'ó', text: 'Ó', value: 'Ó'},
                    {id: 'ô', text: 'Ô', value: 'Ô'},
                    {id: 'ò', text: 'Ò', value: 'Ò'},
                    {id: 'ö', text: 'Ö', value: 'Ö'},
                    {id: 'õ', text: 'Õ', value: 'Õ'},
                    {id: 'ø', text: 'Ø', value: 'Ø'},
                    {id: 'ō', text: 'Ō', value: 'Ō'}
                ]
            },
            {id: 'p', text: 'P', value: 'P'},
            {id: 'q', text: 'Q', value: 'Q'},
            {
                id: 'r', text: 'R', value: 'R', diacriticalLetters: [
                    {id: 'ř', text: 'Ř', value: 'Ř'}
                ]
            },
            {
                id: 's', text: 'S', value: 'S', diacriticalLetters: [
                    {id: 'š', text: 'Š', value: 'Š'},
                    {id: 'ś', text: 'Ś', value: 'Ś'}
                ]
            },
            {
                id: 't', text: 'T', value: 'T', diacriticalLetters: [
                    {id: 'ť', text: 'Ť', value: 'Ť'}
                ]
            },
            {
                id: 'u', text: 'U', value: 'U', diacriticalLetters: [
                    {id: 'ú', text: 'Ú', value: 'Ú'},
                    {id: 'ů', text: 'Ů', value: 'Ů'},
                    {id: 'ù', text: 'Ù', value: 'Ù'},
                    {id: 'û', text: 'Û', value: 'Û'},
                    {id: 'ü', text: 'Ü', value: 'Ü'},
                    {id: 'ū', text: 'Ū', value: 'Ū'}
                ]
            },
            {id: 'v', text: 'V', value: 'V'},
            {
                id: 'w', text: 'W', value: 'W', diacriticalLetters: [
                    {id: 'ŵ', text: 'Ŵ', value: 'Ŵ'}
                ]
            },
            {id: 'x', text: 'X', value: 'X'},
            {
                id: 'y', text: 'Y', value: 'Y', diacriticalLetters: [
                    {id: 'ý', text: 'Ý', value: 'Ý'},
                    {id: 'ÿ', text: 'Ÿ', value: 'Ÿ'},
                    {id: 'ŷ', text: 'Ŷ', value: 'Ŷ'}
                ]
            },
            {
                id: 'z', text: 'Z', value: 'Z', diacriticalLetters: [
                    {id: 'ž', text: 'Ž', value: 'Ž'},
                    {id: 'ź', text: 'Ź', value: 'Ź'},
                    {id: 'ż', text: 'Ż', value: 'Ż'}
                ]
            }
        ]
    }
];
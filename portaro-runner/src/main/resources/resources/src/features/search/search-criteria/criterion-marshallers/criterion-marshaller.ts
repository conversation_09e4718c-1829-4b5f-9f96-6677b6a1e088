import type {Conjunction, Criterion, Disjunction, ICriterionFactory, Restriction} from '../criteria.types';

export default class CriterionMarshaller {

    constructor(private criterionFactory: ICriterionFactory<Criterion>) {
    }

    public fromCriterionToRestriction(criterion: Criterion): Restriction {
        const matcher = criterion.toMatcher();
        return {field: criterion.field, ...matcher};
    }

    public fromCriterionsToRestrictions(criterions: Criterion[]): Restriction[] {
        return criterions
            .filter((crit) => !crit.isEmpty())
            .map(this.fromCriterionToRestriction.bind(this));
    }

    public fromCriterionsToConjunction(criterions: Criterion[]): Conjunction {
        return {
            and: this.fromCriterionsToRestrictions(criterions)
        };
    }

    public fromCriterionsToDisjunction(criterions: Criterion[]): Disjunction {
        return {
            or: this.fromCriterionsToRestrictions(criterions)
        };
    }

    public fromRestrictionToCriterion(restriction: Restriction): Criterion {
        if (restriction.and) {
            throw new Error('Nested conjunction is not supported');
        }

        if (restriction.or) {
            throw new Error('Nested disjunction is not supported');
        }

        if (restriction.not) {
            throw new Error('Not restriction is not supported');
        }

        if (restriction.eqWords) {
            return this.criterionFactory.createTextCriterion(restriction.field, restriction.eqWords, false);
        }

        if (restriction.startsWithWords) {
            return this.criterionFactory.createTextCriterion(restriction.field, restriction.startsWithWords, true);
        }

        if (restriction.eq) {
            return this.criterionFactory.createEqCriterion(restriction.field, restriction.eq);
        }

        if (restriction.between) {
            return this.criterionFactory.createBetweenCriterion(restriction.field, restriction.between);
        }

        if (restriction.in) {
            return this.criterionFactory.createInCriterion(restriction.field, restriction.in);
        }

        throw new Error(`Unknown restriction ${JSON.stringify(restriction)}`);
    }

    public fromRestrictionsToCriterions(restrictions: Restriction[]): Criterion[] {
        return restrictions.map(this.fromRestrictionToCriterion.bind(this));
    }

    public fromConjunctionToCriterions(conjunction: Conjunction = {and: []}): Criterion[] {
        return this.fromRestrictionsToCriterions(conjunction.and);
    }

    public fromDisjunctionToCriterions(disjunction: Disjunction = {or: []}): Criterion[] {
        return this.fromRestrictionsToCriterions(disjunction.or);
    }
}

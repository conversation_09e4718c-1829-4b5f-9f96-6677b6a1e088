import type {KpSettingsData, KpSettingsUrlParams, SettingSection} from './types';
import type {SettingsDataService} from './settings.data-service';
import type {UrlStateManagerService} from 'shared/state-manager/url-state-manager.service';
import type {ServerStateDataService} from '../util/service/server-state.data-service';
import type {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
import type {ModalDialogService} from 'shared/modal-dialogs/modal-dialog.service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import {findFirstOrGetDefault, byId} from 'shared/utils/array-utils';
import {transferify} from 'shared/utils/data-service-utils';
import {exists} from 'shared/utils/custom-utils';
import type {
    Department,
    ViewableSettingChain,
    ViewableSettingValue
} from 'typings/portaro.be.types';

export class KpSettingsService {
    public static serviceName = 'kpSettingsService';

    /*@ngInject*/
    constructor(private serverStateDataService: ServerStateDataService,
                private settingsDataService: SettingsDataService,
                private toastMessageService: ToastMessageService,
                private modalDialogService: ModalDialogService,
                private finishedResponseInteractionService: FinishedResponseInteractionService,
                private urlStateManagerService: UrlStateManagerService<KpSettingsUrlParams>,
                private currentDepartment: Department) {
    }

    public async loadSettingsData(section?: string): Promise<KpSettingsData> {
        const serverState = await this.serverStateDataService.getServerState();
        const settings = await this.settingsDataService.getAllSettingSections();
        const activeSetting = findFirstOrGetDefault(settings, byId(section ?? 'OPAC'), settings[0]);

        return {
            serverState,
            settingSections: settings,
            activeSettingSection: activeSetting
        }
    }

    public setUrlSectionParam(settingSection: SettingSection) {
        this.urlStateManagerService.requestChangeState({section: settingSection.id});
    }

    public setUrlSearchParam(query: string) {
        this.urlStateManagerService.requestChangeState({q: query ? query : null});
    }

    public getUrlState$() {
        return this.urlStateManagerService.getState$();
    }

    public async setSettings(settingTypeIds: string[] = [], message = ''): Promise<void> {
        try {
            const settings = await this.settingsDataService.getSettingsByIds(settingTypeIds);
            await this.modalDialogService.openModalWindow('settings', {settings, message});
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async getSettings(sectionId: string): Promise<ViewableSettingChain[]> {
        try {
            return await this.settingsDataService.getSettings(sectionId);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async saveChainItemSettings(setting: ViewableSettingChain, chainItem: ViewableSettingValue, value: any): Promise<ViewableSettingChain> {
        try {
            const transferableValue = transferify(value, true);
            const transferableSettings = {
                section: setting.section,
                name: setting.name,
                departmentId: chainItem.department && chainItem.department.id,
                fondId: chainItem.fond && chainItem.fond.id,
                value: Array.isArray(transferableValue) ? transferableValue.join(',') : transferableValue,
                note: chainItem.note
            };

            const result = await this.settingsDataService.saveSettings(transferableSettings);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(result);
            return result.savedObject;
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async deleteChainItemSettings(chainItem: ViewableSettingValue): Promise<ViewableSettingChain> {
        try {
            const result = await this.settingsDataService.deleteSettings(chainItem.id);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(result);
            return result.savedObject;
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async searchForSettings(query: string): Promise<ViewableSettingChain[]> {
        try {
            const searchedSettings = await this.settingsDataService.querySettings(query);

            searchedSettings.forEach((setting) => {
                setting.chain.forEach((chainItem) => {
                    if (exists(chainItem.errorText)) {
                        this.toastMessageService.showWarning(chainItem.errorText, setting.id);
                    }
                });
            });

            return searchedSettings;
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public overrideChainItem(setting: ViewableSettingChain, chainItem: ViewableSettingValue): ViewableSettingValue {
        const lastOfChain = setting.chain[setting.chain.length - 1];

        if ('temporary' in lastOfChain && lastOfChain.temporary) {
            return lastOfChain;
        }

        return {
            id: null,
            editable: true,
            overridable: false,
            department: this.currentDepartment,
            fond: null,
            note: null,
            value: chainItem.value,
            default: false,
            effective: false,
            newlyCreated: true
        };
    }
}
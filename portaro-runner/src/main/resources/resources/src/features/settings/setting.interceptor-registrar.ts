import type ModalConversationInterceptorService from '../../modals/modal-conversation-interceptor.service';

export class SettingInterceptorRegistrar {

    constructor(private modalConversationInterceptor: ModalConversationInterceptorService) {
    }

    public register() {
        this.modalConversationInterceptor.registerSettingExceptionHandler('IniNotSetException');
        this.modalConversationInterceptor.registerSettingExceptionHandler('IniFeatureNotEnabledException');
    }
}
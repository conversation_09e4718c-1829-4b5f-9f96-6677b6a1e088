<script lang="ts">
    import type {OverallStatsData} from '../../types';
    import {getLocalization} from 'core/svelte-context/context';
    import KpClassicTable from 'shared/ui-widgets/table/classic/KpClassicTable.svelte';
    import KpTitledSection from 'shared/layouts/containers/KpTitledSection.svelte';

    export let statsData: OverallStatsData;

    const stats = statsData.stats.result.stats;
    const localize = getLocalization();
</script>

<KpTitledSection additionalClasses="overall-stats-additional-info"
                 title="{localize(/* @kp-localization stats.overall.MoreInfo */ 'stats.overall.MoreInfo')}"
                 headingType="h3">

    <KpClassicTable additionalClasses="additional-info-table"
                    colorAccented
                    horizontallyDivided
                    columnHeadersCentered>

        <svelte:fragment slot="body">
            <tr>
                <td rowspan="4">{localize(/* @kp-localization stats.overall.MVS */ 'stats.overall.MVS')}</td>
                <td rowspan="2">{localize(/* @kp-localization stats.overall.MVS_ReceivedRequests */ 'stats.overall.MVS_ReceivedRequests')}</td>
                <td colspan="1">{localize(/* @kp-localization stats.overall.MVS_AKT_CELK */ 'stats.overall.MVS_AKT_CELK')}</td>
                <td>{stats.MVS_AKT_CELK}</td>
            </tr>
            <tr>
                <td colspan="1">{localize(/* @kp-localization stats.overall.MVS_AKT_OK */ 'stats.overall.MVS_AKT_OK')}</td>
                <td>{stats.MVS_AKT_OK}</td>
            </tr>
            <tr>
                <td rowspan="2">{localize(/* @kp-localization stats.overall.MVS_SentRequests */ 'stats.overall.MVS_SentRequests')}</td>
                <td colspan="1">{localize(/* @kp-localization stats.overall.MVS_AKT_CELK */ 'stats.overall.MVS_AKT_CELK')}</td>
                <td>{stats.MVS_AKT_CELK}</td>
            </tr>
            <tr>
                <td colspan="1">{localize(/* @kp-localization stats.overall.MVS_AKT_OK */ 'stats.overall.MVS_AKT_OK')}</td>
                <td>{stats.MVS_AKT_OK}</td>
            </tr>

            <tr>
                <td rowspan="4">{localize(/* @kp-localization stats.overall.MMVS */ 'stats.overall.MMVS')}</td>
                <td rowspan="2">{localize(/* @kp-localization stats.overall.MMVS_Requests */ 'stats.overall.MMVS_Requests')}</td>
                <td colspan="1">{localize(/* @kp-localization stats.overall.MMVS_AKT_CELK */ 'stats.overall.MMVS_AKT_CELK')}</td>
                <td>{stats.MMVS_AKT_CELK}</td>
            </tr>
            <tr>
                <td colspan="1">{localize(/* @kp-localization stats.overall.MMVS_AKT_OK */ 'stats.overall.MMVS_AKT_OK')}</td>
                <td>{stats.MMVS_AKT_OK}</td>
            </tr>
            <tr>
                <td rowspan="2">{localize(/* @kp-localization stats.overall.MMVS_Requirements */ 'stats.overall.MMVS_Requirements')}</td>
                <td colspan="1">{localize(/* @kp-localization stats.overall.MMVS_AKT_CELK */ 'stats.overall.MMVS_AKT_CELK')}</td>
                <td>{stats.MMVS_AKT_CELK}</td>
            </tr>
            <tr>
                <td colspan="1">{localize(/* @kp-localization stats.overall.MMVS_AKT_OK */ 'stats.overall.MMVS_AKT_OK')}</td>
                <td>{stats.MMVS_AKT_OK}</td>
            </tr>

            <tr>
                <td rowspan="4">{localize(/* @kp-localization stats.overall.VF */ 'stats.overall.VF')}</td>
                <td rowspan="2">{localize(/* @kp-localization stats.overall.VF_Loaned */ 'stats.overall.VF_Loaned')}</td>
                <td colspan="1">{localize(/* @kp-localization stats.overall.VF_AKT_SOUB */ 'stats.overall.VF_AKT_SOUB')}</td>
                <td>{stats.VF_AKT_SOUB}</td>
            </tr>
            <tr>
                <td colspan="1">{localize(/* @kp-localization stats.overall.VF_AKT_POC */ 'stats.overall.VF_AKT_POC')}</td>
                <td>{stats.VF_AKT_POC}</td>
            </tr>
            <tr>
                <td rowspan="2">{localize(/* @kp-localization stats.overall.VF_Borrowed */ 'stats.overall.VF_Borrowed')}</td>
                <td colspan="1">{localize(/* @kp-localization stats.overall.VF_PAS_SOUB */ 'stats.overall.VF_PAS_SOUB')}</td>
                <td>{stats.VF_PAS_SOUB}</td>
            </tr>
            <tr>
                <td colspan="1">{localize(/* @kp-localization stats.overall.VF_PAS_POC */ 'stats.overall.VF_PAS_POC')}</td>
                <td>{stats.VF_PAS_POC}</td>
            </tr>

            <tr>
                <td colspan="3">{localize(/* @kp-localization stats.overall.POR_KONZ_RF */ 'stats.overall.POR_KONZ_RF')}</td>
                <td>{stats.POR_KONZ_RF}</td>
            </tr>
            <tr>
                <td colspan="3">{localize(/* @kp-localization stats.overall.MET_NAV */ 'stats.overall.MET_NAV')}</td>
                <td>{stats.MET_NAV}</td>
            </tr>

            <tr>
                <td rowspan="2">{localize(/* @kp-localization stats.overall.Education */ 'stats.overall.Education')}</td>
                <td colspan="2">{localize(/* @kp-localization stats.overall.MET_AKCE_CELK */ 'stats.overall.MET_AKCE_CELK')}</td>
                <td>{stats.MET_AKCE_CELK}</td>
            </tr>
            <tr>
                <td colspan="2">{localize(/* @kp-localization stats.overall.MET_AKCE_NAV */ 'stats.overall.MET_AKCE_NAV')}</td>
                <td>{stats.MET_AKCE_NAV}</td>
            </tr>
        </svelte:fragment>
    </KpClassicTable>
</KpTitledSection>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    :global {
        .overall-stats-additional-info .table-container {
            border: 1px solid @themed-border-default;
            overflow: hidden;
            border-radius: @border-radius-default;

            .additional-info-table {
                tr > td {
                    vertical-align: middle;
                }

                tr > td:last-child {
                    text-align: center;
                }

                tr:first-child td {
                    border-top: none;
                }
            }
        }
    }
</style>
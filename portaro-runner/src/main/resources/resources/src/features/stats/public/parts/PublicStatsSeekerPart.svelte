<script lang="ts">
    import type {PublicStatsData, PublicStatsDataParams} from '../../types';
    import type {EditedProperty} from 'typings/portaro.be.types';
    import {createEventDispatcher} from 'svelte';
    import {filterPublicStatsParams, findEditorBindingsInFields, findEditorLabelInFields} from '../../utils';
    import {getLocalization} from 'core/svelte-context/context';
    import {isEqual} from 'lodash-es';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let statsData: PublicStatsData;
    export let editorFields: EditedProperty[];
    export let params: PublicStatsDataParams;
    $: changedParams = !isEqual(params, filterPublicStatsParams(statsData.stats.params));

    const dispatch = createEventDispatcher<{ 'search': void }>();
    const localize = getLocalization();

    const handleSubmit = () => {
        dispatch('search');
    }
</script>

<form class="public-stats-seeker" on:submit|preventDefault={handleSubmit}>
    <div class="dates-selector-container">
        <label for="date-from">{findEditorLabelInFields(editorFields, 'fromDate')}:</label>
        <KpValueEditor editorId="date-from"
                       {...findEditorBindingsInFields(editorFields, 'fromDate')}
                       bind:model={params.fromDate}/>

        <UIcon color="#969FA6" icon="arrow-right"/>

        <label for="date-to">{findEditorLabelInFields(editorFields, 'toDate')}:</label>
        <KpValueEditor editorId="date-to"
                       {...findEditorBindingsInFields(editorFields, 'toDate')}
                       bind:model={params.toDate}/>
    </div>

    <div class="grouping-container">
        <label for="time-granularity">{findEditorLabelInFields(editorFields, 'timeGranularity')}:</label>
        <KpValueEditor editorId="time-granularity"
                       {...findEditorBindingsInFields(editorFields, 'timeGranularity')}
                       bind:model={params.timeGranularity}/>

        <div class="divider"></div>

        <KpButton additionalClasses="search-btn"
                  buttonStyle="brand-orange-new"
                  buttonType="submit"
                  isDisabled="{!changedParams}">

            <IconedContent icon="search">
                {localize(/* @kp-localization commons.Find */ 'commons.Find')}
            </IconedContent>
        </KpButton>
    </div>
</form>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.media-queries.less";

    .public-stats-seeker {
        width: 100%;
        background-color: @themed-panel-bg;
        border: 1px solid @themed-border-default;
        border-radius: @border-radius-default;
        padding: @spacing-s @spacing-m @spacing-s @spacing-ml;
        display: flex;
        align-items: center;
        gap: @spacing-m;
        margin: 0;
        box-shadow: none;
        justify-content: space-between;

        @media (max-width: @screen-md-max) {
            flex-direction: column;
        }

        .dates-selector-container,
        .grouping-container {
            display: flex;
            align-items: center;
            gap: @spacing-m;

            label {
                margin: 0;
            }
        }

        .grouping-container {
            .divider {
                height: 24px;
                width: 1px;
                flex-shrink: 0;
                border-radius: 1px;
                background-color: @themed-border-default;
            }
        }
    }

    :global {
        .public-stats-seeker {
            @media (max-width: @screen-md-max) {
                .dates-selector-container {
                    width: 100%;

                    kp-value-editor {
                        flex: 1;
                    }
                }

                .grouping-container {
                    width: 100%;

                    .divider {
                        // Must be important
                        background-color: transparent !important;
                        margin: auto !important;
                    }
                }
            }

            @media (max-width: @screen-sm-max) {
                .grouping-container {
                    flex-wrap: wrap;

                    kp-value-editor {
                        flex: 1;
                    }

                    .search-btn {
                        width: 100%;
                    }

                    .divider {
                        display: none;
                    }
                }
            }
        }
    }
</style>
import type {
    OverallStatsData,
    OverallStatsDataParams,
    PublicStatsData,
    PublicStatsDataParams,
    ZStatsData
} from './types';
import type {Auth, EditedProperty} from 'typings/portaro.be.types';
import type {StatsDataService} from './stats.data-service';
import type {UrlStateManagerService} from 'shared/state-manager/url-state-manager.service';
import type {Observable} from 'rxjs';
import {transferify} from 'shared/utils/data-service-utils';

export class StatsService {
    public static serviceName = 'kpStatsService';

    /*@ngInject*/
    constructor(private urlStateManagerService: UrlStateManagerService<PublicStatsDataParams | OverallStatsDataParams>,
                private statsDataService: StatsDataService,
                private currentAuth: Auth) {
    }

    public async loadPublicStatsData(params?: PublicStatsDataParams): Promise<PublicStatsData | null> {
        try {
            return await this.statsDataService.getPublicStats(params);
        } catch {
            return null;
        }
    }

    public async loadPublicStatsFormSettings(): Promise<EditedProperty[] | null> {
        try {
            return await this.statsDataService.getPublicStatsFormSettings();
        } catch {
            return null;
        }
    }

    public async loadOverallStatsData(params?: OverallStatsDataParams): Promise<OverallStatsData> {
        try {
            return await this.statsDataService.getOverallStats(params);
        } catch {
            return null;
        }
    }

    public async loadOverallStatsFormSettings(): Promise<EditedProperty[] | null> {
        try {
            return await this.statsDataService.getOverallStatsFormSettings();
        } catch {
            return null;
        }
    }

    public async loadZStatsData(): Promise<ZStatsData> {
        try {
            return await this.statsDataService.getZStatsData();
        } catch {
            return null;
        }
    }

    public getPublicStatsParamsState(): Observable<PublicStatsDataParams> {
        return this.urlStateManagerService.getState$();
    }

    public getOverallStatsParamsState(): Observable<OverallStatsDataParams> {
        return this.urlStateManagerService.getState$();
    }

    public requestUrlParamsChange(params: PublicStatsDataParams | OverallStatsDataParams) {
        this.urlStateManagerService.requestChangeState(transferify(params));
    }

    public isCurrentUserLibrarian(): boolean {
        return this.currentAuth.evided && this.currentAuth.role.includes('ROLE_LIBRARIAN');
    }
}
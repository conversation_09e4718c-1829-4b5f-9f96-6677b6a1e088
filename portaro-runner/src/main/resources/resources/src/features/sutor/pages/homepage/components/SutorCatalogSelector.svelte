<script lang="ts">
    import type {CatalogInfo} from '../types';
    import {Menu, MenuButton, MenuItems} from '@rgossiaux/svelte-headlessui';
    import {createFloatingActions} from 'svelte-floating-ui';
    import {offset} from 'svelte-floating-ui/dom';
    import KpDropdownMenuItem from 'shared/ui-widgets/dropdown/KpDropdownMenuItem.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let catalogUrls: CatalogInfo[];
    $: currentCatalog = catalogUrls.find((catalog) => catalog.current);

    const [floatingRef, floatingContent] = createFloatingActions({
        placement: 'bottom-end',
        strategy: 'fixed',
        middleware: [
            offset(2)
        ]
    });
</script>

<Menu as="span" class="sutor-catalog-selector">
    <MenuButton use={[floatingRef]}
                class="sutor-catalog-selector-button">

        <IconedContent trailingIcon="angle-small-down" gap="18px">
            <div class="inner-container">
                <span>{currentCatalog.name}</span>
                <small>{currentCatalog.url}</small>
            </div>
        </IconedContent>
    </MenuButton>

    <MenuItems as="ul"
               use={[floatingContent]}
               class="dropdown-menu dropdown-menu-shown">

        {#each catalogUrls as catalogInfo}
            <KpDropdownMenuItem href="{catalogInfo.url}" additionalClasses="division-item">
                <span>{catalogInfo.name}</span>
                <small>{catalogInfo.url}</small>
            </KpDropdownMenuItem>
        {/each}
    </MenuItems>
</Menu>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    :global {
        .sutor-catalog-selector {
            .sutor-catalog-selector-button {
                background-color: @themed-body-bg;
                border-color: var(--accent-blue-new);
                border-radius: @border-radius-default;
                border: 1px solid @themed-border-default;
                box-shadow: rgba(0, 0, 0, 0.05) 2px 2px 12px 0;
                padding: @spacing-s @spacing-sm;
                color: @themed-text-default;
                gap: @spacing-s;
                transition: background-color 0.2s ease-in-out;

                .inner-container {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: start;
                    text-align: start;

                    small {
                        opacity: 0.75;
                    }
                }
            }

            .dropdown-menu .division-item {
                display: flex;
                flex-direction: column;
                align-items: start;
                justify-content: start;
                padding: @spacing-s @spacing-m;

                small {
                    opacity: 0.75;
                }
            }

            .dropdown-menu-shown {
                display: block;
            }
        }
    }
</style>
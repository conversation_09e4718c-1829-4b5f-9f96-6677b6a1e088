<script lang="ts">
    import type {RecordSearchParams} from 'typings/portaro.be.types';
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import type {SearchManager} from 'src/features/search/search-manager/search-manager';
    import KpPageableSearchResults from 'src/features/search/kp-search-results/KpPageableSearchResults.svelte';
    import KpSearchToolbar from 'src/features/search/kp-search-toolbar/KpSearchToolbar.svelte';
    import KpBarebonesTable from 'shared/ui-widgets/table/barebones/KpBarebonesTable.svelte';
    import ProjectItemsTable from 'src/features/sutor/pages/projects/tab-pages/ProjectItemsTable.svelte';
    import KpStaticSearchContext from 'src/features/search/kp-search-context/KpStaticSearchContext.svelte';

    export let selectedRow: RecordRow | null;
    export let columns: number;
    export let projectType: 'reported' | 'contracted';
    export let searchManager: SearchManager<RecordRow, RecordSearchParams>;

    let tableContainerWidth: number;

    function isRecordRow(item: any): item is RecordRow {
        return 'id' in item && 'fields' in item;
    }

    function asRecordRow(item: any): RecordRow {
        if (!isRecordRow(item)) {
            throw new TypeError(`${JSON.stringify(item)} is not a RecordRow`);
        }

        return item;
    }
</script>

<KpStaticSearchContext {searchManager}>
    <div class="sutor-projects-table-container" bind:clientWidth={tableContainerWidth}>
        <KpSearchToolbar/>

        <KpPageableSearchResults pagination="{searchManager.getPagination()}" let:paginationData>
            <KpBarebonesTable additionalClasses="sutor-projects-table"
                              cellHeight="44px"
                              fontSize="12px"
                              rowsTopBordered>

                <tr slot="header">
                    <slot name="header-columns"/>
                </tr>

                <svelte:fragment slot="body">
                    {#each paginationData.items as itemRow(itemRow.id)}
                        {@const record = asRecordRow(itemRow)}

                        <slot name="project-item" {record} rowSelected="{selectedRow?.id === record.id}"/>

                        <ProjectItemsTable {projectType}
                                           {columns}
                                           {record}
                                           width="{tableContainerWidth}"
                                           shown="{selectedRow?.id === record.id}"/>
                    {/each}
                </svelte:fragment>
            </KpBarebonesTable>
        </KpPageableSearchResults>
    </div>
</KpStaticSearchContext>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .sutor-projects-table-container {
        display: flex;
        flex-direction: column;
        width: 100%;
        gap: @spacing-xl;
    }
</style>
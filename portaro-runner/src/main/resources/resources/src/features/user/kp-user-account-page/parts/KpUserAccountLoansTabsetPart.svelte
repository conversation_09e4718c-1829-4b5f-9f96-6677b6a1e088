<script lang="ts">
    import type {UserAccountReactivePageData, UserAccountStaticPageData} from '../types';
    import {exists} from 'shared/utils/custom-utils';
    import {getInjector} from 'core/svelte-context/context';
    import {KpUserAccountPageService} from '../kp-user-account-page.service';
    import {getPageContext} from 'shared/layouts/page-context';
    import {onDestroy} from 'svelte';
    import {UserRoles} from 'shared/services/current-auth.service';
    import KpUserDetailLoansTabset from '../../kp-user-detail-loans/KpUserDetailLoansTabset.svelte';

    const service = getInjector().getByToken<KpUserAccountPageService>(KpUserAccountPageService.serviceName);
    const pageContext = getPageContext<UserAccountStaticPageData, UserAccountReactivePageData>();

    const user = pageContext.staticData.user;
    const detailView = pageContext.staticData.detailView;

    let reactiveData: UserAccountReactivePageData;
    const reactiveDataUnsubscribe = pageContext.reactiveData.subscribe((data) => reactiveData = data);
    onDestroy(reactiveDataUnsubscribe);

    function hasEverythingDefined(data: UserAccountReactivePageData): boolean {
        return exists(data.loanList)
            && exists(data.waitingLoanList)
            && exists(data.endedLoanList)
            && exists(data.seekingsOfRequester)
            && exists(data.seekingsOfProvider)
            && exists(data.providedSeekingsOfSeeker)
    }
</script>

{#if exists(reactiveData) && hasEverythingDefined(reactiveData) && detailView.loansShowing && (user.role.includes(UserRoles.ROLE_READER) || user.role.includes(UserRoles.ROLE_LIBRARY))}
    <KpUserDetailLoansTabset {user}
                             activeLoanList="{reactiveData.loanList}"
                             waitingLoanList="{reactiveData.waitingLoanList}"
                             returnedLoanList="{reactiveData.endedLoanList}"
                             seekingsOfRequesterList="{reactiveData.seekingsOfRequester}"
                             seekingsOfProviderList="{reactiveData.seekingsOfProvider}"
                             seekingsOfSeekerList="{reactiveData.providedSeekingsOfSeeker}"
                             waitingCanLoanCancelAny="{detailView.canLoanCancelAny}"
                             activeCanLoanReturnAny="{detailView.canLoanReturnAny}"
                             waitingCancelLoan="{(loan) => service.cancelLoan(loan, reactiveData.waitingLoanList)}"
                             activeReturnLoan="{(loan) => service.returnLoan(user, loan, reactiveData.loanList, reactiveData.endedLoanList)}"
                             activeRenewLoan="{(loan) => service.renewLoan(loan, reactiveData.loanList)}"/>
{/if}
import type {Loan, ProvidedSeeking, Document, SeekedSeeking, Seeking, User} from 'typings/portaro.be.types';
import type {DelayedReturnPenalty, UserDetailView} from '../types';
import type {DefaultReactivePageableList} from 'shared/utils/default-reactive-pageable-list';

export const USER_ACCOUNT_RELOAD_RETURN_PENALTY_EVENT = 'kp-user-account-page-reload-return-penalty-event';

export type LoanList = DefaultReactivePageableList<Loan, string>;
export type SeekingList = DefaultReactivePageableList<Seeking, string>;
export type ProvidedSeekingList = DefaultReactivePageableList<ProvidedSeeking, string>;
export type SeekedSeekingList = DefaultReactivePageableList<SeekedSeeking, string>;

export interface UserAccountPageLoadedData {
    loanList: LoanList;
    waitingLoanList: LoanList;
    endedLoanList: LoanList;
    seekingsOfRequester: SeekingList;
    providedSeekingsOfSeeker: ProvidedSeekingList;
    seekingsOfProvider: SeekedSeekingList;
    delayedReturnPenalty: DelayedReturnPenalty;
    detailView: UserDetailView;
    user: User;
    userRecord: Document | null;
}

export interface UserAccountStaticPageData {
    user: User;
    detailView: UserDetailView;
    userRecord: Document | null;
}

export interface UserAccountReactivePageData {
    loanList?: LoanList;
    waitingLoanList?: LoanList;
    endedLoanList?: LoanList;
    seekingsOfRequester?: SeekingList;
    providedSeekingsOfSeeker?: ProvidedSeekingList;
    seekingsOfProvider?: SeekedSeekingList;
    delayedReturnPenalty?: DelayedReturnPenalty;
}
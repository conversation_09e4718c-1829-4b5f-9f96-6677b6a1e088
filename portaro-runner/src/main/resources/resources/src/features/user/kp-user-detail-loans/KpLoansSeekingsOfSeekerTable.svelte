<script lang="ts">
    import KpLoansGenericTable from './KpLoansGenericTable.svelte';
    import type {ProvidedSeeking} from 'typings/portaro.be.types';
    import {getDateFormatter, getLocalization} from 'core/svelte-context/context';
    import {exists} from 'shared/utils/custom-utils';
    import DesiredExemplar from '../DesiredExemplar.svelte';
    import {pipe} from 'core/utils';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpPrice from 'shared/components/kp-price/KpPrice.svelte';
    import {fade} from 'svelte/transition';
    import Label from 'shared/components/kp-label/Label.svelte';
    import type {PageableList, PageableListState} from 'shared/utils/reactive-pageable-list';
    import type {EmptyObject} from 'typings/portaro.fe.types';
    import type {Observable} from 'rxjs';

    export let loanList: PageableList<ProvidedSeeking, string, EmptyObject>;

    const localize = getLocalization();
    const dateFormatter = getDateFormatter();

    let loanListData: Observable<PageableListState<ProvidedSeeking, string, EmptyObject>>;
    $: loanListData = loanList.getState$();
</script>

<KpLoansGenericTable {loanList}>
    <tr slot="header">
        <th class="header-desired-document">{localize(/* @kp-localization commons.nazevDokumentu */ 'commons.nazevDokumentu')}</th>
        <th class="header-state">{localize(/* @kp-localization commons.stav */ 'commons.stav')}</th>
        <th class="header-request-create-date">{localize(/* @kp-localization ill.RequestCreateDate */ 'ill.RequestCreateDate')}</th>
        <th class="header-process-start-date">{localize(/* @kp-localization ill.CommenceDate */ 'ill.CommenceDate')}</th>
        <th class="header-reference-number">{localize(/* @kp-localization ill.ReferenceNumber */ 'ill.ReferenceNumber')}</th>
        <th class="header-reservation-date">{localize(/* @kp-localization ill.provision.ExemplarReservationDate */ 'ill.provision.ExemplarReservationDate')}</th>
        <th class="header-realization-date">{localize(/* @kp-localization ill.provision.ProviderSendDate.abbr */ 'ill.provision.ProviderSendDate.abbr')}</th>
        <th class="header-send-back-date">{localize(/* @kp-localization ill.provision.ExemplarReceiveDate */ 'ill.provision.ExemplarReceiveDate')}</th>
        <th class="header-price">{localize(/* @kp-localization commons.cena */ 'commons.cena')}</th>
    </tr>

    <svelte:fragment slot="body">
        {#each $loanListData.loadedItems ?? [] as seeking(seeking.id)}
            {@const hasActiveProvision = exists(seeking.activeProvision)}

            <tr class="loan-item" in:fade>
                <td class="data-desired-document">
                    <DesiredExemplar desiredExemplar="{seeking.desiredExemplar}"/>
                </td>

                <td class="data-state centered-loan-data">
                    <IconedContent icon="info" orientation="vertical">
                        {#if seeking.cancelled}
                            <span>{localize(/* @kp-localization commons.state.CancelledInState */ 'commons.state.CancelledInState')}</span>
                        {/if}
                        <Label labeled={seeking.state}/>
                    </IconedContent>
                </td>

                <td class="data-request-create-date centered-loan-data">{pipe(seeking.createDate, dateFormatter('d.M.yyyy'))}</td>

                <td class="data-process-start-date centered-loan-data">
                    {#if seeking.commenceDate}
                        {pipe(seeking.commenceDate, dateFormatter('d.M.yyyy'))}
                    {/if}
                </td>

                <td class="data-reference-number centered-loan-data">
                    {#if hasActiveProvision && seeking.seekerReferenceId}
                        {seeking.seekerReferenceId}
                    {/if}
                </td>

                <td class="data-reservation-date centered-loan-data">
                    {#if hasActiveProvision && seeking.activeProvision.seekerActivateDate}
                        {pipe(seeking.activeProvision.seekerActivateDate, dateFormatter('d.M.yyyy'))}
                    {/if}
                </td>

                <td class="data-realization-date centered-loan-data">
                    {#if hasActiveProvision && seeking.activeProvision.providerSendDate}
                        {pipe(seeking.activeProvision.providerSendDate, dateFormatter('d.M.yyyy'))}
                    {/if}
                </td>

                <td class="data-send-back-date centered-loan-data">
                    {#if hasActiveProvision && seeking.activeProvision.providerReceiveDate}
                        {pipe(seeking.activeProvision.providerReceiveDate, dateFormatter('d.M.yyyy'))}
                    {/if}
                </td>

                <td class="data-price centered-loan-data">
                    {#if hasActiveProvision}
                        <KpPrice price="{seeking.activeProvision.price}"/>
                    {/if}
                </td>
            </tr>
        {/each}
    </svelte:fragment>
</KpLoansGenericTable>
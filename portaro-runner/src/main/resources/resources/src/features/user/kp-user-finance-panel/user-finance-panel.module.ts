import {KpUserFinancePanelService} from './kp-user-finance-panel.service';
import register from '@kpsys/angularjs-register';

/**
 * @ngdoc module
 * @name portaro.features.user.user-finance-panel
 * @module portaro.features.user.user-finance-panel
 */
export default register('portaro.features.user.user-finance-panel')
    .service(KpUserFinancePanelService.serviceName, KpUserFinancePanelService)
    .name();
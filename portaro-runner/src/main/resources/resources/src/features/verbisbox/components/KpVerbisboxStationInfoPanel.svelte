<script lang="ts">
    import type {StationHealth} from '../types';
    import {StationHealthStatus} from '../types';
    import {assertUnreachable, exists, isNullOrUndefined} from 'shared/utils/custom-utils';
    import {getLocalization} from 'core/svelte-context/context';
    import {getVerbisboxContext} from '../utils/verbisbox-context';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let stationHealth: StationHealth;

    const localize = getLocalization();
    const {stationName} = getVerbisboxContext();

    let showDetails: boolean;
    $: showDetails = hasDetails(stationHealth.details);

    function hasDetails(details: Record<string, string>): boolean {
        if (isNullOrUndefined(details)) {
            return false;
        }
        const detailsEntries = Object.entries(stationHealth.details);
        return detailsEntries.length > 0 && detailsEntries.some(([, entryValue]) => exists(entryValue));
    }

    function isInputVoltage(detailKey: string): boolean {
        return detailKey === 'inputVoltage';
    }

    function isInternetSignalStrength(detailKey: string): boolean {
        return detailKey === 'internetSignalStrength';
    }

    function localizeHealthStatus(healthStatus: StationHealthStatus): string {
        switch (healthStatus) {
            case StationHealthStatus.UP:
                return localize(/* @kp-localization verbisbox.health.Up */ 'verbisbox.health.Up');
            case StationHealthStatus.DOWN:
                return localize(/* @kp-localization verbisbox.health.Down */ 'verbisbox.health.Down');
            case StationHealthStatus.OFFLINE:
                return localize(/* @kp-localization verbisbox.health.Offline */ 'verbisbox.health.Offline');
            default:
                assertUnreachable(healthStatus);
        }
    }
</script>

<div class="verbisbox-station-info-panel">
    <h1 class="unset-style station-name">
        {stationName}
    </h1>

    <div class="details-panel">
        <div class="operation-status">
            <span class="badge"
                  class:ok={stationHealth.status === StationHealthStatus.UP}
                  class:not-ok={stationHealth.status !== StationHealthStatus.UP}
                  role="presentation">
                {localizeHealthStatus(stationHealth.status)}
            </span>
        </div>

        {#if showDetails}
            <div class="health-details">
                {#each Object.entries(stationHealth.details) as [detailKey, detailValue]}
                    {#if exists(detailValue)}
                        <span>
                            <UIcon icon="{isInputVoltage(detailKey) ? 'bolt' : 'signal-stream'}"/>

                            <span class="detail-value"
                                  class:voltage={isInputVoltage(detailKey)}
                                  class:percentage={isInternetSignalStrength(detailKey)}>
                                {detailValue}
                            </span>
                        </span>
                    {/if}
                {/each}
            </div>
        {/if}
    </div>
</div>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    @dark: #5f5e6e;
    @light: #f5f5f5;

    .verbisbox-station-info-panel {
        width: 100%;

        @media (max-width: @screen-md-min) {
            max-width: none;
        }

        @media (min-width: @screen-md-min) {
            max-width: calc(var(--columns-count) * 25%);
        }

        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        flex-wrap: wrap;

        .station-name {
            background-color: @dark;
            color: @light;

            padding-top: 8px;
            padding-left: 1em;
            padding-right: 1em;
        }

        .details-panel {
            background-color: @dark;
            color: @light;

            display: flex;
            gap: 1.5em;

            padding-top: 8px;
            padding-left: 1em;
            padding-right: 1em;
        }

        .operation-status {
            display: flex;
            align-items: center;
            gap: 8px;

            .ok {
                background-color: var(--success-green);
            }

            .not-ok {
                background-color: var(--danger-red);
            }
        }

        .health-details {
            display: flex;
            gap: 1.5em;

            .detail-value {
                &.voltage::after {
                    content: 'V';
                }

                &.percentage::after {
                    content: '%';
                }
            }
        }
    }
</style>
import type {Observable} from 'rxjs';
import {BehaviorSubject} from 'rxjs';
import {assertExists, exists} from 'shared/utils/custom-utils';
import {DateTime} from 'luxon';
import {
    calendarDateFromLuxon,
    getCalendarMonth,
    getCalendarWeek,
    luxonDateFromCalendarDate,
    luxonStartOfWeek,
    luxonIsSameDay,
    getDaysBetweenDatesInclusive,
    getDaysFromRangeInclusive
} from 'shared/components/kp-calendar/utils';
import type {
    CalendarDataSettings,
    CalendarDate,
    CalendarDay,
    CalendarMonth,
    CalendarNavigateDirectionType,
    CalendarUnitByGranularity,
    CalendarView,
    CalendarViewSettings,
    CalendarWeek
} from 'shared/components/kp-calendar/types';

/**
 * A class that manages and generates calendar views, allowing navigation through weeks and months.
 * It supports view types such 'weekView' and 'monthView', and provides navigation
 * capabilities, including moving forwards and backwards and to current day.
 */
export class KpCalendarManager {

    private navigationOffset = 0;
    private view$: BehaviorSubject<CalendarView>;

    private constructor(private settings: CalendarViewSettings) {
        this.view$ = new BehaviorSubject<CalendarView>(this.generateView(0));
    }

    /**
     * Navigate the calendar by a specified amount.
     * This adjusts the current view by the given amount, either forward or backward.
     *
     * @param amount - The number of weeks or months to navigate by. Positive values move forward,
     * negative values move backward, and 0 keeps the current view.
     */
    public navigate(amount: number): void {
        this.navigationOffset += amount;
        this.view$.next(this.generateView(amount < 0 ? -1 : (amount > 0 ? 1 : 0)));
    }

    /**
     * Navigate the calendar to a specific offset.
     *
     * @param offset - The target offset to navigate to.
     */
    public navigateTo(offset: number): void {
        this.navigationOffset = offset;
        this.view$.next(this.generateView(0));
    }

    /**
     * Navigate to today's date and update the view accordingly.
     * For 'weekView', it moves to the current week, and for 'monthView', it moves to the current month.
     */
    public navigateToToday(): void {
        const today = DateTime.now();
        const startDate = exists(this.settings.startDate) ? luxonDateFromCalendarDate(this.settings.startDate) : today;

        let offset: number;

        if (this.settings.type === 'weekView') {
            const startOfStartDateWeek = luxonStartOfWeek(startDate);
            const startOfTodayWeek = luxonStartOfWeek(today);

            const weeksBetween = startOfTodayWeek.diff(startOfStartDateWeek, 'weeks').weeks;
            offset = Math.floor(weeksBetween);
        } else if (this.settings.type === 'monthView') {
            const monthsBetween = today.diff(startDate, 'months').months;
            offset = Math.floor(monthsBetween);
        }

        this.navigateTo(offset);
    }

    /**
     * Get an observable stream of the current calendar view.
     *
     * This provides a stream of `CalendarView` objects, allowing subscribers to react to changes in the calendar view.
     *
     * @returns An observable emitting the current `CalendarView` whenever it is updated.
     */
    public getView$(): Observable<CalendarView> {
        return this.view$.asObservable();
    }

    private generateView(navigateDirection: CalendarNavigateDirectionType): CalendarView {
        const now = DateTime.now();
        let startDate = DateTime.now();

        if (exists(this.settings.startDate)) {
            startDate = luxonDateFromCalendarDate(this.settings.startDate);
        }

        if (this.settings.type === 'weekView') {
            const currentWeekNumber = startDate.weekNumber + this.navigationOffset;
            const weekDatesRange = getCalendarWeek(currentWeekNumber, startDate.year);

            const days = getDaysFromRangeInclusive(weekDatesRange, (date) => {
                return {
                    current: luxonIsSameDay(now, date),
                    events: []
                }
            });

            return {
                days,
                startDate: calendarDateFromLuxon(startDate),
                endDate: weekDatesRange.end,
                navigationOffset: this.navigationOffset,
                week: currentWeekNumber,
                year: startDate.year,
                navigateDirection
            };
        }

        if (this.settings.type === 'monthView') {
            const startDateWithOffset = startDate.plus({months: this.navigationOffset});
            const monthDatesRange = getCalendarMonth(startDateWithOffset.month, startDateWithOffset.year);

            const days = getDaysFromRangeInclusive(monthDatesRange, (date) => {
                return {
                    current: luxonIsSameDay(now, date),
                    events: []
                }
            });

            return {
                days,
                startDate: calendarDateFromLuxon(startDate),
                endDate: monthDatesRange.end,
                navigationOffset: this.navigationOffset,
                month: startDateWithOffset.month,
                year: startDateWithOffset.year,
                navigateDirection
            };
        }

        return null;
    }

    /**
     * Static method to create calendar data for a specific granularity (day, week, or month).
     *
     * @param settings - The settings for generating the calendar data, including a granularity type.
     * @returns Generated calendar data based on the specified granularity.
     * @throws An error if the granularity is not supported.
     */
    public static createCalendarData<GRANULARITY extends keyof CalendarUnitByGranularity>(
        settings: CalendarDataSettings & { granularity: GRANULARITY }
    ): CalendarUnitByGranularity[GRANULARITY] {
        assertExists(settings);
        const {from, to} = settings;

        if (settings.granularity === 'day') {
            return KpCalendarManager.generateDays(from, to) as CalendarUnitByGranularity[GRANULARITY];
        }

        if (settings.granularity === 'week') {
            return KpCalendarManager.generateWeeks(from, to) as CalendarUnitByGranularity[GRANULARITY];
        }

        if (settings.granularity === 'month') {
            return KpCalendarManager.generateMonths(from, to) as CalendarUnitByGranularity[GRANULARITY];
        }

        throw new Error(`Unsupported granularity: ${settings.granularity}`);
    }

    /**
     * Static method to create a new instance of KpCalendarManager with the specified settings.
     *
     * @param settings - The calendar view settings, including the start date and view type.
     * @returns A new instance of KpCalendarManager.
     */
    public static createNavigableCalendar(settings: CalendarViewSettings): KpCalendarManager {
        return new KpCalendarManager(settings);
    }

    private static generateDays(from: CalendarDate, to: CalendarDate): CalendarDay[] {
        const startDate = luxonDateFromCalendarDate(from);
        const endDate = luxonDateFromCalendarDate(to);

        return getDaysBetweenDatesInclusive(startDate, endDate);
    }

    private static generateWeeks(from: CalendarDate, to: CalendarDate): CalendarWeek[] {
        const startDate = luxonDateFromCalendarDate(from);
        const endDate = luxonDateFromCalendarDate(to);
        const days = getDaysBetweenDatesInclusive(startDate, endDate);

        return days.reduce((reducedWeeks: CalendarWeek[], day) => {
            let currentWeek = reducedWeeks.find((week) => week.week === day.week);

            if (!currentWeek) {
                currentWeek = {
                    days: [],
                    year: day.year,
                    month: day.month,
                    week: day.week
                };
                reducedWeeks.push(currentWeek);
            }

            currentWeek.days.push(day);

            return reducedWeeks;
        }, []);
    }

    private static generateMonths(from: CalendarDate, to: CalendarDate): CalendarMonth[] {
        const startDate = luxonDateFromCalendarDate(from);
        const endDate = luxonDateFromCalendarDate(to);
        const days = getDaysBetweenDatesInclusive(startDate, endDate);
        const weeks = this.generateWeeks(from, to);

        return days.reduce((reducedMonths: CalendarMonth[], day) => {
            let currentMonth = reducedMonths.find((month) => month.month === day.month && month.year === day.year);

            if (!currentMonth) {
                currentMonth = {
                    year: day.year,
                    month: day.month,
                    weeks: weeks.filter((week) => week.month === day.month && week.year === day.year),
                    days: []
                };
                reducedMonths.push(currentMonth);
            }

            currentMonth.days.push(day);

            return reducedMonths;
        }, []);
    }
}
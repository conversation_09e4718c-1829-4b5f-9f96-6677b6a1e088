<script lang="ts">
    import type {Link} from 'typings/portaro.be.types';
    import {resolveTarget} from './utils';
    import {pipe} from 'core/utils';
    import {loc} from '../../utils/pipes';

    export let link: Link;
    export let forceNewWindow = false;

    let target: '_self' | '_blank';
    $: target = forceNewWindow ? '_blank' : resolveTarget(link);
</script>

<a href={link.url} {target} rel={target === '_blank' ? 'noreferrer' : null} {...$$restProps}>
    {#if $$slots.default}
        {pipe(link, loc())}
        <slot/>
    {:else}
        {pipe(link, loc())}
    {/if}
</a>
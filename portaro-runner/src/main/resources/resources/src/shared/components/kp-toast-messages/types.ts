import type {UUID} from 'typings/portaro.be.types';
import type {UIcons} from 'shared/ui-widgets/uicons/types';
import type {SvelteComponentConstructor} from 'core/types';

export type ToastType = 'info' | 'success' | 'error' | 'warning' | 'wait' | 'custom-component';

export interface ToastMessage {
    id: UUID;
    type: ToastType;
    title: string;
    message: string;
    timeout: number;
    customComponent?: SvelteComponentConstructor;
    customComponentProps?: Record<string, any>;
}

export interface ToastMessageTypeSettings {
    backgroundColor: string;
    icon?: UIcons;
    textColor?: string;
    borderColor?: string;
}
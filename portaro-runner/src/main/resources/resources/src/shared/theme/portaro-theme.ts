import type {Readable} from 'svelte/store';
import {getContext, setContext} from 'svelte';

export const themeLocalStorageKey = 'portaro-theme';
const themeContextKey = 'portaro-theme-ctx';

export type PortaroTheme = 'light' | 'dark';

export interface ThemeContext {
    currentTheme: Readable<PortaroTheme>;
    changeTheme: (value: PortaroTheme) => void;
}

export function createThemeContext(context: ThemeContext) {
    setContext<ThemeContext>(themeContextKey, context);
}

export function getThemeContext() {
    return getContext<ThemeContext>(themeContextKey);
}
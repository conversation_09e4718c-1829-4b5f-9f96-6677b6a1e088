<script>
    import {Meta, Template, Story} from '@storybook/addon-svelte-csf';
    import KpGenericPanel from './KpGenericPanel.svelte';
</script>

<Meta title="Widgets/KpGenericPanel"
      description="panel"
      component={KpGenericPanel}

      argTypes={{
            panelStyle: {
                control: { type: 'select' },
                options: ['default' , 'primary' , 'success' , 'info' , 'warning' , 'danger']
            },
            additionalClasses: { control: 'text' },
            headingSlot: { control: 'text' },
            defaultSlot: { control: 'text' },
            footerSlot: { control: 'text' }
  }}
/>

<Template let:args>
    <KpGenericPanel {...args}>
        <svelte:fragment slot="heading">{args.headingSlot}</svelte:fragment>
        <svelte:fragment>{args.defaultSlot}</svelte:fragment>
        <svelte:fragment slot="footer">{args.footerSlot}</svelte:fragment>
    </KpGenericPanel>
</Template>

<Story name='Default'
       args={{panelStyle: 'default', headingSlot: 'Main content', defaultSlot: 'Optional title', footerSlot: 'Optional footer'}}
/>
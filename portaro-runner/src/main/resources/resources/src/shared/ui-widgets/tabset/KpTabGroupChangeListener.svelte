<script lang="ts">
    import type {Writable} from 'svelte/store';
    import type {HeadlessTabsApiData, TabId} from 'shared/ui-widgets/tabset/types';
    import {createEventDispatcher, getContext, onDestroy} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {TABS_CONTEXT_NAME} from 'shared/ui-widgets/tabset/types';

    const dispatch = createEventDispatcher<{ 'tab-change': TabId }>();
    let selectedIndex = -1;

    const api: Writable<any> = getContext(TABS_CONTEXT_NAME);
    if (!api) {
        throw new Error(
            '<KpTabGroupChangeListener /> is missing a parent <TabGroup /> component.'
        );
    }

    const apiUnsubscribe = api.subscribe((apiData: HeadlessTabsApiData) => {
        if (apiData.tabs.length === 0) {
            return;
        }

        if (selectedIndex !== apiData.selectedIndex) {
            selectedIndex = apiData.selectedIndex;
            const selectedTab = apiData.tabs[selectedIndex];
            if (!exists(selectedTab)) {
                return;
            }

            const tabDataAttribute = selectedTab.dataset.tab;

            if (exists(tabDataAttribute)) {
                dispatch('tab-change', tabDataAttribute as TabId);
            }
        }
    });

    onDestroy(() => {
        apiUnsubscribe();
    });
</script>
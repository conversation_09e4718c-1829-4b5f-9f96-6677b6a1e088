import {isActionResponseWithText, isLabeled, isObject} from 'shared/utils/types-utils';
import {isString} from 'shared/utils/string-utils';
import {exists, isNullOrUndefined} from 'shared/utils/custom-utils';
import type {Labeled} from 'typings/portaro.be.types';


export function stringifyArgument(arg: any): string {
    const asString = arg?.toString();
    const type = typeof arg;

    if (type !== 'object') {
        return `{type=${type}, value=${asString}}`
    }

    let asJson: string;
    try {
        asJson = JSON.stringify(arg)
    } catch {
        asJson = 'JSON.stringify ERROR'
    }
    return `{type=${type}, value=${asJson}}`
}

export function isError(value: unknown): value is Error {
    return value instanceof Error;
}

export function isUserFriendlyError(value: unknown): value is Error & Labeled {
    return isError(value) && isLabeled(value);
}

const UNKNOWN_ERROR_MSG = 'Unknown error';

export function resolveErrorMessage(thrownObject: unknown): string {
    if (isNullOrUndefined(thrownObject)) {
        return UNKNOWN_ERROR_MSG;
    }

    if (isActionResponseWithText(thrownObject) || isUserFriendlyError(thrownObject)) {
        return thrownObject.text;
    }

    if (isError(thrownObject)) {
        return `${thrownObject.name}: ${thrownObject.message}`;
    }

    if (isObject(thrownObject) && 'error' in thrownObject && exists(thrownObject.error)) {
        return resolveErrorMessage(thrownObject.error);
    }

    if (isString(thrownObject)) {
        return thrownObject;
    }

    return UNKNOWN_ERROR_MSG;
}
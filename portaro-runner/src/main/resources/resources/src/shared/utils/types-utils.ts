import type {ActionResponse, ActionResponseWithText, Document, ExceptionActionResponse, FlattenedTreeListNode, FondedRecordLabeledReference, Identified, Labeled, LabeledIdentified, LabeledReference, LabeledValue, Rec, RecordSearchParams, SearchParams, UUID, Valuable} from 'typings/portaro.be.types';
import type {BasicPrimitiveType, Equatable, PayloadActionResponse} from 'typings/portaro.fe.types';
import {Kind, recordTypes, responseTypes, Subkind} from '../constants/portaro.constants';
import type {IHttpResponse} from 'angular';
import {exists, isDefined, isFunction, isValidUuidString} from 'shared/utils/custom-utils';
import {isArray} from 'shared/utils/array-utils';
import {stringifyArgument} from 'shared/utils/error-utils';


export function isActionResponse(object: unknown): object is ActionResponse {
    return exists(object) && isObject(object) && 'finished' in object && exists(object.responseType)
}

export function assertIsActionResponse(object: unknown): asserts object is ActionResponse {
    if (!isActionResponse(object)) {
        throw new TypeError(`${stringifyArgument(object)} is not a ActionResponse`)
    }
}

export function isActionResponseWithText(object: unknown): object is ActionResponseWithText {
    return isActionResponse(object) && isLabeled(object);
}

export function isExceptionActionResponse(object: unknown): object is ExceptionActionResponse {
    return isActionResponseWithText(object) && object.responseType === responseTypes.EXCEPTION
}

export function isPayloadActionResponse<T>(object: unknown): object is PayloadActionResponse<T> {
    return isActionResponse(object) && 'payload' in object;
}

export function assertIsPayloadActionResponse<T>(object: unknown): asserts object is PayloadActionResponse<T> {
    if (!isPayloadActionResponse(object)) {
        throw new TypeError(`${stringifyArgument(object)} is not a PayloadActionResponse`)
    }
}

export function isIHttpResponse<T>(object: unknown): object is IHttpResponse<T> {
    return exists(object) && isObject(object) && 'data' in object && 'status' in object && 'headers' in object && 'config' in object && 'statusText' in object && 'xhrStatus' in object;
}

function isDocumentType(type: string): boolean {
    return type === recordTypes.TYPE_DOCUMENT;
}

export function isRecord(object: unknown): object is Rec {
    return isObject(object) && 'type' in object && (object.type === Subkind.SUBKIND_AUTHORITY || object.type === Subkind.SUBKIND_DOCUMENT);
}

export function isRecordLabeledReference(object: any): object is FondedRecordLabeledReference {
    return isObject(object) && isLabeled(object) && isIdentified(object) && 'kind' in object && object.kind === Kind.KIND_RECORD && 'fond' in object;
}

export function isDocument(record: Rec): record is Document {
    return isRecord(record) && isDocumentType(record.type);
}

export function asDocument(record: Rec): Document {
    if (!isDocument(record)) {
        throw new TypeError(`${stringifyArgument(record)} is not a Document`)
    }
    return record;
}

export function isObject(value: unknown): value is Record<string, any> {
    const type = typeof value;
    return type === 'function' || type === 'object' && exists(value);
}

export function assertIsObject(value: unknown): asserts value is Record<string, any> {
    if (!isObject(value)) {
        throw new TypeError(`${stringifyArgument(value)} is not an object`)
    }
}

export function isBasicPrimitive(value: any): value is BasicPrimitiveType {
    const type = typeof value;
    return exists(value) && ['string', 'number', 'bigint', 'boolean', 'symbol'].includes(type);
}

export function assertIsBasicPrimitive(value: any): asserts value is BasicPrimitiveType {
    if (!isBasicPrimitive(value)) {
        throw new TypeError(`${stringifyArgument(value)} is not a primitive value`);
    }
}

export function isIdentified<T = unknown>(obj: unknown): obj is Identified<T> {
    return isObject(obj) && 'id' in obj && exists(obj.id);
}

export function isLabeled(obj: unknown): obj is Labeled {
    return isObject(obj) && 'text' in obj && exists(obj.text);
}

export function isValuable(obj: unknown): obj is Valuable<unknown> {
    return isObject(obj) && 'value' in obj && isDefined(obj.value); // value can be null
}

export function isLabeledIdentified<T = unknown>(labeled: Labeled): labeled is LabeledIdentified<T> {
    return isIdentified(labeled);
}

export function isLabeledReference<T = unknown>(labeled: Labeled): labeled is LabeledReference<T> {
    return isLabeledIdentified(labeled) && 'kind' in labeled;
}

export function isLabeledValue<T = unknown>(labeled: Labeled): labeled is LabeledValue<T> {
    return isValuable(labeled);
}

export function asIdentified(obj: unknown): Identified<unknown> {
    if (!isIdentified(obj)) {
        throw new TypeError(`${stringifyArgument(obj)} is not a Identified`)
    }
    return obj;
}

export function asLabeledReference<T = unknown>(labeled: Labeled): LabeledReference<T> {
    if (!isLabeledReference<T>(labeled)) {
        throw new TypeError(`${stringifyArgument(labeled)} is not a LabeledReference`)
    }
    return labeled;
}

export function assertIsFunction(value: any): asserts value is (...args: any[]) => any {
    if (typeof value !== 'function') {
        throw new TypeError(`${stringifyArgument(value)} is not a function`)
    }
}

export function isRecordSearchParams(params: SearchParams): params is RecordSearchParams {
    return !!(params && exists(params.kind) && params.kind.includes(Kind.KIND_RECORD));
}

export function asRecordSearchParams(params: SearchParams): RecordSearchParams {
    if (!isRecordSearchParams(params)) {
        throw new TypeError(`${stringifyArgument(params)} is not a RecordSearchParams`)
    }
    return params;
}

export function isUuid<T extends string = string>(value: T): value is UUID<T> {
    return isValidUuidString(value);
}

export function assertIsUuid<T extends string = string>(value: T): asserts value is UUID<T> {
    if (!isUuid(value)) {
        throw new TypeError(`${stringifyArgument(value)} is not a UUID`)
    }
}

export function asUuid<T extends string = string>(value: T): UUID<T> {
    assertIsUuid(value)
    return value;
}

export function isElement(value: unknown): value is Element {
    return value instanceof Element;
}

export function assertIsElement(value: unknown): asserts value is Element {
    if (!isElement(value)) {
        throw new TypeError(`${stringifyArgument(value)} is not a Element`)
    }
}

export function isHtmlInputElement(value: unknown): value is HTMLInputElement {
    return value instanceof HTMLInputElement;
}

export function assertIsHtmlInputElement(value: unknown): asserts value is HTMLInputElement {
    if (!isHtmlInputElement(value)) {
        throw new TypeError(`${stringifyArgument(value)} is not a HTMLInputElement`)
    }
}

export function isFlattenedTreeListNode(value: unknown): value is FlattenedTreeListNode {
    return isObject(value) &&
        'depth' in value &&
        exists(value.depth) &&
        'treeGraphElements' in value &&
        exists(value.treeGraphElements) &&
        isArray(value.treeGraphElements);
}

export function assertFlattenedTreeListNode(value: unknown): asserts value is FlattenedTreeListNode {
    if (!isFlattenedTreeListNode(value)) {
        throw new TypeError(`${stringifyArgument(value)} is not a FlattenedTreeListNode`);
    }
}

export function asFlattenedTreeListNode(value: unknown): FlattenedTreeListNode {
    assertFlattenedTreeListNode(value);
    return value;
}

export function isEquatable(value: unknown): value is Equatable {
    return isObject(value) && 'equals' in value && isFunction(value.equals);
}
/**
 * @ngdoc module
 * @name portaro.shared.utils.url-utils
 * @module portaro.shared.utils.url-utils
 */
import type {AnyObject} from 'typings/portaro.fe.types';

let _$injector: angular.auto.IInjectorService;

/**
 * This function must be inserted to `run` method of `angular.module`.
 * It is necessary for correct running of `url-utils`.
 *
 * @param {IInjectorService} $injector
 *
 * @example
 * ```
 *      angular.module('myModule', []).run(initUrlUtilsHelper);
 * ```
 */
export function initUrlUtilsHelper($injector: angular.auto.IInjectorService) {
    'ngInject';

    _$injector = $injector;
}

/**
 * @ngdoc type
 * @name getUrlParams
 * @module url-utils
 * @king function
 *
 * @returns {object}
 *
 * @description
 * Returns URL search params (section after `?`) as object.
 * If no URL doesn't contain any params, it returns empty object.
 */
export function getUrlParams(withAfterHash: boolean = false): { [p: string]: string & string[] } {
    const paramsObject = {};
    const query = _$injector.get('$window').location.search.substring(1);

    if (query.length > 0) {
        const entries = query.split('&');

        for (const entry of entries) {
            const pair = entry.split('=');

            const paramName = pair[0];
            const paramValue = pair[1] ? decodeURIComponent(pair[1].replace(/\+/g, '%20')) : undefined;

            // If paramsObject doesn't contain query parameter
            if (typeof paramsObject[paramName] === 'undefined') {
                paramsObject[paramName] = paramValue;
                // If paramsObject already contains query parameter indicates that it is probably array -> check if value is array. If not, create array from it.
            } else if (!Array.isArray(paramsObject[paramName])) {
                paramsObject[paramName] = [paramsObject[paramName], paramValue];
            } else {
                paramsObject[paramName].push(paramValue);
            }
        }
    }

    if (withAfterHash) {
        const paramsAfterHash = _$injector.get('$location').search();
        Object.assign(paramsObject, paramsAfterHash);
    }

    return paramsObject;
}

/**
 * @ngdoc type
 * @name getCurrentRoot
 * @module url-utils
 * @king function
 *
 * @returns {string}
 *
 * @description
 * Return current URL root.
 */
export function getCurrentRoot(): string {
    const $window = _$injector.get('$window');

    let currentRoot = `${$window.location.protocol}//${$window.location.hostname}`;

    if ($window.location.port) {
        currentRoot += `:${$window.location.port}`;
    }

    return currentRoot;
}

/**
 * @ngdoc type
 * @name isPartOfBaseURL
 * @module url-utils
 * @king function
 *
 * @param {string} url Testing URL.
 *
 * @returns {boolean}
 *
 * @description
 * Detect if `url` is part of root URL.
 */
export function isPartOfBaseURL(url: string): boolean {
    return url.startsWith('/') || url.startsWith(getCurrentRoot());
}

export function buildQueryStringWithoutFalsyValues(data: AnyObject) {
    const params = new URLSearchParams()

    Object.entries(data).forEach(([key, value]) => {
        if (value) {
            if (Array.isArray(value)) {
                value.forEach((item) => params.append(key, item.toString()))
            } else {
                // eslint-disable-next-line @typescript-eslint/no-base-to-string
                params.append(key, value.toString())
            }
        }
    });

    return params.toString()
}

export function replaceUrlHashbangParameter(url: string, parameterName: string, value: string): string {
    const [base, hashbang] = url.split('/#!');
    const [hashbangBase, hashbangParams] = hashbang.split('?');

    const params = new URLSearchParams(hashbangParams);
    params.set(parameterName, value);

    return `${base}/#!${hashbangBase}?${params.toString()}`;
}
import type {RangeValueEditorOptions} from './types';
import type {Formatter, Modifier} from '../../forms/types';
import {isDefined, isNull, isNullOrUndefined} from '../../../../utils/custom-utils';
import type {RangeValueEditorModel} from './types';

export function nullAsExtremeFormatter(options: RangeValueEditorOptions, valueWhenUndefined: number): Formatter {
    return (model: number | null): number => {
        if (isNullOrUndefined(model)) {
            return valueWhenUndefined;
        }
        return model;
    };
}

export function extremesAsNullModifier(options: RangeValueEditorOptions): Modifier {
    return (model: RangeValueEditorModel): RangeValueEditorModel => {
        if (options.extremesAsNull && model.from === options.min) {
            model.from = null;
        }
        if (options.extremesAsNull && model.to === options.max) {
            model.to = null;
        }
        return model;
    };
}

export function addMissingExtremeModifier(options: RangeValueEditorOptions): Modifier {
    return (model: RangeValueEditorModel): RangeValueEditorModel => {
        if (isNullOrUndefined(model)) {
            return model;
        }
        if (!isNull(model.from) || !isNull(model.to)) {
            return {
                from: model.from ?? options.min,
                to: model.to ?? options.max
            };
        } else {
            return model;
        }
    }
}

export function parseInputs(currentFrom: HTMLInputElement, currentTo: HTMLInputElement): [number, number] {
    const from = Number.parseInt(currentFrom.value, 10);
    const to = Number.parseInt(currentTo.value, 10);
    return [from, to];
}

export function setToggleAccessible(currentTarget: HTMLInputElement): void {
    if (Number(currentTarget.value) <= 0) {
        currentTarget.style.zIndex = String(2);
    } else {
        currentTarget.style.zIndex = String(0);
    }
}

export function roundToClosestMultipleOfStep(value: number, step: number, offset: number): number {
    return Math.round((value - offset) / step) * step + offset; // example value: 66, step: 20, offset: 10 -> 70
}

export function getPosition(value: number, min: number, max: number): number {
    return ((value - min) / (max - min)) * 100;
}

// Calculates pit points position and return css position
export function getPitPointsPosition(options: RangeValueEditorOptions): { top: string, left: string, position: string }[] {
    if (isDefined(options.pitPoints)) {
        return options.pitPoints.map((point) => {
            const position = getPosition(point, options.min, options.max);
            return {left: `${position}%`, top: '12px', position: 'absolute'};
        });
    }
    return [];
}


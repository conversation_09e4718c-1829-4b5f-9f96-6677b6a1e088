import type {ValueEditorOptions, ValueEditorValidations} from '../../../kp-value-editor/types';
import type {CommonValueEditorLocalizations} from '../_shared/common-types';
import type {SvelteComponentConstructor} from 'core/types';


/**
 * @ngdoc type
 * @name SingleAcceptableValueEditorOptions
 * @module portaro.value-editors.single-acceptable
 *
 * @template VALUE
 * @template ID
 *
 * @property {VALUE[]} acceptableValues Array of predefined values.
 * @property {Function} optionIdResolver
 * ```
 * function({option}: OptionIdResolverParams<VALUE>) => ID
 * ```
 * Option identifier getter.
 * If option (some acceptable value) is object, the editor requires some property to identify option for comparing, searching and tracking options.
 *
 * | argument&nbsp;name | Type | Description    |
 * | -------- | ----- | ----------------------- |
 * | `option` | VALUE | passed acceptable value |
 *
 * @property {SvelteComponentConstructor<{option: VALUE}>} optionLabelComponent Svelte component for displaying label of acceptable value. Current option is accessible via `option` prop.
 * @property {Function | undefined} sortComparator
 * ```
 * function({element1, element2}: SortComparatorParams<VALUE>) => number
 * ```
 *
 * If defined, options will be sorted using this comparator function.
 *
 * | argument&nbsp;name | Type  | Description    |
 * | ------------------ | ----- | -------------- |
 * | `element1`         | VALUE | Element 1      |
 * | `element2`         | VALUE | Element 2      |
 *
 * @property {number} switchToInlineModeThreshold If count of options is bigger than threshold, value editor switches into inline mode. If threshold is `0`, value editor never switch to inline mode.
 * @property {boolean} modelAsArray If `true`, model will be array also if `multiple = false`. In this case array will contain only one element.
 * @property {boolean} allowSelectNull If `true`, single selectable editors will allow select null option or deselect selected.
 * @property {Function} optionDisabledResolver
 * ```
 * function({item, model, options}: OptionDisabledResolverParams<VALUE>) => boolean
 * ```
 * Resolver for determine if `item` is disabled.
 *
 * | argument&nbsp;name | Type                             | Description     |
 * | --------- | ----------------------------------------- | --------------- |
 * | `item`    | VALUE                                     | Acceptable item |
 * | `model`   | VALUE                                     | Current model   |
 * | `options` | SingleAcceptableValueEditorOptions<VALUE> | Full options    |
 *
 *
 * @description
 * Extends {@link type:ValueEditorOptions}
 *
 * Default value: {@link SINGLE_ACCEPTABLE_VALUE_EDITOR_DEFAULT_OPTIONS}
 */
export interface SingleAcceptableValueEditorOptions<VALUE, ID = string | number> extends ValueEditorOptions {
    acceptableValues: VALUE[];
    switchToInlineModeThreshold?: number;
    blockInRow?: boolean;
    modelAsArray?: boolean;
    allowSelectNull?: boolean;
    optionLabelComponent?: SvelteComponentConstructor<{option: VALUE, usedAsLabel?: boolean}>;
    optionIdResolver?: ({option}: OptionIdResolverParams<VALUE>) => ID;
    sortComparator?: ({element1, element2}: SortComparatorParams<VALUE>) => number | undefined;
    optionDisabledResolver?: ({item, model, options}: OptionDisabledResolverParams<VALUE>) => boolean;
}

export interface OptionIdResolverParams<VALUE> {
    option: VALUE
}

export interface SortComparatorParams<VALUE> {
    element1: VALUE,
    element2: VALUE
}

export interface OptionDisabledResolverParams<VALUE> {
    item: VALUE,
    model: VALUE | VALUE[],
    options: SingleAcceptableValueEditorOptions<VALUE>
}

/**
 * @ngdoc type
 * @name SingleAcceptableValueEditorLocalizations
 * @module portaro.value-editors.single-acceptable
 *
 * @property {string} noneOfTheOptions text for the option of selecting null/deselecting other selected options
 *
 * @description
 * Extends {@link type:CommonValueEditorLocalizations}
 *
 * Default localizations: {@link SINGLE_ACCEPTABLE_VALUE_EDITOR_DEFAULT_LOCALIZATIONS}
 */
export interface SingleAcceptableValueEditorLocalizations extends CommonValueEditorLocalizations {
    noneOfTheOptions: string;
}

export interface SingleAcceptableValueEditorTypeMap {
    'single-acceptable': {options: SingleAcceptableValueEditorOptions<unknown>, validations: ValueEditorValidations, localizations: SingleAcceptableValueEditorLocalizations}
}
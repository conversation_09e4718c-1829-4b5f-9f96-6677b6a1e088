<script lang="ts">
    import type {FormControlOptions} from '../../forms/types';
    import type {TextOrSelectValueEditorLocalizations, TextOrSelectValueEditorOptions} from './types';
    import type {Readable} from 'svelte/store';
    import type {EditorsLocalizationFunction} from '../../../localizations/types';
    import type {TextValueEditorValidations} from '../text/types';
    import type {FormControl} from '../../forms/form-control';
    import type {ValueEditorSize} from '../../../types';
    import {FormControlBuilder} from '../../forms/form-control';
    import {formControls} from '../../forms/use.form-controls';
    import {style} from 'svelte-forms';
    import {onDestroy} from 'svelte';
    import {focusOnMount} from '../../shared/use.focus-on-mount';
    import {touched} from '../../shared/use.touched';
    import {ariaInvalid} from '../../errors/use.aria-invalid';
    import {ariaErrormessage} from '../../errors/use.aria-errormessage';
    import {getContext} from 'svelte';
    import {hasRequiredValidation, validatorFactory} from './utils';
    import {emptyAsNullFormatter, emptyAsNullParser} from '../_shared/editors-utils';
    import {get} from 'svelte/store';
    import {createInputDescription} from '../../description/utils';
    import {ariaDescribedby} from '../../description/use.aria-describedby';
    import {getValueEditorLabelContext} from '../../label/value-editor-label-context';
    import {required} from '../../forms/use.required';
    import {exists} from 'shared/utils/custom-utils';
    import {getLogger} from 'core/svelte-context/context';
    import {cloneDeep} from 'lodash-es';
    import {EDITORS_LOCALIZE, UNIVERSAL_FORM_MODEL} from '../../../context-keys';
    import ErrorMessages from '../../errors/ErrorMessages.svelte';
    import Description from '../../description/Description.svelte';
    import Selectbox from '../_shared/Selectbox.svelte';
    import TextualEditorsContextMenuWrapper from 'shared/value-editors/internal/editors/_shared/TextualEditorsContextMenuWrapper.svelte';

    export let model: string;
    export let formControlOptions: FormControlOptions;

    export let editorId: string;
    export let editorName: string;
    export let placeholder: string;

    export let isDisabled: boolean;
    export let isFocused: boolean;
    export let forceShowErrors: boolean;
    export let size: ValueEditorSize;

    export let options: TextOrSelectValueEditorOptions;
    export let validations: TextValueEditorValidations;

    const logger = getLogger();
    const localize: EditorsLocalizationFunction<TextOrSelectValueEditorLocalizations> = getContext(EDITORS_LOCALIZE);
    const universalFormModel$: Readable<Record<string, unknown>> = getContext(UNIVERSAL_FORM_MODEL);
    const formModel = get(universalFormModel$);
    const labelContext = getValueEditorLabelContext();

    let items: string[] = [];
    let showSelect: boolean;
    let selectedValue: string;
    let lastViewValue: string;
    let inputElement: HTMLInputElement;

    const formControl = FormControlBuilder.for(editorId, editorName, model)
        .withValidators(validatorFactory(validations, formModel, options))
        .withOptions(formControlOptions)
        .withModifiers([emptyAsNullParser(options)])
        .withFormatters([emptyAsNullFormatter(options)])
        .build(logger);

    if (exists(labelContext) && !hasRequiredValidation(validations.required, formModel, options.disableRequireIfFormContains)) {
        labelContext.config.update((currentConfig) => ({...currentConfig, disableRequiredAsterisk: true}));
    }

    const modelSubscription = formControl.getModelValue$().subscribe((value) => model = value);  // two-way data binding -> sending updated value up to a parent
    $: formControl.setModelValue(model); // two-way data binding -> receiving new model value from parent

    const viewValueSubscription = formControl.getViewValue$().subscribe((value) => {
        lastViewValue = value;
        selectedValue = value
    });
    $: {
        if (selectedValue !== lastViewValue) { // to prevent cycle (works as distinct until change filter)
            formControl.setViewValue(selectedValue);
            formControl.setTouched();
        }
    }

    const inputDescription = createInputDescription({editorId, editorName, description: localize('description')});

    if (options.canDoAction) {
        loadItems();
    }

    onDestroy(() => {
        modelSubscription.unsubscribe();
        viewValueSubscription.unsubscribe();
    })

    export function getFormController(): FormControl<string> {
        return formControl;
    }

    async function loadItems() {
        try {
            items = await options.dataSource({
                model,
                name: editorName,
                formModel: cloneDeep(get(universalFormModel$))
            });
        } catch (e) {
            logger.error('Unable to fetch items. Error:', e);
        } finally {
            showSelect = options.canDoAction && items && items.length > 0;
        }
    }

    function tryToFindSelectedValue(selectedItem: string): string | undefined {
        return items.find((item) => item === selectedItem);
    }
</script>

<TextualEditorsContextMenuWrapper {editorId} {inputElement} let:specialSymbolsKeyboardAction>
    {#if showSelect}
        <span id="{editorId}-select-or-type-manually" class="sr-only">{localize('selectOptionOrTypeManually')}</span>

        <div class="input-group input-group-{size}"
             role="group"
             aria-labelledby="{labelContext?.labelId ?? ''} {editorId}-select-or-type-manually">

            <div class="input-group-btn">
                <span id="{editorId}-suggested-options" class="sr-only">{localize('suggestedOptions')}</span>

                <Selectbox selected={selectedValue}
                           disabled={isDisabled}
                           {size}
                           selectboxOptions={items}
                           ariaLabelledby="{editorId}-suggested-options"
                           on:change={(event) => {selectedValue = event.detail; formControl.setTouched()}}>

                    <svelte:fragment slot="button">
                        {tryToFindSelectedValue(selectedValue) ?? localize('select')}
                    </svelte:fragment>

                    <svelte:fragment slot="option" let:option>
                        {option}
                    </svelte:fragment>
                </Selectbox>
            </div>

            <input class="form-control input-{size}"
                   type="text"
                   id={editorId}
                   name={editorName}
                   placeholder={placeholder || localize('orType')}
                   disabled={isDisabled}
                   maxlength={validations.maxlength}
                   minlength={validations.minlength}
                   use:required={hasRequiredValidation(validations.required, formModel, options.disableRequireIfFormContains)}
                   use:ariaErrormessage={formControl}
                   use:ariaInvalid={formControl}
                   use:focusOnMount={isFocused}
                   use:formControls={formControl}
                   use:style={{field: formControl.getFieldStateAsStore()}}
                   use:touched={{formControl}}
                   use:ariaDescribedby={inputDescription}
                   use:specialSymbolsKeyboardAction
                   bind:this={inputElement}
                   data-main-input/>
        </div>
    {:else}
        <input class="form-control input-{size}"
               type="text"
               id={editorId}
               name={editorName}
               placeholder={placeholder}
               disabled={isDisabled}
               maxlength={validations.maxlength}
               minlength={validations.minlength}
               use:required={hasRequiredValidation(validations.required, formModel, options.disableRequireIfFormContains)}
               use:ariaErrormessage={formControl}
               use:ariaInvalid={formControl}
               use:focusOnMount={isFocused}
               use:formControls={formControl}
               use:style={{field: formControl.getFieldStateAsStore()}}
               use:touched={{formControl}}
               use:ariaDescribedby={inputDescription}
               use:specialSymbolsKeyboardAction
               bind:this={inputElement}
               data-main-input/>
    {/if}

    <ErrorMessages formController={formControl} {forceShowErrors} {size}/>
    <Description {inputDescription}/>
</TextualEditorsContextMenuWrapper>
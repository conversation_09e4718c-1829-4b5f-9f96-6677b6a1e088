import valueEditorModule from '../../../../value-editors/value-editors.base.module';
import {createTestContext} from '../../../../../test-utils/utils';
import {
    createEditor, getDescriptionElement, getErrorMessageElementByErrorType,
    getInputElement,
    setEditorModelValue,
    setInputValueOn,
    waitForEvent, waitForNextState
} from '../../../test-utils/utils';
import {MODEL_CHANGE} from '../../../events';
import IInjectorService = angular.auto.IInjectorService;
import {waitForElementToBeRemoved} from '@testing-library/svelte';
import {AngularManualInjector} from 'core/injector';


describe('year-value-editor', () => {
    let testContext;

    beforeEach(() => {
        angular.mock.module(valueEditorModule);

        inject(/*@ngInject*/ ($injector: IInjectorService) => {
            testContext = createTestContext(new AngularManualInjector($injector));
        });
    });


    it('should render editor', async () => {
        const {container, componentInstance, unmount} = await createEditor<'year', number>('year', testContext);

        expect(componentInstance).toBeTruthy();
        expect(getInputElement<HTMLInputElement>(container)).toBeTruthy();

        unmount();
    });

    it('should not render editorId', async () => {
        const {container, unmount} = await createEditor<'year', number>('year', testContext);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.getAttribute('id')).toBeFalsy();

        unmount();
    });


    it('should render editorName', async () => {
        const {container, unmount} = await createEditor<'year', number>('year', testContext);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.getAttribute('name')).toMatch(new RegExp('^year_[a-fA-F0-9-_]+'));

        unmount();
    });


    it('should render editorId and editorName', async () => {
        const {container, unmount} = await createEditor<'year', number>('year', testContext, null, {editorId: 'test-id', editorName: 'test-name'});

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.getAttribute('id')).toBe('test-id');
        expect(inputElement.getAttribute('name')).toBe('test-name');

        unmount();
    });


    it('should render initial model', async () => {
        const {container, unmount} = await createEditor<'year', number>('year', testContext, 2020);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.value).toBe('2020');

        unmount();
    });

    it('should change model on input', async () => {
        const {container, componentInstance, unmount} = await createEditor<'year', number>('year', testContext, 2020);

        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '1996');

        let newModelValue: number = await waitForEvent(componentInstance, MODEL_CHANGE);
        expect(newModelValue).toBe(1996);
        expect(componentInstance.model).toBe(1996);

        setInputValueOn(inputElement, '');


        newModelValue = await waitForEvent(componentInstance, MODEL_CHANGE);
        expect(newModelValue).toBe(null);
        expect(componentInstance.model).toBe(null);

        unmount();
    });

    it('should change value if model is changed', async () => {
        const {container, componentInstance, unmount} = await createEditor<'year', number>('year', testContext, 2020);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.value).toBe('2020');

        await setEditorModelValue(componentInstance, 1996)
        expect(inputElement.value).toBe('1996');

        unmount();
    });

    it('should have working implicit valid date validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'year', number>('year', testContext, 2020);

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, 'not a valid date');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('date')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'date')).toBeTruthy();

        setInputValueOn(inputElement, '1996');

        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('date')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'date'), {container});
        expect(getErrorMessageElementByErrorType(container, 'date')).toBeFalsy();
        unmount();
    });

    it('should have working required validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'year', number>('year', testContext, 2020, {validations: {required: true}});

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('required')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'required')).toBeTruthy();

        setInputValueOn(inputElement, '1996');

        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('required')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'required'), {container});
        expect(getErrorMessageElementByErrorType(container, 'required')).toBeFalsy();
        unmount();
    });

    it('should have working minDate validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'year', number>('year', testContext, 2020, {validations: {minDate: 1980}});

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '1969');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('minDate')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'minDate')).toBeTruthy();

        setInputValueOn(inputElement, '1996');

        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('minDate')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'minDate'), {container});
        expect(getErrorMessageElementByErrorType(container, 'minDate')).toBeFalsy();
        unmount();
    });

    it('should have working maxDate validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'year', number>('year', testContext, 42, {validations: {maxDate: 2022}});

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '2025');
        controller.setTouched(); // to show errors

        let state = await waitForNextState(componentInstance);
        expect(state.errors.includes('maxDate')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'maxDate')).toBeTruthy();

        setInputValueOn(inputElement, '2015');

        state = await waitForNextState(componentInstance);
        expect(state.errors.includes('maxDate')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'maxDate'), {container});
        expect(getErrorMessageElementByErrorType(container, 'maxDate')).toBeFalsy();
        unmount();
    });


    it('should have working input disabling', async () => {
        const {container, unmount} = await createEditor<'year', number>('year', testContext, 2020, {isDisabled: true});

        expect(getInputElement<HTMLInputElement>(container).disabled).toBe(true);
        unmount();
    });

    it('should be focused', async () => {
        const {container, unmount} = await createEditor<'year', number>('year', testContext, 2020, {isFocused: true}, true);

        expect(document.activeElement).toEqual(getInputElement<HTMLInputElement>(container));
        unmount();
    });

    it('should correctly parse year before 1892', async () => {
        const {container, componentInstance, unmount} = await createEditor<'year', number>('year', testContext);

        const inputElement = getInputElement<HTMLInputElement>(container);
        setInputValueOn(inputElement, '1666');

        const newModelValue: number = await waitForEvent(componentInstance, MODEL_CHANGE);
        expect(newModelValue).toBe(1666);
        expect(componentInstance.model).toBe(1666);

        unmount();
    });

    it('should render editors description', async () => {
        const DESCRIPTION = 'some-description';
        const {container, unmount} = await createEditor<'year', number>('year', testContext, null, {
            localizations: {description: DESCRIPTION}
        });

        const descriptionElement = getDescriptionElement(container)
        expect(descriptionElement).toBeTruthy();
        expect(descriptionElement.textContent).toEqual(DESCRIPTION);
        expect(getInputElement(container).getAttribute('aria-describedby')).toEqual(descriptionElement.id);

        unmount();
    });
});
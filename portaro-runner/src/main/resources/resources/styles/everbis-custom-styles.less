@import "~bootstrap-less/bootstrap/variables";

:root {
    --brand-orange: #ff6633;
    --brand-orange-dark: #e04100;
}

/*** BRAND BUTTONS ***/

.btn-brand {
    border-radius: 1rem 0 1rem 0;
    transition: opacity 0.3s ease-in-out, background-color 0.3s ease-in-out, border-color 0.3s ease-in-out, border 0.3s ease-in-out, border-radius 0.15s cubic-bezier(.4, 0, .2, 1) !important;
}

.btn-brand:hover {
    border-radius: 1rem;
}

.btn-brand-primary {
    background-color: var(--brand-orange);
    border-color: var(--brand-orange);
    color: #fff;
}

.btn-brand-primary.active,
.btn-brand-primary.focus,
.btn-brand-primary:active,
.btn-brand-primary:focus,
.btn-brand-primary:hover {
    background-color: var(--brand-orange-dark);
    border-color: var(--brand-orange-dark);
    color: #fff;
}

.btn-brand-primary.disabled,
.btn-brand-primary.disabled.active,
.btn-brand-primary.disabled.focus,
.btn-brand-primary.disabled:active,
.btn-brand-primary.disabled:focus,
.btn-brand-primary.disabled:hover,
.btn-brand-primary[disabled],
.btn-brand-primary[disabled].active,
.btn-brand-primary[disabled].focus,
.btn-brand-primary[disabled]:active,
.btn-brand-primary[disabled]:focus,
.btn-brand-primary[disabled]:hover,
fieldset[disabled] .btn-brand-primary,
fieldset[disabled] .btn-brand-primary.active,
fieldset[disabled] .btn-brand-primary.focus,
fieldset[disabled] .btn-brand-primary:active,
fieldset[disabled] .btn-brand-primary:focus,
fieldset[disabled] .btn-brand-primary:hover {
    background-color: var(--brand-orange);
    border-color: var(--brand-orange);
}


/*** LANDING-PAGE JUMBOTRON ***/
.everbis-landing-page {
    .verbis-jumbotron {
        min-height: 40rem;
        display: flex;
        align-items: flex-end;

        background-image: url('../img/brand/verbis-pictogram-orange.svg?assets');
        background-repeat: no-repeat;
        background-position: ~"max(500px, calc(50% + 500px))" 0;
    }

    .verbis-jumbotron h1 {
        font-weight: bold;
        font-size: 3em;
    }

    .verbis-jumbotron .btn-primary {
        text-transform: uppercase;
    }

    .verbis-jumbotron p {
        color: #818181;
    }

    .verbis-jumbotron a {
        color: var(--brand-orange);
    }
}

/*** LANDING-PAGE LEFT-EDGED PANEL ***/

.everbis-landing-page {
    .left-edge-panel {
        display: grid;
        margin-top: 12rem;
        margin-bottom: 8rem;
    }
    .left-edge-panel-background {
        grid-area: 1/1;
        width: ~"min(calc(50% + 340px), 100%)";

        background: var(--brand-orange);
        border-radius: 0 2.5rem 2.5rem 0;
    }
    .left-edge-panel-content {
        grid-area: 1/1;
        display: flex;
        padding: 3rem 0;
    }
    .left-edge-panel, .left-edge-panel h2 {
        color: #fff;
    }
    .left-edge-panel-content-text {
        flex: 1 1 50%;
        margin: 10px
    }
    .left-edge-panel-content-video {
        margin: 10px;
    }
    .left-edge-panel h2 {
        margin-top: 4rem;
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 1px solid #fff;
    }
    .left-edge-panel ul {
        columns: 2;
    }
    .left-edge-panel ul li {
        display: flex;
        align-items: flex-start;
    }
    .left-edge-panel ul li::before {
        content: "•";
        font-weight: bold;
        margin-right: 5px;
    }

    .everbis-video-iframe {
        border-radius: 2.5rem;
    }

    @media (max-width: @screen-md-max) {
        /* responsive video on smaller screens */

        .left-edge-panel-content {
            /* use explicit column instead of wrapping */
            flex-direction: column;
        }

        /* https://css-tricks.com/fluid-width-video/#aa-iframe-video-youtube-vimeo-etc */
        .everbis-video-iframe-container {
            position: relative;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            height: 0;
        }

        /* https://css-tricks.com/fluid-width-video/#aa-iframe-video-youtube-vimeo-etc */
        .everbis-video-iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    }
}

/*** HERO CARD ***/

.everbis-index-page {
    .hero-card {
        display: grid;
        grid-auto-flow: column;
        grid-auto-columns: 2fr 1fr;
        gap: 1em;
        margin-top: 6em;

        @media (max-width: @screen-xs-max) {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            gap: 4em;
        }
    }
}

/*** INTRO TEXT ***/

.everbis-index-page {
    .intro-text {
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .intro-text h2 {
        color: #000;
        font-size: 2.5em;
    }

    .intro-text p {
        margin: 0;
        font-size: 1.2em;
        color: #000;
    }

    .intro-text a,
    .intro-text a:hover,
    .intro-text a:focus {
        color: var(--brand-orange);
    }
}

/*** CTA BUTTONS ***/

.everbis-index-page {
    .cta-buttons {
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 1em;

        @media (max-width: @screen-xs-max) {
            row-gap: 3em;
        }
    }

    .cta-buttons > * {
        width: 100%;
        max-width: 250px;
    }

    .ultrabig-button {
        font-size: 1.2em;
        width: 100%;
        padding: 10px 60px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    }
}

/*** STATS ***/

.everbis-index-page .stats-container {
    background-color: #fff7f5;
    padding: 6em 0;
    margin: 6em 0;
}

/**** pages/about ***/

.everbis-howto-page {
    .big-heading {
        font-size: 56px;
    }

    .info-panel {
        padding: 4em 2em;
        background: #eee;
        border-radius: 8px;
    }

    .info-block {
        display: flex;
        gap: 5em;
        margin: 2em 0;

        .info-img {
            border-radius: 2.5rem;
            height: 200px
        }

        @media (max-width: @screen-xs-max) {
            flex-direction: column;
            gap: 0;

            .info-text {
                order: 0;
            }

            .info-img {
                order: 1;
                align-self: center;
                height: auto;
                width: 100%;
            }
        }
    }
}

/*** pages/contacts ***/

.everbis-contacts-page {
    .big-heading {
        font-size: 56px;
    }

    .icon {
        height: 6em;
    }

    .contacts {
        padding: 2em 0;
        display: flex;
        justify-content: space-around;
        background: #eee;
        border-radius: 8px;

        @media (max-width: @screen-xs-max) {
            flex-direction: column;
            align-items: center;
            gap: 4em;
        }
    }

    .contact-info {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .contact-info a {
        margin-top: 1em;
    }

    .contact-info h2 {
        margin: 0;
    }
}

// Background colors
@themed-body-bg: var(--body-bg);
@themed-panel-bg: var(--panel-bg);

// Text colors
@themed-text-default: var(--text-default);
@themed-text-muted: var(--text-muted);
@themed-text-muted-label: var(--text-muted-label);

// Border colors
@themed-border-default: var(--border-default);
@themed-border-muted: var(--border-muted);
@themed-border-light: var(--border-light);
@themed-border-bold: var(--border-bold);

// Highlight colors
@themed-body-bg-orange-highlighted: var(--body-bg-orange-highlighted);
@themed-body-bg-blue-highlighted: var(--body-bg-blue-highlighted);
@themed-body-bg-red-highlighted: var(--body-bg-red-highlighted);

// Colors for focus styles (accessibility outline)
@themed-focus-outline-color: var(--focus-outline-color);
@themed-focus-box-shadow-color: var(--focus-box-shadow-color);

.portaro-theme-light {
    .light-colors();
}

.portaro-theme-dark {
    // TODO: Use dark colors
    .light-colors();
}

.light-colors() {
    --body-bg: #FFF;
    --panel-bg: #F8F8F8;
    --text-default: #333;
    --text-muted: #777;
    --text-muted-label: #AAA;
    --border-default: #CCC;
    --border-muted: #DFDFDF;
    --border-light: #EEEEEE;
    --border-bold: #AAA;
    --body-bg-orange-highlighted: #F4E1DC;
    --body-bg-blue-highlighted: #EAEEFF;
    --body-bg-red-highlighted: #FCEAEC;
    --focus-outline-color: black;
    --focus-box-shadow-color: white;
}

.dark-colors() {
    --body-bg: #19191C;
    --panel-bg: #131314;
    --text-default: #e9e9ec;
    --text-muted: #b3b3b4;
    --border-light: #1E1D21;
    --text-muted-label: #999;
    --border-default: #454556;
    --border-muted: #3f3f4f;
    --border-bold: #525265;
    --body-bg-orange-highlighted: #3F302A;
    --body-bg-blue-highlighted: #262940;
    --body-bg-red-highlighted: #371C22;
    --focus-outline-color: white;
    --focus-box-shadow-color: black;
}
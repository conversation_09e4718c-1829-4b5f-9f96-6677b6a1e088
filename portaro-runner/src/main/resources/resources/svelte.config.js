// svelte.config.js
// this file is required by the svelte-check for usage of svelte-preprocess
// because svelte-check does not pass tsconfig file (from cli arguments) to the preprocessor
// therefore preprocessor searches for "svelte.config.js" file, probably in the project root directory
// ES module syntax and typescript is not working, preprocessor requires "svelte.config.js" file

const sveltePreprocessConfig = require('./svelte-preprocess.config');

module.exports = {
    preprocess: sveltePreprocessConfig.preprocess
};
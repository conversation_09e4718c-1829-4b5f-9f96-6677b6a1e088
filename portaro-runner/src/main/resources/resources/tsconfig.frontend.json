{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "allowSyntheticDefaultImports": true,
    "noImplicitThis": true,
    "module": "esnext",
    "moduleResolution": "bundler", // TS 5 new feature - newer and better imports resolution
    "sourceMap": true,
    "target": "es2022", // keep this synchronized this with env settings of eslint and with compilerOptions' target in svelte-preprocess.config.js (svelte preprocess ignores value from tsconfig)
    "importHelpers": true, // import ts helper functions from tslib instead of generating them inline
    "lib": [
      "ESNext",
      "DOM",
      "DOM.Iterable"
    ]
  },
  "exclude": [
    "webpack",
    "idea",
    "docs",
    "reports",
    "dist"
  ]
}
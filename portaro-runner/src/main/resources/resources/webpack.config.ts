import * as merge from 'webpack-merge';

import {DEVELOPMENT_ENV, DEVELOPMENT_WITH_GRADLE_ENV, PRODUCTION_ENV, PRODUCTION_WITH_GRADLE_ENV, TEST_ENV} from './webpack/webpack.constants';

import commonConfigFactory from './webpack/webpack.common.config';
import develConfigFactory from './webpack/webpack.devel.config';
import productionConfigFactory from './webpack/webpack.production.config';
import testConfigFactory from './webpack/webpack.karma.config';

const commonConfig = commonConfigFactory.bind(this, __dirname);
const develConfig = develConfigFactory.bind(this, __dirname);
const productionConfig = productionConfigFactory.bind(this, __dirname);
const testConfig = testConfigFactory(__dirname);

const environment = process.env.ENVIRONMENT;

export default (env) => {

    if (typeof env !== 'string') {
        env = undefined;
    }

    switch (env || environment) {
        case PRODUCTION_ENV:
            return merge.merge(commonConfig(environment), productionConfig(environment));

        case PRODUCTION_WITH_GRADLE_ENV:
            return merge.merge(commonConfig(environment), develConfig(DEVELOPMENT_WITH_GRADLE_ENV), productionConfig(environment));

        case TEST_ENV:
            return testConfig;

        case DEVELOPMENT_ENV:
        case DEVELOPMENT_WITH_GRADLE_ENV:
            return merge.merge(commonConfig(environment), develConfig(environment));

        default:
            throw new Error('No environment is defined.');
    }
};

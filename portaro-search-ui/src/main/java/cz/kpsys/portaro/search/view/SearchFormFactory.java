package cz.kpsys.portaro.search.view;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.form.Form;
import cz.kpsys.portaro.search.MapBackedParams;
import lombok.NonNull;

import java.util.List;
import java.util.Locale;

public interface SearchFormFactory {

    <PARAMS extends MapBackedParams> List<Form> createForms(@NonNull String searchType,
                                                            @NonNull List<String> kinds,
                                                            @NonNull List<String> subkinds,
                                                            @NonNull PARAMS customParams,
                                                            @NonNull Department ctx,
                                                            @NonNull UserAuthentication currentAuth,
                                                            @NonNull Locale locale);
}

package cz.kpsys.portaro.search.z;

import cz.kpsys.portaro.appserver.oxm.AppserverTag;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.Splitter;
import cz.kpsys.portaro.search.field.QueryFieldsBySearchFieldLoader;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.convert.AbstractJunctionConverter;
import cz.kpsys.portaro.search.restriction.convert.GenericRestrictionConverter;
import cz.kpsys.portaro.search.restriction.convert.MatchingCompositeTermConverter;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import cz.kpsys.portaro.search.restriction.matcher.EqWords;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.Objects;

/**
 * Converts Restriction (Junction, Not, Term or RawRestriction)
 * Created by Jan Pachol on 14.03.2017.
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RestrictionToZServerQueryConverter extends GenericRestrictionConverter<SearchField, String> implements Converter<Restriction<? extends SearchField>, String> {

    public RestrictionToZServerQueryConverter(QueryFieldsBySearchFieldLoader<ZQueryField> zQueryFieldLoader) {
        MatchingCompositeTermConverter<ZQueryField, Object> zQueryFieldedTermConverter = new MatchingCompositeTermConverter<ZQueryField, Object>()
                .addByMatcherClass(Eq.class, new EqTermConverter())
                .addByMatcherClass(EqWords.class, new EqWordsTermConverter());

        withJunctionConverter(new ZServerQueryJunctionConverter(this));
        withTermConverter(new TermConverter(zQueryFieldedTermConverter, zQueryFieldLoader));
    }


    private static String createTerm(ZQueryField key, Object value) {
        return AppserverTag.group("constraintModel")
                .addChild(AppserverTag.group("constraint")
                        .addChild("semantic", key.getSemantic())
                        .addChild("relation", key.getRelation())
                        .addChild("position", key.getPosition())
                        .addChild("structure", key.getStructure())
                        .addChild("truncation", key.getTruncation())
                        .addChild("completion", key.getCompletion())
                )
                .addChild("model", value.toString())
                .toString();
    }



    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    private static class ZServerQueryJunctionConverter extends AbstractJunctionConverter<SearchField, String> {

        public ZServerQueryJunctionConverter(@NonNull Converter<Restriction<? extends SearchField>, String> restrictionConverter) {
            super(restrictionConverter);
        }

        @Override
        protected String convertConjunction(List<String> convertedJunctionItems) {
            return new ZQueryConjunction(convertedJunctionItems).toString();
        }

        @Override
        protected String convertDisjunction(List<String> convertedJunctionItems) {
            return new ZQueryDisjunction(convertedJunctionItems).toString();
        }

    }


    /**
     * Converts set of search fields and matcher (documentName =* "Babicka Nemcova") to string.
     * Spreads it by keys to disjunction P100="Babicka Nemcova" OR P110="Babicka Nemcova" OR P111="Babicka Nemcova"
     */
    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class TermConverter implements Converter<Term<SearchField>, String> {

        @NonNull Converter<Term<ZQueryField>, Object> zQueryFieldedTermConverter;
        @NonNull QueryFieldsBySearchFieldLoader<ZQueryField> zQueryFieldLoader;

        @Override
        public String convert(final Term<SearchField> term) {
            SearchField searchField = Objects.requireNonNull(term.field(), () -> String.format("Term search field is null (term %s)", term));

            List<ZQueryField> zQueryFields = Objects.requireNonNull(zQueryFieldLoader.getAllBySearchField(searchField), () -> "Null z-query search fields of %s".formatted(searchField));

            List<Term<ZQueryField>> singleKeyTerms = ListUtil.convert(zQueryFields, key -> new Term<>(key, term.matcher()));
            return new ZQueryDisjunction(ListUtil.convert(singleKeyTerms, zQueryFieldedTermConverter)).toString();
        }

    }


    /**
     * Converts single key term (P100="Babicka Nemcova")
     */
    private static class EqTermConverter implements Converter<Term<ZQueryField>, Object> {

        @Override
        public Object convert(Term<ZQueryField> source) {
            return createTerm(source.field(), ((Eq) source.matcher()).getValue());
        }

    }


    /**
     * Converts single key term (P100="Babicka Nemcova")
     */
    private static class EqWordsTermConverter implements Converter<Term<ZQueryField>, Object> {

        @Override
        public Object convert(final Term<ZQueryField> source) {
            final Object matcherValue = ((EqWords) source.matcher()).getValue();
            List<String> words = Splitter.splitBySpaceIgnoringDoubleQuotes(String.valueOf(matcherValue));

            return new ZQueryConjunction(ListUtil.convert(words, word -> createTerm(source.field(), word)));
        }

    }
}

package cz.kpsys.portaro.search.restriction.convert;

import cz.kpsys.portaro.search.restriction.FalseRestriction;
import cz.kpsys.portaro.search.restriction.Restriction;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

public class FieldTypeConvertingFalseRestrictionConverter<FIELD, TARGET> implements Converter<FalseRestriction<FIELD>, Restriction<? extends TARGET>> {

    @Override
    public FalseRestriction<TARGET> convert(@NonNull FalseRestriction<FIELD> source) {
        return FalseRestriction.create();
    }
}

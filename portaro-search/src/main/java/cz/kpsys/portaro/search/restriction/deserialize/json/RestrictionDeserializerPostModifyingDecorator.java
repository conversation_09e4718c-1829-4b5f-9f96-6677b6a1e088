package cz.kpsys.portaro.search.restriction.deserialize.json;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import cz.kpsys.portaro.datatype.DatatypableStringConverter;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.search.field.DatatypeBySearchFieldLoader;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.convert.RestrictionDeserializer;
import cz.kpsys.portaro.search.restriction.matcher.Between;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import cz.kpsys.portaro.search.restriction.matcher.In;
import cz.kpsys.portaro.search.restriction.matcher.SearchMatcher;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RestrictionDeserializerPostModifyingDecorator extends StdDeserializer<Restriction> {

    @NonNull RestrictionDeserializer delegate;
    @NonNull DatatypableStringConverter datatypableStringConverter;
    @NonNull DatatypeBySearchFieldLoader datatypeBySearchFieldLoader;

    public RestrictionDeserializerPostModifyingDecorator(RestrictionDeserializer delegate, DatatypableStringConverter datatypableStringConverter, DatatypeBySearchFieldLoader datatypeBySearchFieldLoader) {
        super(Restriction.class);
        this.delegate = delegate;
        this.datatypableStringConverter = datatypableStringConverter;
        this.datatypeBySearchFieldLoader = datatypeBySearchFieldLoader;
    }


    @Override
    public Restriction deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        Restriction deserialized = delegate.deserialize(jp, ctxt);

        if (!(deserialized instanceof Term)) {
            return deserialized;
        }

        SearchField searchField = (SearchField) ((Term<?>) deserialized).field();
        Optional<ScalarDatatype> maybeSearchFieldDatatype = datatypeBySearchFieldLoader.getOptionalBySearchField(searchField);
        if (maybeSearchFieldDatatype.isEmpty()) {
            return deserialized;
        }
        ScalarDatatype searchFieldDatatype = maybeSearchFieldDatatype.get();


        SearchMatcher matcher = ((Term<?>) deserialized).matcher();

        switch (matcher) {
            case Eq eq -> {
                Object originalValue = eq.getValue();
                Object convertedValue = datatypableStringConverter.convertFromSimpleTypePreservingStructure(originalValue, searchFieldDatatype);
                matcher = new Eq(convertedValue);
            }
            case Between<?> between -> {
                Object originalFrom = between.from();
                Object originalTo = between.to();
                Object convertedFrom = datatypableStringConverter.convertFromSimpleTypePreservingStructure(originalFrom, searchFieldDatatype);
                Object convertedTo = datatypableStringConverter.convertFromSimpleTypePreservingStructure(originalTo, searchFieldDatatype);
                matcher = between.ofConverted(convertedFrom, convertedTo);
            }
            case In in -> {
                Collection<?> originalValue = in.value();
                Collection<?> convertedValue = (List<?>) datatypableStringConverter.convertFromSimpleTypePreservingStructure(originalValue, searchFieldDatatype);
                matcher = new In(convertedValue);
            }
            default -> {
                return deserialized;
            }
        }

        return new Term<>(searchField, matcher);
    }

}

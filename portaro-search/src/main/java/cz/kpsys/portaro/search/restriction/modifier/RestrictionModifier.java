package cz.kpsys.portaro.search.restriction.modifier;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.search.SearchParams;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.Conjunction;

public interface RestrictionModifier<PARAMS extends SearchParams> {

    Conjunction<SearchField> modify(Conjunction<SearchField> conjunction, PARAMS params, Department ctx);

}

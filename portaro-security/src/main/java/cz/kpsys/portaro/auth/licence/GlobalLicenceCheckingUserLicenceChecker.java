package cz.kpsys.portaro.auth.licence;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.licence.Licence;
import cz.kpsys.portaro.licence.Module;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class GlobalLicenceCheckingUserLicenceChecker implements UserLicenceChecker {

    @NonNull Provider<@NonNull Licence> licenceProvider;

    @Override
    public boolean hasCatalogLicence(UserAuthentication currentAuth, Department department) {
        return licenceProvider.get().isEnabled(Module.CATALOGING);
    }

    @Override
    public boolean hasLoanLicence(UserAuthentication currentAuth, Department department) {
        return licenceProvider.get().isEnabled(Module.LOANING);
    }
}

package cz.kpsys.portaro.security;

import cz.kpsys.portaro.commons.object.BasicIdentified;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class Action<SUBJECT> extends BasicIdentified<String> {

    @Getter
    @NonNull
    Class<? extends SUBJECT> subjectClass;

    private Action(@NonNull String id, @NonNull Class<? extends SUBJECT> subjectClass) {
        super(id);
        this.subjectClass = subjectClass;
    }

    public static Action<Void> withoutSubject(@NonNull String id) {
        return withSubject(id, Void.class);
    }

    public static <SUBJECT> Action<SUBJECT> withSubject(@NonNull String id, @NonNull Class<? extends SUBJECT> subjectClass) {
        return new Action<SUBJECT>(id, subjectClass);
    }

    @Override
    public String toString() {
        return getId();
    }
}

package cz.kpsys.portaro.sip2.client.deserialize.impl;

import cz.kpsys.portaro.sip2.client.deserialize.InvalidSip2ResponseValueException;
import cz.kpsys.portaro.sip2.client.deserialize.RequiredSip2ValueParsing;
import cz.kpsys.portaro.sip2.client.deserialize.Sip2ResponseParser;
import cz.kpsys.portaro.sip2.client.model.Sip2CheckinResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import static cz.kpsys.portaro.sip2.Sip2Constants.FieldCodes.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class Sip2CheckinResponseParser implements Sip2ResponseParser<Sip2CheckinResponse> {

    @NonNull MediaTypeParser mediaTypeParser;
    @NonNull AlertTypeParser alertTypeParser;

    public static Sip2CheckinResponseParser createDefault() {
        return new Sip2CheckinResponseParser(
                new MediaTypeParser(),
                new AlertTypeParser()
        );
    }

    @Override
    public Sip2CheckinResponse parse(RequiredSip2ValueParsing parsing) throws InvalidSip2ResponseValueException {
        Sip2CheckinResponse response = new Sip2CheckinResponse();

        response.setOk(parsing.from(2).first1Char().as01Boolean());
        response.setResensitize(parsing.from(3).first1Char().asNYBoolean());
        response.setMagneticMedia(parsing.from(4).first1Char().asNYUThroolean());
        response.setAlert(parsing.from(5).first1Char().asNYBoolean());
        response.setTransactionDate(parsing.from(6).dateTimeInstant());

        RequiredSip2ValueParsing fieldsParsing = parsing.from(24);

        response.setInstitutionId(fieldsParsing.field(AO_INSTITUTION_ID).stringValue());
        response.setItemIdentifier(fieldsParsing.field(AB_ITEM_IDENTIFIER).stringValue());
        response.setPermanentLocation(fieldsParsing.field(AQ_PERMANENT_LOCATION).stringValue());
        response.setTitleIdentifier(fieldsParsing.field(AJ_TITLE_IDENTIFIER).optional().notBlank().stringValue());
        response.setSortBin(fieldsParsing.field(CL_SORT_BIN).optional().notBlank().stringValue());
        response.setPatronIdentifier(fieldsParsing.field(AA_PATRON_IDENTIFIER).optional().notBlank().stringValue());
        response.setMediaType(fieldsParsing.field(CK_MEDIA_TYPE).optional().map(mediaTypeParser::getMediaType));
        response.setItemProperties(fieldsParsing.field(CH_ITEM_PROPERTIES).optional().notBlank().stringValue());

        /* SIP2 Extensions - Begin */
        response.setCollectionCode(fieldsParsing.field(CR_COLLECTION_CODE).optional().notBlank().stringValue());
        response.setCallNumber(fieldsParsing.field(CS_CALL_NUMBER).optional().notBlank().stringValue());
        response.setDestinationLocation(fieldsParsing.field(CT_DESTINATION_LOCATION).optional().notBlank().stringValue());
        response.setAlertType(fieldsParsing.field(CV_ALERT_TYPE).optional().map(alertTypeParser::getAlertType));
        response.setHoldPatronId(fieldsParsing.field(CY_HOLD_PATRON_ID).optional().notBlank().stringValue());
        response.setHoldPatronName(fieldsParsing.field(DA_HOLD_PATRON_NAME).optional().notBlank().stringValue());
        /* SIP2 Extensions - End */

        response.setScreenMessage(fieldsParsing.fieldValues(AF_SCREEN_MESSAGE));
        response.setPrintLine(fieldsParsing.fieldValues(AG_PRINT_LINE));

        fieldsParsing.sequence().ifPresent(response::setSequence);

        return response;
    }
}

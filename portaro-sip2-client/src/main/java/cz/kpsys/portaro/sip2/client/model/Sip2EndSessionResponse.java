package cz.kpsys.portaro.sip2.client.model;

import cz.kpsys.portaro.sip2.CommandType;
import cz.kpsys.portaro.sip2.client.Sip2MessageResponse;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;

import java.time.Instant;
import java.util.List;

/**
 * The ILS must send this message in response to the
 * {@link Sip2EndPatronSessionRequest SIP2EndPatronSessionRequest} message.
 */
@Getter
@Setter
public class Sip2EndSessionResponse extends Sip2MessageResponse {

    /**
     * Tells if the ILS has ended the patron's session. False means
     * an error condition.
     */
    private boolean endSession;

    /**
     * Date and time of the request.
     */
    @NonNull
    private Instant transactionDate;

    /**
     * Library's institution id.
     */
    @NonNull
    private String institutionId;

    /**
     * An identifying value for the patron, library card's barcode number for example.
     */
    @NonNull
    private String patronIdentifier;

    /**
     * Variable-length field. This field provides a way for the ILS to display messages on the system's screen. They are never required. When used, there can be one or more of those fields, which are then displayed on consecutive lines of the screen.
     */
    private List<String> screenMessage;

    /**
     * Variable-length field. This field provides a way for the ILS to print messages on the system's printer. They are never required. When used, there can be one or more of these fields, which are then pronted on consecutive lines of the printer.
     */
    private List<String> printLine;

    /**
     * Constructs and initializes a new SIP2EndSessionResponse object containing
     * the given data.
     */
    public Sip2EndSessionResponse() {
        super(CommandType.END_SESSION_RESPONSE);
    }

}

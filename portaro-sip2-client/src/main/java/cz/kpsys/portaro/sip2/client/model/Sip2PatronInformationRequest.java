package cz.kpsys.portaro.sip2.client.model;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.sip2.CommandType;
import cz.kpsys.portaro.sip2.client.Sip2MessageRequest;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
public class Sip2PatronInformationRequest extends Sip2MessageRequest<Sip2PatronInformationResponse> {

    /**
     * Language requested by the patron.
     */
    @NonNull
    private Language language = Language.UNKNOWN;

    /**
     * Date and time of the request.
     */
    @NonNull
    private Instant transactionDate;

    /**
     * Defines what detailed and summary information of selected category of items that are be requested as a part of this request. For example a list of items that the patron has borrowed.
     */
    @NonNull
    private Summary summary;

    /**
     * Library's institution id.
     */
    @NonNull
    private String institutionId = "";

    /**
     * An identifying value for the patron, library card's barcode number for example.
     */
    @NonNull
    private String patronIdentifier;

    /**
     * Password for the system to login to the ILS. If this feature is not used by the ILS in the library then the value should be empty if it's required in the request, and can be omitted entirely if the field is optional in the message.
     */
    @NullableNotBlank
    private String terminalPassword;

    /**
     * Password (PIN) of the patron. If this feature is not used by the ILS in the library then the value should be empty if it's required in the request, and can be omitted entirely if the field is optional in the message.
     */
    @NullableNotBlank
    private String patronPassword;

    /**
     * Specifies the number of the first item to be sent in the list defined
     * by the summary. Optional field.
     */
    @NullableNotBlank
    private String startItem;

    /**
     * Specifies the number of the last item to be sent in the list defined
     * by the summary. Optional field.
     */
    @NullableNotBlank
    private String endItem;

    public Sip2PatronInformationRequest(@NonNull Instant transactionDate, @NonNull String patronIdentifier) {
        super(CommandType.PATRON_INFORMATION_REQUEST);
        this.transactionDate = transactionDate;
        this.summary = new Summary();
        this.patronIdentifier = patronIdentifier;
    }

    public Sip2PatronInformationRequest(@NonNull Instant transactionDate, @NonNull String institutionId, String patronIdentifier) {
        this(transactionDate, patronIdentifier);
        this.institutionId = institutionId;
    }

    public Sip2PatronInformationRequest(@NonNull Instant transactionDate, @NonNull String institutionId, String patronIdentifier, String patronPassword) {
        this(transactionDate, patronIdentifier);
        this.institutionId = institutionId;
        this.patronPassword = patronPassword;
    }

    public Sip2PatronInformationRequest(@NonNull Instant transactionDate, @NonNull String institutionId, String terminalPassword, String patronIdentifier, String patronPassword) {
        this(transactionDate, patronIdentifier);
        this.institutionId = institutionId;
        this.terminalPassword = terminalPassword;
        this.patronPassword = patronPassword;
    }

}

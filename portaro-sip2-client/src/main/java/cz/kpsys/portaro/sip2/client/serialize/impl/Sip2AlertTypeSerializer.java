package cz.kpsys.portaro.sip2.client.serialize.impl;

import cz.kpsys.portaro.sip2.client.model.AlertType;
import cz.kpsys.portaro.sip2.client.serialize.CommandSerializing;
import cz.kpsys.portaro.sip2.client.serialize.TypedSip2ValueSerializer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Sip2AlertTypeSerializer implements TypedSip2ValueSerializer<AlertType> {

    @Override
    public void serialize(@NonNull AlertType alertType, CommandSerializing.@NonNull ValueSerializing val) {
        val.char2ByInt(getIntValue(alertType));
    }

    private static int getIntValue(@NonNull AlertType alertType) {
        return switch (alertType) {
            case UNKNONW -> 0;
            case HOLD_FOR_THIS_LIBRARY -> 1;
            case HOLD_FOR_OTHER_BRANCH -> 2;
            case HOLD_FOR_ILL -> 3;
            case SENT_TO_OTHER_BRANCH -> 4;
            case OTHER -> 99;
        };
    }

}

package cz.kpsys.portaro.sip2.client.serialize.impl;

import cz.kpsys.portaro.sip2.client.model.CirculationStatus;
import cz.kpsys.portaro.sip2.client.serialize.CommandSerializing;
import cz.kpsys.portaro.sip2.client.serialize.TypedSip2ValueSerializer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Sip2CirculationStatusSerializer implements TypedSip2ValueSerializer<CirculationStatus> {

    @Override
    public void serialize(@NonNull CirculationStatus circulationStatus, CommandSerializing.@NonNull ValueSerializing val) {
        val.char2ByInt(getIntValue(circulationStatus));
    }

    private static int getIntValue(@NonNull CirculationStatus circulationStatus) {
        return switch (circulationStatus) {
            case OTHER -> 1;
            case ON_ORDER -> 2;
            case AVAILABLE -> 3;
            case CHARGED -> 4;
            case CHARGED_NOT_TO_BE_RECALLED_UNTIL_EARLIEST_RECALL_DATE -> 5;
            case IN_PROCESS -> 6;
            case RECALLED -> 7;
            case WAITING_ON_HOLD_SHELF -> 8;
            case WAITING_TO_BE_RESHELVED -> 9;
            case IN_TRANSIT -> 10;
            case CLAIMED_RETURNED -> 11;
            case LOST -> 12;
            case MISSING -> 13;
        };
    }

}

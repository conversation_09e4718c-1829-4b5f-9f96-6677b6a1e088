package cz.kpsys.portaro.sip2.client.serialize.impl;

import cz.kpsys.portaro.sip2.client.serialize.CommandSerializing;
import cz.kpsys.portaro.sip2.client.serialize.TypedSip2ValueSerializer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.Instant;
import java.time.format.DateTimeFormatter;

import static cz.kpsys.portaro.sip2.Sip2Constants.CZECH_TIME_ZONE;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Sip2InstantSerializer implements TypedSip2ValueSerializer<Instant> {

    @Override
    public void serialize(@NonNull Instant instant, @NonNull CommandSerializing.ValueSerializing val) {
        val.date(getStringValue(instant));
    }

    private static String getStringValue(@NonNull Instant instant) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd    HHmmss")
                .withZone(CZECH_TIME_ZONE);
        return formatter.format(instant);
    }
}

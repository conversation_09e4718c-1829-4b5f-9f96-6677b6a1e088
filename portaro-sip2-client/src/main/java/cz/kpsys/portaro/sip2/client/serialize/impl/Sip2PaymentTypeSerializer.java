package cz.kpsys.portaro.sip2.client.serialize.impl;

import cz.kpsys.portaro.sip2.client.model.PaymentType;
import cz.kpsys.portaro.sip2.client.serialize.CommandSerializing;
import cz.kpsys.portaro.sip2.client.serialize.TypedSip2ValueSerializer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Sip2PaymentTypeSerializer implements TypedSip2ValueSerializer<PaymentType> {

    @Override
    public void serialize(@NonNull PaymentType paymentType, CommandSerializing.@NonNull ValueSerializing val) {
        val.char2ByInt(getIntValue(paymentType));
    }

    private static int getIntValue(@NonNull PaymentType paymentType) {
        return switch (paymentType) {
            case CASH -> 0;
            case VISA -> 1;
            case CREDIT_CARD -> 2;
        };
    }

}

package cz.kpsys.portaro.sip2.client.deserialize.impl;

import cz.kpsys.portaro.commons.object.Throolean;
import cz.kpsys.portaro.sip2.client.deserialize.RequiredSip2ValueParsing;
import cz.kpsys.portaro.sip2.client.model.CurrencyType;
import cz.kpsys.portaro.sip2.client.model.Language;
import cz.kpsys.portaro.sip2.client.model.Sip2PatronInformationResponse;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@Tag("ci")
@Tag("unit")
class Sip2PatronInformationResponseParserTest {

    private final Sip2PatronInformationResponseParser parser = Sip2PatronInformationResponseParser.createDefault();

    @Test
    void shouldParse() {
        Sip2PatronInformationResponse response = parser.parse(RequiredSip2ValueParsing.of("64 Y Y YY  YYY  00120230717    182434000400050006000700080009AA210123456789|AEtestFirstName testLastName|AFUzivatel testFirstName testLastName|AOKPSYS|<EMAIL>|BLY|CA0500|CB9999|CQY|AY9AZC2ED\r"));

        assertFalse(response.getStatus().isChargePrivilegesDenied());
        assertTrue(response.getStatus().isRenewalPrivilegesDenied());
        assertFalse(response.getStatus().isRecallPrivilegesDenied());
        assertTrue(response.getStatus().isHoldPrivilegesDenied());
        assertFalse(response.getStatus().isCardReportedLost());
        assertTrue(response.getStatus().isTooManyItemsCharged());
        assertTrue(response.getStatus().isTooManyItemsOverdue());
        assertFalse(response.getStatus().isTooManyRenewals());
        assertFalse(response.getStatus().isTooManyClaimsOfItemsReturned());
        assertTrue(response.getStatus().isTooManyItemsLost());
        assertTrue(response.getStatus().isExcessiveOutstandingFines());
        assertTrue(response.getStatus().isExcessiveOutstandingFees());
        assertFalse(response.getStatus().isRecallOverdue());
        assertFalse(response.getStatus().isTooManyItemsBilled());
        assertEquals(Language.ENGLISH, response.getLanguage());
        assertEquals(Instant.parse("2023-07-17T16:24:34Z"), response.getTransactionDate());
        assertEquals(4, response.getHoldItemsCount());
        assertEquals(5, response.getOverdueItemsCount());
        assertEquals(6, response.getChargedItemsCount());
        assertEquals(7, response.getFineItemsCount());
        assertEquals(8, response.getRecallItemsCount());
        assertEquals(9, response.getUnavailableHoldsCount());

        assertEquals("210123456789", response.getPatronIdentifier());
        assertEquals("testFirstName testLastName", response.getPersonalName());
        assertEquals(List.of("Uzivatel testFirstName testLastName"), response.getScreenMessage());
        assertEquals(List.of(), response.getPrintLine());
        assertEquals("KPSYS", response.getInstitutionId());
        assertEquals("<EMAIL>", response.getEmail());
        assertNull(response.getHomeAddress());
        assertNull(response.getPhone());
        assertEquals(Throolean.TRUE, response.getValidPatron());
        assertNull(response.getHoldItemsLimit());
        assertEquals(500, response.getOverdueItemsLimit());
        assertEquals(9999, response.getChargedItemsLimit());
        assertEquals(Throolean.TRUE, response.getValidPatronPassword());
        assertNull(response.getCurrencyType());
        assertNull(response.getFeeAmount());
        assertEquals(9, response.getSequence());
    }

    @Test
    void shouldParseTritiusReponse() {
        Sip2PatronInformationResponse response = parser.parse(RequiredSip2ValueParsing.of("64              00020230821    115653                        AA30|AEBenda Petr|AO|<EMAIL> |BF777251715|BHCZK|BLY|BV0|BZ0000|CA0000|CB0000|CC0|CQN|AY0AZD7BE\r"));

        assertFalse(response.getStatus().isChargePrivilegesDenied());
        assertFalse(response.getStatus().isRenewalPrivilegesDenied());
        assertFalse(response.getStatus().isRecallPrivilegesDenied());
        assertFalse(response.getStatus().isHoldPrivilegesDenied());
        assertFalse(response.getStatus().isCardReportedLost());
        assertFalse(response.getStatus().isTooManyItemsCharged());
        assertFalse(response.getStatus().isTooManyItemsOverdue());
        assertFalse(response.getStatus().isTooManyRenewals());
        assertFalse(response.getStatus().isTooManyClaimsOfItemsReturned());
        assertFalse(response.getStatus().isTooManyItemsLost());
        assertFalse(response.getStatus().isExcessiveOutstandingFines());
        assertFalse(response.getStatus().isExcessiveOutstandingFees());
        assertFalse(response.getStatus().isRecallOverdue());
        assertFalse(response.getStatus().isTooManyItemsBilled());
        assertEquals(Language.UNKNOWN, response.getLanguage());
        assertEquals(Instant.parse("2023-08-21T09:56:53Z"), response.getTransactionDate());
        assertEquals(0, response.getHoldItemsCount());
        assertEquals(0, response.getOverdueItemsCount());
        assertEquals(0, response.getChargedItemsCount());
        assertEquals(0, response.getFineItemsCount());
        assertEquals(0, response.getRecallItemsCount());
        assertEquals(0, response.getUnavailableHoldsCount());

        assertEquals("30", response.getPatronIdentifier());
        assertEquals("Benda Petr", response.getPersonalName());
        assertEquals(List.of(), response.getScreenMessage());
        assertEquals(List.of(), response.getPrintLine());
        assertEquals("", response.getInstitutionId());
        assertEquals("<EMAIL>", response.getEmail());
        assertNull(response.getHomeAddress());
        assertEquals("777251715", response.getPhone());
        assertEquals(Throolean.TRUE, response.getValidPatron());
        assertEquals(0, response.getHoldItemsLimit());
        assertEquals(0, response.getOverdueItemsLimit());
        assertEquals(0, response.getChargedItemsLimit());
        assertEquals(Throolean.FALSE, response.getValidPatronPassword());
        assertEquals(CurrencyType.CZK, response.getCurrencyType());
        assertEquals("0", response.getFeeAmount());
        assertEquals(0, response.getSequence());
    }
}
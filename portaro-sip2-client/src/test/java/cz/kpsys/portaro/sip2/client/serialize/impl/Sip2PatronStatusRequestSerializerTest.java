package cz.kpsys.portaro.sip2.client.serialize.impl;

import cz.kpsys.portaro.sip2.client.Sip2Command;
import cz.kpsys.portaro.sip2.client.model.Language;
import cz.kpsys.portaro.sip2.client.model.Sip2PatronStatusRequest;
import cz.kpsys.portaro.sip2.client.model.Sip2PatronStatusResponse;
import cz.kpsys.portaro.sip2.client.serialize.Sip2RequestSerializerTestParent;
import cz.kpsys.portaro.sip2.client.serialize.TypedSip2MessageSerializer;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.time.Instant;

import static cz.kpsys.portaro.sip2.Sip2Constants.CommandCharacters.NEWLINE;
import static cz.kpsys.portaro.sip2.client.TestUtils.SIP2_DATE_TIME_INSTANT;

@Tag("ci")
@Tag("unit")
public class Sip2PatronStatusRequestSerializerTest extends Sip2RequestSerializerTestParent<Sip2PatronStatusRequest, Sip2PatronStatusResponse> {

    protected TypedSip2MessageSerializer<Sip2Command<Sip2PatronStatusRequest, Sip2PatronStatusResponse>> getSerializer() {
        return new NewlineAddingSip2MessageSerializer<>(ErrorDetectionAddingSip2RequestSerializer.withSequenceAndChecksum(Sip2PatronStatusRequestSerializer.createDefault()));
    }

    @Test
    public void testPatronStatusRequestWithoutErrorDetection() {
        test(
                () -> new Sip2PatronStatusRequest(SIP2_DATE_TIME_INSTANT, "institutionId", "terminalPassword", "patronIdentifier", "patronPassword"),
                false,
                "2300020210814    083455AOinstitutionId|AApatronIdentifier|ACterminalPassword|ADpatronPassword|" + NEWLINE
        );
    }

    @Test
    public void testPatronStatusRequestWithErrorDetection1() {
        test(
                () -> {
                    Sip2PatronStatusRequest req = new Sip2PatronStatusRequest(SIP2_DATE_TIME_INSTANT, "patronIdentifier");
                    req.setSequence(2);
                    return req;
                },
                true,
                "2300020210814    083455AO|AApatronIdentifier|AC|AD|AY2AZEFB7" + NEWLINE
        );
    }

    @Test
    public void testPatronStatusRequestWithErrorDetection2() {
        test(
                () -> {
                    Sip2PatronStatusRequest req = new Sip2PatronStatusRequest(SIP2_DATE_TIME_INSTANT, "institutionId", "patronIdentifier");
                    req.setLanguage(Language.ITALIAN);
                    return req;
                },
                true,
                "2300420210814    083455AOinstitutionId|AApatronIdentifier|AC|AD|AY0AZEA3E" + NEWLINE
        );
    }

    @Test
    public void testPatronStatusRequestWithErrorDetection3() {
        test(
                () -> {
                    Sip2PatronStatusRequest req = new Sip2PatronStatusRequest(Instant.parse("2007-01-02T07:01:09Z"), "institutionId", "patronIdentifier", "patronPassword");
                    req.setSequence(1);
                    return req;
                },
                true,
                "2300020070102    080109AOinstitutionId|AApatronIdentifier|AC|ADpatronPassword|AY1AZE467" + NEWLINE
        );
    }

    @Test
    public void testPatronStatusRequestWithErrorDetection4() {
        test(
                () -> new Sip2PatronStatusRequest(SIP2_DATE_TIME_INSTANT, "institutionId", "terminalPassword", "patronIdentifier", "patronPassword"),
                true,
                "2300020210814    083455AOinstitutionId|AApatronIdentifier|ACterminalPassword|ADpatronPassword|AY0AZDDAC" + NEWLINE
        );
    }
}

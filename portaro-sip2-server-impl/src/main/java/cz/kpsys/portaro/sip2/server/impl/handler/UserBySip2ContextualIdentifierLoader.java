package cz.kpsys.portaro.sip2.server.impl.handler;

import cz.kpsys.portaro.commons.barcode.BarCodeValidator;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.object.repo.MoreThanOneItemFoundException;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.StaticParamsModifier;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.UserByContextualIdentifierLoader;
import cz.kpsys.portaro.user.UserConstants;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UserBySip2ContextualIdentifierLoader<USER extends BasicUser> implements UserByContextualIdentifierLoader<USER> {

    @NonNull ParameterizedSearchLoader<MapBackedParams, USER> userSearchLoader;
    @NonNull ContextualProvider<Department, @NonNull BarCodeValidator> validatorProvider;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;

    @Override
    public Optional<? extends USER> getByCtxAndIdentifier(@NonNull Department ctx, @NonNull String identifier) throws MoreThanOneItemFoundException, ItemNotFoundException {
        return findByBarCode(ctx, identifier)
                .or(() -> findByCardNumber(ctx, identifier))
                .or(() -> findByRfId(identifier))
                .or(() -> findByCaseInsensitiveNetId(identifier));
    }

    private Optional<USER> findByBarCode(@NonNull Department ctx, @NonNull String identifier) {
        if (!validatorProvider.getOn(ctx).isValid(identifier)) {
            return Optional.empty();
        }
        return userSearchLoader.getMaxOne(StaticParamsModifier.of(UserConstants.SearchParams.BAR_CODE, identifier));
    }

    private Optional<USER> findByCardNumber(@NonNull Department ctx, @NonNull String identifier) {
        return userSearchLoader.getMaxOne(StaticParamsModifier.of(
                UserConstants.SearchParams.CARD_NUMBER, identifier,
                CoreSearchParams.DEPARTMENT, contextHierarchyLoader.getAllByScope(ctx, HierarchyLoadScope.FAMILY) //card numbers are unique per department
        ));
    }

    private Optional<USER> findByRfId(@NonNull String identifier) {
        return userSearchLoader.getMaxOne(StaticParamsModifier.of(UserConstants.SearchParams.RFID_USER_ID, identifier));
    }

    private Optional<USER> findByCaseInsensitiveNetId(@NonNull String identifier) {
        return userSearchLoader.getMaxOne(StaticParamsModifier.of(UserConstants.SearchParams.CASE_INSENSITIVE_NET_ID, List.of(identifier)));
    }
}

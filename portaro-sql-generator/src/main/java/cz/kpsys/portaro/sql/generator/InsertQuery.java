package cz.kpsys.portaro.sql.generator;

/**
 *
 * <AUTHOR>
 */
public class InsertQuery extends Query {
    
    private Insert insert;
    private Values values;
    


    
    
    public InsertQuery(DbSpecifics dbSpecifics) {
        super(dbSpecifics);
        insert = new Insert(this);
        values = new Values(this);
    }
    
    

    @Override
    public String toString() {
        return "" + insert + " " + values;
    }
    
    
    
    
    public Insert insert() {
        return insert;
    }
    
    public Insert insert(String tableName) {
        insert.table(tableName);
        return insert;
    }
    
    public Values values() {
        return values;
    }
}

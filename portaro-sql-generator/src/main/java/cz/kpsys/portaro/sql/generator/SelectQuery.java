package cz.kpsys.portaro.sql.generator;

import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.search.KeysetPaging;
import cz.kpsys.portaro.search.Paging;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.sorting.Sorting;
import jakarta.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.lang.Nullable;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

import static cz.kpsys.portaro.sql.generator.SelectQueryUtils.joinWithConditions;
import static cz.kpsys.portaro.sql.generator.SelectQueryUtils.selectWhereAndExistsQuery;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SelectQuery extends Query {

    Select select;
    From from;
    JoinList joinList;
    Where where;
    OrderBy orderBy;
    GroupBy groupBy;
    Having having;
    With with;
    @NonFinal @Nullable Integer page;
    @NonFinal @Nullable Integer pageSize;


    public SelectQuery(DbSpecifics dbSpecifics) {
        super(dbSpecifics);
        joinList = new JoinList(this);
        select = new Select(this);
        orderBy = new OrderBy(this);
        from = new From(this);
        where = new Where(this);
        groupBy = new GroupBy(this);
        having = new Having(this);
        with = new With(this);
    }

    public SelectQuery(Query parent, DbSpecifics dbSpecifics) {
        super(dbSpecifics);
        joinList = new JoinList(parent);
        select = new Select(parent);
        orderBy = new OrderBy(parent);
        from = new From(parent);
        where = new Where(parent);
        groupBy = new GroupBy(parent);
        having = new Having(parent);
        with = new With(parent);
    }

    @Override
    public String toString() {
        StringBuilder sbQuery = new StringBuilder();

        sbQuery.append(select);
        if (from.willWrite()) {
            sbQuery.append(' ').append(from);
        }
        if (joinList.willWrite()) {
            sbQuery.append(' ').append(joinList);
        }
        sbQuery.append(' ').append(where);
        if (groupBy.willWrite()) {
            sbQuery.append(' ').append(groupBy);
        }
        if (having.willWrite()) {
            sbQuery.append(' ').append(having);
        }
        if (orderBy.willWrite()) {
            sbQuery.append(' ').append(orderBy);
        }

        String mainQuery;
        if (page != null && pageSize != null) {
            mainQuery = getDbSpecifics().getPaginatedQuery(sbQuery.toString(), page, pageSize);
        } else {
            mainQuery = sbQuery.toString();
        }

        if (with.willWrite()) {
            return with + " " + mainQuery; // can not resolve Firebird DB specifics pagination in other way
        }
        return mainQuery;
    }


    public SelectQuery from(String... items) {
        from.add(items);
        return this;
    }

    public SelectQuery fromUnionAll(List<SelectQuery> selectQueries, String alias) {
        Union union = new Union(getDbSpecifics(), selectQueries, true, alias, true);
        getParamMap().addValues(union.getParamMap().getValues());
        from.add(union);
        return this;
    }

    public SelectQuery fromUnionAll(SelectQuery selectQuery1, SelectQuery selectQuery2, String alias) {
        return fromUnionAll(List.of(selectQuery1, selectQuery2), alias);
    }

    public SelectQuery fromSubquery(SelectQuery subquery, String subqueryName) {
        return from("(" + subquery.getSql(false) + ") " + subqueryName);
    }

    public SelectQuery fromSubquery(String subquery, String subqueryName) {
        return from("(" + subquery + ") " + subqueryName);
    }

    public SelectQuery fromFunction(String name, Object... params) {
        from.add(new Function(this, name, params).toString());
        return this;
    }

    public SelectQuery fromWithRecursive(String withQueryName, SelectQuery anchorQuery, SelectQuery recursiveQuery) {
        with.setRecursive(true);
        with.addWithQuery(new WithQuery(getDbSpecifics(), withQueryName, new UnionAll(getDbSpecifics(), List.of(anchorQuery, recursiveQuery), false)));
        return from(withQueryName);
    }

    public JoinList joins() {
        return joinList;
    }

    public void joinOrExists(QueryFactory queryFactory, String sourceTable, String sourceColumn, String targetTable, String targetColumn, String alias, BiConsumer<Brackets, String> additionalCondition) {
        joinOrExists(queryFactory, sourceTable, sourceColumn, targetTable, targetColumn, alias, true, additionalCondition);
    }

    public void exists(QueryFactory queryFactory, String sourceTable, String sourceColumn, String targetTable, String targetColumn, String alias, BiConsumer<Brackets, String> additionalCondition) {
        joinOrExists(queryFactory, sourceTable, sourceColumn, targetTable, targetColumn, alias, false, additionalCondition);
    }

    public void joinOrExists(QueryFactory queryFactory, String sourceTable, String sourceColumn, String targetTable, String targetColumn, String alias, boolean joinType, BiConsumer<Brackets, String> additionalCondition) {
        if (joinType) {
            joinWithConditions(this, sourceTable, sourceColumn, targetTable, targetColumn, alias, additionalCondition);
        } else {
            selectWhereAndExistsQuery(this, queryFactory, sourceTable, sourceColumn, targetTable, targetColumn, additionalCondition);
        }
    }

    public Where where() {
        return where;
    }

    public Select select() {
        return select;
    }

    public SelectQuery select(String... items) {
        select.add(items);
        return this;
    }

    public SelectQuery selectBlob(BlobHandler blobHandler) {
        blobHandler.addColumnsTo(select);
        return this;
    }

    public SelectQuery selectDistinct(String... items) {
        select.setDistinct(true);
        select.add(items);
        return this;
    }

    public Select selectCount() {
        select.setCount(true);
        return select;
    }

    public Select selectCount(String item) {
        select.add(item);
        return selectCount();
    }

    public Select selectCountDistinct() {
        select.setDistinct(true);
        select.setCount(true);
        return select;
    }

    public Select selectCountDistinct(String... items) {
        select.add(items);
        return selectCountDistinct();
    }

    public OrderBy orderBy() {
        return orderBy;
    }

    public GroupBy groupBy(String... items) {
        groupBy.add(items);
        return groupBy;
    }

    public Having having() {
        return having;
    }

    public void setRange(Range r) {
        if (r.isForAll()) {
            this.page = null;
            this.pageSize = null;
        } else {
            this.page = r.getPageNumber();
            this.pageSize = r.getPageSize();
        }
    }

    public void setPaging(@NonNull Paging paging, @NonNull Sorting sorting) {
        switch (paging) {
            case KeysetPaging keysetPaging -> setKeysetRange(keysetPaging.pageSize(), keysetPaging.lastValues(), sorting);
            case RangePaging rangePaging -> setRange(rangePaging.range());
        }
    }

    public void setKeysetRange(@NonNull Integer pageSize, @Nullable @NotEmpty LinkedHashMap<String, Object> lastValues, @NonNull Sorting sorting) {
        if (lastValues != null) {
            where.and().addKeysetPagingCondition(lastValues, sorting);
        }
        setRange(Range.forFirstPage(pageSize));
    }

    public void setKeysetRange(@NonNull Integer pageSize, @NonNull String keyColumn, @Nullable Object lastValue, @NonNull Sorting sorting) {
        setKeysetRange(pageSize, lastValue == null ? null : new LinkedHashMap<>(Map.of(keyColumn, lastValue)), sorting);
    }

    /**
     * Page size is plus one for ability to resolve if current page is last page
     */
    public void setKeysetRangeLookingBeyondHorizon(@NonNull Integer pageSize, @NonNull String keyColumn, @Nullable Object lastValue, @NonNull Sorting sorting) {
        setKeysetRange(pageSize + 1, keyColumn, lastValue, sorting);
    }

}

package cz.kpsys.portaro.test.e2e.web;

import cz.kpsys.portaro.commons.containers.mail.MailReader;
import cz.kpsys.portaro.test.e2e.TestEnvironmentFactory;
import cz.kpsys.portaro.test.e2e.web.pages.UserDetailPage;
import cz.kpsys.portaro.test.e2e.web.testers.PortaroGuiTester;
import cz.kpsys.portaro.test.e2e.web.testers.PortaroTester;
import cz.kpsys.portaro.test.e2e.web.utils.IniSetting;
import cz.kpsys.portaro.test.e2e.web.utils.Junit5VncRecorder;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.time.Duration;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;

@Testcontainers
@Tag("ci")
@Tag("e2e")
@ExtendWith(Junit5VncRecorder.class)
public class RegistrationTest {

    private PortaroTester portaroTester;
    private PortaroGuiTester portaroGuiTester;

    private static final int REGISTRATION_FEE = 150;
    // After registration user shouldn't have any registration fee
    private static final String EXPECTED_REGISTRATION_FEE_STRING = "0";
    private static final String REGISTRATION_FEE_STRING = String.valueOf(REGISTRATION_FEE);


    @BeforeAll
    public static void startupEnv() {
        TestEnvironmentFactory.startup();
        TestEnvironmentFactory.startPortaroGuiTester();
    }

    @BeforeEach
    public void startupTest() {
        portaroTester = TestEnvironmentFactory.portaroTester();
        portaroGuiTester = TestEnvironmentFactory.portaroGuiTester();

        portaroTester.setupNecessarySettings();
        portaroTester.changeSetting(new IniSetting("OPAC_USER", "FullRegistration", "ANO"));

        portaroTester.getPortaroApiAuthCaller().changeReaderCategoryFee("DO", REGISTRATION_FEE);
        portaroTester.changeSetting(new IniSetting("EMAIL", "SMTP_SENDER_NAME", TestEnvironmentFactory.mailServerSmtpSenderName));
        portaroTester.changeSetting(new IniSetting("EMAIL", "SMTP_SENDER_ADDRESS", TestEnvironmentFactory.mailServerSmtpSenderAddress));
        portaroTester.changeSetting(new IniSetting("EMAIL", "SMTP_SERVER_ADDRESS", TestEnvironmentFactory.mailServerInnerNetworkSmtpHost));
        portaroTester.changeSetting(new IniSetting("EMAIL", "SMTP_SERVER_PORT", TestEnvironmentFactory.mailServerInnerNetworkSmtpPort));
    }

    @AfterEach
    public void cleanUpWithScreenshot(TestInfo testInfo) {
        portaroGuiTester.getScreenshotAndLogout(testInfo);
        portaroTester.cleanup();
    }

    @AfterAll
    public static void cleanupEnv() {
        TestEnvironmentFactory.stopPortaroGuiTester();
    }

    // TODO complete this test by adding registration completion functionality
    @Test
    void shouldRegisterNewUser() {
        portaroGuiTester.getPortaroBrowserCaller().goToHomepage();

        portaroGuiTester.getPages().topMainMenu()
                .clickOnLoginButton();

        portaroGuiTester.getPages().signUpModal()
                .clickOnNewRegistrationButton();

        TestEnvironmentFactory.mailTester().assertReceivedIncomingMessage(Duration.ofSeconds(5), MailReader.registrationSuccessfulMailPredicate(), () -> {
            portaroGuiTester.getPages().registrationModal()
                    .clickOnFullRegistrationButton()
                    .enterEmail("<EMAIL>")
                    .enterNewPasswordRepetition("eAVMhFAO51Bq53A")
                    .enterNewPassword("eAVMhFAO51Bq53A")
                    .enterUserName("testik")
                    .enterPhoneNumber("*********")
                    .enterLastName("Tester")
                    .enterFirstName("Tonda")
                    .clickOnUniversalFormOkButton();

            portaroGuiTester.getPages().finishedResponseModal()
                    .clickOnFinishedResponseCloseButton();
        });

        // After preferences changes, we go to USER account page, where we want to check payment balance of account for registration
        portaroGuiTester.getPages().topMainMenu()
                .clickOnUserAccountSubmenuButton();

        UserDetailPage userDetailPage = portaroGuiTester.getPages().userDetailPage();
        assertThat(userDetailPage.getUserFinanceSumText(), containsString(EXPECTED_REGISTRATION_FEE_STRING));
    }
}



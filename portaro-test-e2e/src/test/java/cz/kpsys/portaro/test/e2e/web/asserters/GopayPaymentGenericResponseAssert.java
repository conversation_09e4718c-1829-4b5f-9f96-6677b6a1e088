package cz.kpsys.portaro.test.e2e.web.asserters;

import io.restassured.response.Response;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import static cz.kpsys.portaro.test.e2e.web.utils.ResponseDataExtractor.extractAsString;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@NonFinal
public class GopayPaymentGenericResponseAssert extends GenericResponseAssert {

    public GopayPaymentGenericResponseAssert(@NonNull Response resp) {
        super(resp);
    }

    public GopayPaymentGenericResponseAssert assertHttpStatusOk() {
        return (GopayPaymentGenericResponseAssert) super.assertHttpStatusOk();
    }

    public GopayPaymentGenericResponseAssert assertHasRedirectUrl() {
        assertNotNull(extractAsString(resp, "redirectUrl"));
        return this;
    }

}

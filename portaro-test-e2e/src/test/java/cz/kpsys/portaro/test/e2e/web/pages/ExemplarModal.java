package cz.kpsys.portaro.test.e2e.web.pages;

import cz.kpsys.portaro.test.e2e.web.utils.PageUtils;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class ExemplarModal {

    final PageUtils pageUtils;

    @FindBy(css = ".loanCategory [role='combobox']")
    WebElement categorySelect;

    @FindBy(css = ".loanCategory [role='listbox'] [role='option']:nth-child(2)")
    WebElement categorySelectOption2;

    @FindBy(css = ".status [role='combobox']")
    WebElement statusSelect;

    @FindBy(css = ".status [role='listbox'] [role='option']")
    WebElement statusOption1;

    @FindBy(css = ".signature input")
    WebElement signatureInput;

    @FindBy(css = ".barCode input")
    WebElement barcodeInput;

    @FindBy(css = ".barCode .error-message")
    WebElement barcodeInputErrorMessage;

    @FindBy(css = ".attachments input")
    WebElement attachmentsInput;

    @FindBy(css = ".identifiers .add")
    WebElement addExemplarButton;

    @FindBy(css = "button[data-qa='universal-form-ok-button']")
    WebElement universalFormOkButton;


    public ExemplarModal(@NonNull WebDriver driver) {
        pageUtils = new PageUtils(driver);
        PageFactory.initElements(driver, this);
    }

    public ExemplarModal clickOnLoanCategorySelect() {
        pageUtils.waitForSteadinessAndClick(categorySelect);
        return this;
    }

    public ExemplarModal clickOnLoanCategoryOption2() {
        categorySelectOption2.click();
        return this;
    }

    public ExemplarModal clickOnStatusSelect() {
        statusSelect.click();
        return this;
    }

    public ExemplarModal clickOnStatusOption1() {
        statusOption1.click();
        return this;
    }

    public ExemplarModal clickOnAddExemplarButton() {
        pageUtils.waitForSteadinessAndClick(addExemplarButton);
        return this;
    }

    public ExemplarModal enterSignature(String signature) {
        pageUtils.waitForSteadinessAndSendKeys(signatureInput, signature);
        return this;
    }

    public ExemplarModal enterBarcode(String barcode) {
        barcodeInput.sendKeys(barcode);
        return this;
    }

    public ExemplarModal waitForAsyncValidation() {
        pageUtils.waitForElementDisappearance(barcodeInputErrorMessage);
        return this;
    }

    public ExemplarModal enterAttachments(String attachment) {
        attachmentsInput.sendKeys(attachment);
        return this;
    }

    public ExemplarModal clickOnUniversalFormOkButton() {
        universalFormOkButton.click();
        return this;
    }
}

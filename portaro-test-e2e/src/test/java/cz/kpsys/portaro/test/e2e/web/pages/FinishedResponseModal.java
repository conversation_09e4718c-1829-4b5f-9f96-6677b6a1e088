package cz.kpsys.portaro.test.e2e.web.pages;

import cz.kpsys.portaro.test.e2e.web.utils.PageUtils;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class FinishedResponseModal {

    final PageUtils pageUtils;

    @FindBy(css = "button[data-qa='finished-response-close-button']")
    WebElement finishedResponseCloseButton;

    @FindBy(css = "[data-qa='finished-response-text']")
    WebElement finishedResponseText;

    public FinishedResponseModal(@NonNull WebDriver driver) {
        pageUtils = new PageUtils(driver);
        PageFactory.initElements(driver, this);
    }

    public String getFinishedResponseText() {
        return finishedResponseText.getText();
    }

    public FinishedResponseModal clickOnFinishedResponseCloseButton() {
        pageUtils.waitForSteadinessAndClick(finishedResponseCloseButton);
        return this;
    }
}

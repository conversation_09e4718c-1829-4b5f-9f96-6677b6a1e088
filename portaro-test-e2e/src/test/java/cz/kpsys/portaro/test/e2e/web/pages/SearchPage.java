package cz.kpsys.portaro.test.e2e.web.pages;

import cz.kpsys.portaro.test.e2e.web.utils.PageUtils;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class SearchPage {

    final PageUtils pageUtils;

    @NonNull
    final By foundDocuments = By.cssSelector(".nazevZaznamu a");

    @FindBy(css = "[data-qa='search-documents-button']")
    WebElement searchButton;

    @FindBy(css = "[data-qa='clear-search-form-button']")
    WebElement clearSearchFormButton;

    @FindBy(css = "#criterion-editor-fond input[value='1']")
    WebElement monographyCheckBox;


    public SearchPage(@NonNull WebDriver driver) {
        pageUtils = new PageUtils(driver);
        PageFactory.initElements(driver, this);
    }

    public SearchPage clickOnSearchButton() {
        pageUtils.waitForElementAndClick(searchButton);
        return this;
    }

    public SearchPage clickOnClearSearchFormButton() {
        pageUtils.waitForElementAndClick(clearSearchFormButton);
        return this;
    }

    public SearchPage checkSearchFormStatus(boolean shouldBeEmpty) {
        pageUtils.checkElementVisibility(clearSearchFormButton, !shouldBeEmpty);
        return this;
    }

    public SearchPage clickOnMonographyFormOptionCheckbox() {
        pageUtils.waitForElementAndClick(monographyCheckBox);
        return this;
    }

    public SearchPage clickOnRecordByDocumentName(String documentName) {
        WebElement recordByDocumentName = pageUtils.findWebElementByContent(foundDocuments, documentName);
        pageUtils.waitForElementAndClick(recordByDocumentName);
        return this;
    }
}

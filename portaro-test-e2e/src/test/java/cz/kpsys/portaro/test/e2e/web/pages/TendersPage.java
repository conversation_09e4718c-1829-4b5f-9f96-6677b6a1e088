package cz.kpsys.portaro.test.e2e.web.pages;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class TendersPage {

    @FindBy(linkText = "Zobrazit řízení")
    WebElement showTenderButton;

    public TendersPage(@NonNull WebDriver driver) {
        PageFactory.initElements(driver, this);
    }

    public TendersPage clickOnShowTenderButton() {
        showTenderButton.click();
        return this;
    }
}

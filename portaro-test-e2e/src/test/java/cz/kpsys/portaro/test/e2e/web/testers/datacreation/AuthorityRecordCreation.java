package cz.kpsys.portaro.test.e2e.web.testers.datacreation;

import cz.kpsys.portaro.commons.util.PersonName;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AuthorityRecordCreation {

    public String createDefaultAuthorityRecord(PersonName name) {
        return """
                <?xml version="1.0" encoding="ISO-8859-1"?>
                <record>
                    <leader>-----nam-a22------i-4500</leader>
                    <controlfield tag="001">jn20000401683</controlfield>
                    <controlfield tag="003">CZ PrNK</controlfield>
                    <controlfield tag="005">20171207104352.2</controlfield>
                    <controlfield tag="008">000425|n|acnnnaabn           n a|a</controlfield>
                    <datafield tag="040" ind1="#" ind2="#">
                      <subfield code="a">ABA001</subfield>
                      <subfield code="b">cze</subfield>
                      <subfield code="d">ABA001</subfield>
                    </datafield>
                    <datafield tag="046" ind1="#" ind2="#">
                      <subfield code="f">1944</subfield>
                    </datafield>
                    <datafield tag="100" ind1="1" ind2="#">
                      <subfield code="a">%s</subfield>
                      <subfield code="d">1944-</subfield>
                      <subfield code="7">jn20000401683</subfield>
                    </datafield>
                    <datafield tag="370" ind1="#" ind2="#">
                      <subfield code="c">Česko</subfield>
                    </datafield>
                    <datafield tag="667" ind1="#" ind2="#">
                      <subfield code="a">Záznam neprošel revizí podle pravidel RDA.</subfield>
                    </datafield>
                    <datafield tag="670" ind1="#" ind2="#">
                      <subfield code="a">Kdo je kdo v ČR na přelomu 20. stol.</subfield>
                    </datafield>
                    <datafield tag="678" ind1="0" ind2="#">
                      <subfield code="a">Narozen 7.12.1944 v Lihomyšli. MUDr., zubní lékař, politik, překladatel z angličtiny.</subfield>
                    </datafield>
                    <datafield tag="906" ind1="#" ind2="#">
                      <subfield code="a">im20000101</subfield>
                    </datafield>
                    <datafield tag="906" ind1="#" ind2="#">
                      <subfield code="a">op20000101</subfield>
                      <subfield code="b">jazi</subfield>
                      <subfield code="c">Biografická poznámka - nově</subfield>
                    </datafield>
                </record>
                """.formatted(name.lastNameCommaFirstName());
    }
}

package cz.kpsys.portaro.test.e2e.web.testers.datacreation;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DomainedDepartmentCreation {

    public Map<String, Object> createDomainedDepartment(@NonNull Integer parentDepartmentId, @NonNull String name, @NonNull String serverUrl, @NonNull Boolean exemplarable, @NonNull String sigla) {
        return Map.ofEntries(
                Map.entry("parentDepartment", parentDepartmentId),
                Map.entry("name", name),
                Map.entry("serverUrl", serverUrl),
                Map.entry("exemplarable", exemplarable),
                Map.entry("sigla", sigla)
        );
    }
}

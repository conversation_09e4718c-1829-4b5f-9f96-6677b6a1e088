package cz.kpsys.portaro.user.discount;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.User;
import jakarta.validation.constraints.NotNull;
import lombok.NonNull;

import java.time.Instant;

public record DiscountApprovedCommand(

        @NotNull
        User user,

        @NonNull
        Department ctx,

        @NonNull
        UserAuthentication currentAuth,

        boolean ztp,

        @NonNull
        Instant ztpValidityEndDate,

        boolean student,

        @NonNull
        Instant studentValidityEndDate,

        boolean teacher,

        @NonNull
        Instant teacherValidityEndDate,

        boolean blind,

        @NonNull
        Instant blindValidityEndDate,

        boolean retiree,

        @NonNull
        Instant retireeValidityEndDate
) {}
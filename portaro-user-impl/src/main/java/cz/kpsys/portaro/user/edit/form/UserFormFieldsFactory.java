package cz.kpsys.portaro.user.edit.form;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.department.CurrentAuthDepartmentsLoader;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.geo.Country;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.formfield.FormField;
import cz.kpsys.portaro.form.formfield.FormFieldList;
import cz.kpsys.portaro.form.validation.RemoteValidation;
import cz.kpsys.portaro.form.valueeditor.LocalizationsAwareValueEditor;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import cz.kpsys.portaro.form.valueeditor.acceptableroot.AcceptableRootValueEditor;
import cz.kpsys.portaro.form.valueeditor.bool.BooleanValueEditor;
import cz.kpsys.portaro.form.valueeditor.list.ListValueEditor;
import cz.kpsys.portaro.form.valueeditor.object.ObjectValueEditor;
import cz.kpsys.portaro.form.valueeditor.object.ObjectValueEditorField;
import cz.kpsys.portaro.form.valueeditor.object.ObjectValueEditorOptions;
import cz.kpsys.portaro.form.valueeditor.singleacceptable.SingleAcceptableValueEditor;
import cz.kpsys.portaro.form.valueeditor.telephonenumber.TelephoneNumberValueEditor;
import cz.kpsys.portaro.form.valueeditor.text.TextValueEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.text.TextValueEditorType;
import cz.kpsys.portaro.security.SecurityAccessor;
import cz.kpsys.portaro.user.*;
import cz.kpsys.portaro.user.contact.ContactManager;
import cz.kpsys.portaro.user.contact.SourceOfData;
import cz.kpsys.portaro.user.contact.Sourceable;
import cz.kpsys.portaro.user.contact.UserEmailEditationCommand;
import cz.kpsys.portaro.user.edit.command.UserAddressEditationCommand;
import cz.kpsys.portaro.user.edit.request.UserEditationRequest;
import cz.kpsys.portaro.user.role.editor.EditorAccount;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class UserFormFieldsFactory implements FormFieldsFactory<User> {

    @NonNull ContextualProvider<Department, ? extends ValueEditor<?, ?, ?>> mandatoryUserPasswordEditorDepartmentedProvider;
    @NonNull ContextualProvider<Department, ? extends ValueEditor<?, ?, ?>> optionalUserPasswordEditorDepartmentedProvider;
    @NonNull UserEditationPropsSetting userEditationPropsSetting;
    @NonNull UserRequiredPropsSetting userRequiredPropsSetting;
    @NonNull SecurityAccessor securityAccessor;
    @NonNull CurrentAuthDepartmentsLoader currentAuthEditableDepartmentsLoader;
    @NonNull List<String> readerUserSelfEditationRequiredProps;
    @NonNull ContactManager contactManager;

    @Override
    public List<FormField<?, ?>> get(@NonNull User user, @NonNull Department department, @NonNull UserAuthentication currentAuth) {
        FormFieldList list = new FormFieldList(UserEditationRequest.OBJECT_NAME);

        List<String> userProps = userEditationPropsSetting.getFields(user, currentAuth, department);

        if (user.hasRole(EditorAccount.class)) {
            Set<String> editorRequiredProps = readerUserSelfEditationRequiredProps.stream()
                    .filter(s -> Set.of(UserEditableFields.USERNAME, UserEditableFields.PASSWORD, UserEditableFields.EMAIL).contains(s))
                    .collect(Collectors.toUnmodifiableSet());

            userProps.addAll(editorRequiredProps);
        }

        List<String> fields = getFields(userProps.stream().distinct().collect(Collectors.toList()));
        Set<String> requiredFields = new HashSet<>(getFields(userRequiredPropsSetting.getRequiredProps(user, currentAuth, department)));

        fields.forEach(field -> {
            boolean required = requiredFields.contains(field);
            ValueEditor editorForField = getEditorForField(field, user, department, currentAuth, required);
            if (editorForField != null) {
                list.add(field, editorForField);
            }
        });

        return list;
    }

    private ValueEditor<?, ?, ?> getEditorForField(@NonNull String field, @NonNull User user, @NonNull Department department, @NonNull UserAuthentication currentAuth, boolean required) {
        Integer editedUserId = user.getId();

        if (field.equals(UserEditationRequest.Fields.username)) {
            return TextValueEditor.getEmptyEditor()
                    .withRequired(required)
                    .withMaxLength(Person.USERNAME_MAX_LENGTH)
                    .withRemoteValidation(
                            RemoteValidation.getEmptyValidation(UserApiConstants.UNIQUE_USERNAME_VALIDATION_URL)
                                    .addStaticParam(Identified.Fields.id, editedUserId)
                    );
        }

        if (field.equals(UserEditationRequest.Fields.newPassword)) {
            return required
                    ? mandatoryUserPasswordEditorDepartmentedProvider.getOn(department)
                    : optionalUserPasswordEditorDepartmentedProvider.getOn(department);
        }

        if (field.equals(UserEditationRequest.Fields.readableDepartments)) {
            return AcceptableRootValueEditor.getEmptyEditor(securityAccessor.getShowedEditingRootHierarchicalDepartment(currentAuth, department))
                    .withEnabledItems(currentAuthEditableDepartmentsLoader.getSubtreesByAuth(currentAuth))
                    .withRequired(true);
        }

        if (field.equals(UserEditationRequest.Fields.editableDepartments)) {
            return AcceptableRootValueEditor.getEmptyEditor(securityAccessor.getShowedEditingRootHierarchicalDepartment(currentAuth, department))
                    .withEnabledItems(currentAuthEditableDepartmentsLoader.getSubtreesByAuth(currentAuth))
                    .withRequired(false);
        }

        if (field.equals(UserEditationRequest.Fields.emails)) {
            var editor = ListValueEditor.<ObjectValueEditor, UserEmailEditationCommand>getEmptyEditor()
                    .withRequired(isRequired(user.getEmails(), required))
                    .withSubEditor(
                            ObjectValueEditor.getEmptyEditor()
                                    .withOptions(
                                            ObjectValueEditorOptions.getEmptyOptions().withFields(List.of(
                                                    new ObjectValueEditorField<>(Texts.ofEmpty(), UserEmailEditationCommand.Fields.value, TextValueEditor.getEmptyEditor()
                                                            .withRequired(true)
                                                            .withTextEditorType(TextValueEditorType.EMAIL)
                                                            .withRemoteValidation(
                                                                    RemoteValidation.getEmptyValidation(UserApiConstants.UNIQUE_EMAIL_VALIDATION_URL)
                                                                            .addStaticParam(Identified.Fields.id, editedUserId)
                                                            )
                                                    )
                                            ))
                                    )
                    )
                    .withSubEditorPrototype(new UserEmailEditationCommand("", SourceOfData.INTERNAL));
            // TODO: localize
            Optional<String> localization = contactManager.getFallbackEmail(user).map(
                    mail -> "Pokud nemáte e-mail, bude se používat: " + mail.value());
            if (localization.isPresent()) {
                editor = editor.addLocalization(LocalizationsAwareValueEditor.CommonLocalizationCodes.DESCRIPTION, Texts.ofNative(localization.get()));
            }
            return editor;
        }

        if (field.equals(UserEditationRequest.Fields.addresses)) {
            return createUserAddressesEditor(user, required);
        }

        if (field.equals(UserEditationRequest.Fields.phoneNumbers)) {
            return createUserPhoneNumbersEditor(user, required);
        }

        return null;
    }

    private ListValueEditor<ObjectValueEditor, UserAddressEditationCommand> createUserAddressesEditor(@NonNull User user, boolean required) {
        ObjectValueEditor addressEditor = ObjectValueEditor.getEmptyEditor()
                .withOptions(
                        ObjectValueEditorOptions.getEmptyOptions().withFields(
                                List.of(
                                        new ObjectValueEditorField<>(Texts.ofMessageCoded("address.edit.street"), UserAddressEditationCommand.Fields.street, TextValueEditor.getEmptyEditor()
                                                .withMaxLength(UserAddressEditationCommand.PROPERTY_STREET_MAX_LENGTH)
                                                .withRequired(true)),
                                        new ObjectValueEditorField<>(Texts.ofMessageCoded("address.edit.city"), UserAddressEditationCommand.Fields.city, TextValueEditor.getEmptyEditor()
                                                .withMaxLength(UserAddressEditationCommand.PROPERTY_CITY_MAX_LENGTH)
                                                .withRequired(true)),
                                        new ObjectValueEditorField<>(Texts.ofMessageCoded("address.edit.postalCode"), UserAddressEditationCommand.Fields.postalCode, TextValueEditor.getEmptyEditor()
                                                .withMaxLength(UserAddressEditationCommand.PROPERTY_POSTAL_CODE_MAX_LENGTH)
                                                .withRequired(true)),
                                        new ObjectValueEditorField<>(Texts.ofMessageCoded("address.edit.country"), UserAddressEditationCommand.Fields.country, SingleAcceptableValueEditor.getEmptyEditor(Country.CODEBOOK.sorted(Country.getTextComparator(CoreConstants.Locales.CS)))
                                                .withRequired(true)),
                                        new ObjectValueEditorField<>(Texts.ofMessageCoded("address.edit.permanent"), UserAddressEditationCommand.Fields.permanent, BooleanValueEditor.getEmptyEditor()),
                                        new ObjectValueEditorField<>(Texts.ofMessageCoded("address.edit.mailing"), UserAddressEditationCommand.Fields.mailing, BooleanValueEditor.getEmptyEditor())
                                )
                        ));

        return ListValueEditor.<ObjectValueEditor, UserAddressEditationCommand>getEmptyEditor()
                .withRequired(isRequired(user.getAddresses(), required))
                .withSubEditor(addressEditor)
                .withSubEditorPrototype(new UserAddressEditationCommand(true, true, SourceOfData.INTERNAL, null, null, null, Country.CZE));
    }

    private ListValueEditor<TelephoneNumberValueEditor, PhoneNumberEditationCommand> createUserPhoneNumbersEditor(@NonNull User user, boolean required) {
        TelephoneNumberValueEditor phoneNumberEditor = TelephoneNumberValueEditor.getEmptyEditor()
                .withRequired(true)
                .withRemoteValidation(RemoteValidation.getEmptyValidation(UserApiConstants.TELEPHONE_NUMBER_VALIDATION_URL));

        return ListValueEditor.<TelephoneNumberValueEditor, PhoneNumberEditationCommand>getEmptyEditor()
                .withRequired(isRequired(user.getPhoneNumbers(), required))
                .withSubEditor(phoneNumberEditor)
                .withSubEditorPrototype(new PhoneNumberEditationCommand("", true, SourceOfData.INTERNAL));
    }

    private List<String> getFields(Collection<String> props) {
        return props.stream()
                .flatMap(requiredProp -> {
                    List<String> fields = UserEditableFields.PROP_TO_USER_EDITATION_REQUEST_FIELDS_MAP.get(requiredProp);
                    if (fields == null) {
                        log.warn("Non defined user editation property (probably in ini) '{}'", requiredProp);
                        return Stream.empty();
                    }
                    return fields.stream();
                })
                .collect(Collectors.toList());
    }

    private boolean isRequired(List<? extends Sourceable> list, boolean required) {
        boolean hasNoExternalEmails = list.stream().filter(contact -> contact.source() != SourceOfData.INTERNAL).toList().isEmpty();
        return required && hasNoExternalEmails;
    }
}

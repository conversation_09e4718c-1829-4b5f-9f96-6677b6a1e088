package cz.kpsys.portaro.user.entity;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.Min;
import lombok.NonNull;

public record LibraryEntity(

        @NonNull
        @Min(1)
        Integer userId,

        @NullableNotBlank
        String sigla,

        @Nullable
        @Min(1)
        Integer departmentId

) {}

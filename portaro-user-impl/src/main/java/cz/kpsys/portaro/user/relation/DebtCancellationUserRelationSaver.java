package cz.kpsys.portaro.user.relation;

import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.edit.DebtCanceler;
import cz.kpsys.portaro.user.edit.command.DebtCancellationForFamilyAssignmentCommand;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
@Slf4j
public class DebtCancellationUserRelationSaver implements Saver<UserRelationsSaveCommand, UserRelationsSaveCommand> {

    @NonNull Saver<UserRelationsSaveCommand, UserRelationsSaveCommand> delegate;
    @NonNull UserRelationsHelper userRelationsHelper;
    @NonNull DebtCanceler debtCanceler;

    @Override
    public @NonNull UserRelationsSaveCommand save(@NonNull UserRelationsSaveCommand command) {

        command = delegate.save(command);
        List<UserRelation> changedRelations = command.userRelationsToUpsert();

        Optional<User> family = userRelationsHelper.getFamilies(changedRelations.stream())
                .findFirst();

        if (family.isPresent()) {
            debtCanceler.cancelDebtForFamilyAssignment(new DebtCancellationForFamilyAssignmentCommand(changedRelations, command, family.get()));
        }

        return command;
    }

}

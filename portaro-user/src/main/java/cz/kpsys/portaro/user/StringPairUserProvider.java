package cz.kpsys.portaro.user;

import java.util.Optional;

public interface StringPairUserProvider<CTX> {

    /**
     * Met<PERSON>, ktera prihlasi uzivatele podle username a hesla.
     * <br/>
     * Pokud se nepovede prihlaseni (spatny credentials apod.), vrati null. <br/>
     * <PERSON><PERSON><PERSON> by vyhazovat BadLoginCredentialsException, ale null (kdyz se tento objekt pouziva v
     * autentikacniSerii, ktera testuje na null)
     */
    Optional<? extends User> get(CTX ctx, String username, String password);
}

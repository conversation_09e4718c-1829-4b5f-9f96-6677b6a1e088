package cz.kpsys.portaro.user.contact;

import cz.kpsys.portaro.commons.object.Identified;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import static cz.kpsys.portaro.databasestructure.UserDb.ADRESY.ID_ADRESY;
import static cz.kpsys.portaro.databasestructure.UserDb.ADRESY.MI;
import static cz.kpsys.portaro.databasestructure.UserDb.ADRESY.PSC;
import static cz.kpsys.portaro.databasestructure.UserDb.ADRESY.STAT;
import static cz.kpsys.portaro.databasestructure.UserDb.ADRESY.TABLE;
import static cz.kpsys.portaro.databasestructure.UserDb.ADRESY.UL;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class AddressEntity implements Identified<Integer> {

    @Column(name = ID_ADRESY)
    @Id
    @EqualsAndHashCode.Include
    @NonNull
    Integer id;

    @Column(name = UL)
    @Nullable
    String street;

    @Column(name = MI)
    @Nullable
    String city;

    @Column(name = PSC)
    @Nullable
    String postalCode;

    @Column(name = STAT)
    @Nullable
    String countryId;
}

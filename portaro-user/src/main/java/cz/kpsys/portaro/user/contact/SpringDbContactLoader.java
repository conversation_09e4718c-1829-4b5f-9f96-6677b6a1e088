package cz.kpsys.portaro.user.contact;

import cz.kpsys.portaro.commons.geo.Country;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cz.kpsys.portaro.commons.db.QueryUtils.TC;
import static cz.kpsys.portaro.databasestructure.UserDb.UZIV_KONTAKTY;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbContactLoader implements ContactLoader, RowMapper<Contact> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull Codebook<ContactType, Integer> contactTypeLoader;
    @NonNull Codebook<SourceOfData, String> contactSourceLoader;


    @Override
    public List<Contact> getAllByUser(int userId) {
        return getAllByUserIdsInternal(List.of(userId));
    }


    @Override
    public Map<Integer, List<Contact>> getAllByUserIds(List<Integer> userIds) {
        Map<Integer, List<Contact>> map = getAllByUserIdsInternal(userIds).stream()
                .collect(Collectors.groupingBy(Contact::userId));
        userIds.forEach(iduserId -> map.computeIfAbsent(iduserId, none -> new ArrayList<>())); //create empty entries to users without any contact
        return map;
    }


    private List<Contact> getAllByUserIdsInternal(List<Integer> userIds) {
        if (userIds.isEmpty()) {
            return new ArrayList<>();
        }

        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(UZIV_KONTAKTY.TABLE);
        sq.where().in(TC(UZIV_KONTAKTY.TABLE, UZIV_KONTAKTY.FK_UZIV), userIds);
        sq.orderBy().addAsc(UZIV_KONTAKTY.PORADI);
        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
    }


    @Override
    public Contact mapRow(ResultSet rs, int rowNum) throws SQLException {
        Integer id = DbUtils.getIntegerNotNull(rs, UZIV_KONTAKTY.ID_UZIV_KONTAKTY);
        Integer userId = DbUtils.getIntegerNotNull(rs, UZIV_KONTAKTY.FK_UZIV);
        ContactType contactType = contactTypeLoader.getById(rs.getInt(UZIV_KONTAKTY.TYP));
        String value = rs.getString(UZIV_KONTAKTY.HODNOTA);
        LocalDate validityEndDate = DbUtils.getLocalDateOrNull(rs, UZIV_KONTAKTY.VALIDITY_END_DATE);
        Country issuingCountry = Country.getByAlpha2OrNull(rs.getString(UZIV_KONTAKTY.ISSUING_COUNTRY));
        String issuer = rs.getString(UZIV_KONTAKTY.ID_CARD_ISSUER);
        LocalDate issueDate = DbUtils.getLocalDateOrNull(rs, UZIV_KONTAKTY.ID_CARD_ISSUE_DATE);
        String paymentAccountCurrency = rs.getString(UZIV_KONTAKTY.PAYMENT_ACCOUNT_CURRENCY_ID);
        SourceOfData source = contactSourceLoader.getById(rs.getString(UZIV_KONTAKTY.ZDROJ));
        return Contact.createExisting(id, userId, contactType, value, source, validityEndDate, issuingCountry, issuer, issueDate, paymentAccountCurrency);
    }
}

package cz.kpsys.portaro.user.role.reader;

import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.sequence.SequenceItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.UserDb.DEF_CTEN_CISLEG;
import static cz.kpsys.portaro.databasestructure.UserDb.DEF_PUJC_CISLEG;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SpringDbCardNumberSequenceItemLoader implements CardNumberSequenceItemLoader, RowMapper<CardNumberSequenceItem> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull ContextualFunction<String, Department, @NonNull Optional<Integer>> userIdByCardNumberLoader;


    @Override
    public Optional<CardNumberSequenceItem> getNext(@NonNull List<Department> acceptableTopDepartments) {
        return acceptableTopDepartments.stream()
                .map(this::getFirstSequenceForTopDepartment)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .findFirst();
    }


    /**
     * Vrati prvni sekvenci ze seznamu vsech pujcoven
     */
    private Optional<CardNumberSequenceItem> getFirstSequenceForTopDepartment(@NonNull Department topDepartment) {
        List<CardNumberSequenceItem> allSequencesOnDepartmentFamily = loadSequencesByDepartment(topDepartment);
        return allSequencesOnDepartmentFamily.stream()
                .map(CardNumberSequenceItem::next)
                .map(next -> getFirstUnusedSequence(next, topDepartment))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .findFirst();
    }


    private List<CardNumberSequenceItem> loadSequencesByDepartment(@NonNull Department department) {
        List<Department> departmentFamily = contextHierarchyLoader.getAllByScope(department, HierarchyLoadScope.FAMILY);
        if (departmentFamily.isEmpty()) {
            return List.of();
        }

        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(WHOLE(DEF_CTEN_CISLEG.TABLE));
        sq.from(DEF_CTEN_CISLEG.TABLE);
        sq.joins().add(DEF_PUJC_CISLEG.TABLE, COLSEQ(TC(DEF_PUJC_CISLEG.TABLE, DEF_PUJC_CISLEG.FK_CISLEG), TC(DEF_CTEN_CISLEG.TABLE, DEF_CTEN_CISLEG.ID_CISLEG)));
        sq.where().in(TC(DEF_PUJC_CISLEG.TABLE, DEF_PUJC_CISLEG.FK_PUJC), ListUtil.getListOfIds(departmentFamily));
        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
    }


    @Override
    public CardNumberSequenceItem mapRow(ResultSet rs, int rowNum) throws SQLException {
        int id = rs.getInt(DEF_CTEN_CISLEG.ID_CISLEG);
        String prefix = rs.getString(DEF_CTEN_CISLEG.PREFIX);
        long lastUsedValue = rs.getLong(DEF_CTEN_CISLEG.POSL_CIS);
        int bodyLength = rs.getInt(DEF_CTEN_CISLEG.POCET_CISLIC);
        return new CardNumberSequenceItem(id, lastUsedValue, prefix, bodyLength, false);
    }


    /**
     * Iterates sequentions and tries to find first unused
     */
    private Optional<CardNumberSequenceItem> getFirstUnusedSequence(CardNumberSequenceItem starting, Department topDepartment) {
        final int MAX_ITERATIONS = 100;

        return SequenceItem.streamWithCurrentFirst(starting)
                .limit(MAX_ITERATIONS)
                .filter(next -> userIdByCardNumberLoader.getOn(next.getValue(), topDepartment).isEmpty())
                .findFirst();
    }

}

package cz.kpsys.portaro.user.contact;

import cz.kpsys.portaro.commons.geo.Country;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@Tag("ci")
@Tag("unit")
class ContactValueComparatorTest {

    private final ContactValueComparator comparator = new ContactValueComparator();

    @Nested
    class BirthNumbers {
        @Test
        void shouldReturnTrueForEqualBirthNumbers() {
            BirthNumber first = new BirthNumber(null, 123456, "99014758467", SourceOfData.DEFAULT_EXTERNAL);
            BirthNumber second = new BirthNumber(null, 123456, "99014758467", SourceOfData.DEFAULT_EXTERNAL);
            assertTrue(comparator.equals(first, second));
        }

        @Test
        void shouldReturnTrueForEqualBirthNumbersAndDifferentIds() {
            BirthNumber first = new BirthNumber(152, 123456, "99014758467", SourceOfData.DEFAULT_EXTERNAL);
            BirthNumber second = new BirthNumber(254, 123456, "99014758467", SourceOfData.DEFAULT_EXTERNAL);
            assertTrue(comparator.equals(first, second));
        }

        @Test
        void shouldReturnFalseForDifferentUserId() {
            BirthNumber first = new BirthNumber(null, 123456, "99014758467", SourceOfData.DEFAULT_EXTERNAL);
            BirthNumber second = new BirthNumber(null, 654321, "99014758467", SourceOfData.DEFAULT_EXTERNAL);
            assertFalse(comparator.equals(first, second));
        }

        @Test
        void shouldReturnFalseForDifferentBirthNumbers() {
            BirthNumber first = new BirthNumber(null, 123456, "99014758467", SourceOfData.DEFAULT_EXTERNAL);
            BirthNumber second = new BirthNumber(null, 123456, "98014758467", SourceOfData.DEFAULT_EXTERNAL);
            assertFalse(comparator.equals(first, second));
        }

        @Test
        void shouldReturnFalseForDifferentSourceOfData() {
            BirthNumber first = new BirthNumber(null, 123456, "99014758467", SourceOfData.DEFAULT_EXTERNAL);
            BirthNumber second = new BirthNumber(null, 123456, "99014758467", SourceOfData.EXTERNAL_BAKALARI);
            assertFalse(comparator.equals(first, second));
        }


        @Test
        void shouldThrowExceptionIfDifferentContactIsUsed() {
            BirthNumber first = new BirthNumber(null, 123456, "99014758467", SourceOfData.DEFAULT_EXTERNAL);
            EmailContact second = new EmailContact(null, 123456, "<EMAIL>", SourceOfData.DEFAULT_EXTERNAL, null);
            assertThrows(IllegalArgumentException.class, () -> comparator.equals(first, second));
        }
    }

    @Nested
    class EmailContacts {
        @Test
        void shouldReturnTrueForEqualEmailContacts() {
            EmailContact first = new EmailContact(null, 123456, "<EMAIL>", SourceOfData.DEFAULT_EXTERNAL, null);
            EmailContact second = new EmailContact(null, 123456, "<EMAIL>", SourceOfData.DEFAULT_EXTERNAL, null);
            assertTrue(comparator.equals(first, second));
        }

        @Test
        void shouldReturnTrueForEqualEmailContactsAndDifferentIds() {
            EmailContact first = new EmailContact(152, 123456, "<EMAIL>", SourceOfData.DEFAULT_EXTERNAL, null);
            EmailContact second = new EmailContact(254, 123456, "<EMAIL>", SourceOfData.DEFAULT_EXTERNAL, null);
            assertTrue(comparator.equals(first, second));
        }

        @Test
        void shouldReturnFalseForDifferentSourceOfDataInEmailContacts() {
            EmailContact first = new EmailContact(null, 123456, "<EMAIL>", SourceOfData.DEFAULT_EXTERNAL, null);
            EmailContact second = new EmailContact(null, 123456, "<EMAIL>", SourceOfData.EXTERNAL_BAKALARI, null);
            assertFalse(comparator.equals(first, second));
        }

        @Test
        void shouldReturnFalseForDifferentUserIdInEmailContacts() {
            EmailContact first = new EmailContact(null, 123456, "<EMAIL>", SourceOfData.DEFAULT_EXTERNAL, null);
            EmailContact second = new EmailContact(null, 654321, "<EMAIL>", SourceOfData.DEFAULT_EXTERNAL, null);
            assertFalse(comparator.equals(first, second));
        }


        @Test
        void shouldReturnFalseForDifferentEmailContacts() {
            EmailContact first = new EmailContact(null, 123456, "<EMAIL>", SourceOfData.DEFAULT_EXTERNAL, null);
            EmailContact second = new EmailContact(null, 123456, "<EMAIL>", SourceOfData.DEFAULT_EXTERNAL, null);
            assertFalse(comparator.equals(first, second));
        }

        @Test
        void shouldThrowExceptionIfDifferentContactIsUsed() {
            EmailContact first = new EmailContact(null, 123456, "<EMAIL>", SourceOfData.DEFAULT_EXTERNAL, null);
            BirthNumber second = new BirthNumber(null, 123456, "99014758467", SourceOfData.DEFAULT_EXTERNAL);
            assertThrows(IllegalArgumentException.class, () -> comparator.equals(first, second));
        }
    }

    @Nested
    class IdCards {
        @Test
        void shouldReturnTrueForEqualIdCards() {
            IdCard first = new IdCard(null, 123456, ContactType.IR_CARD, "*********", SourceOfData.DEFAULT_EXTERNAL, null, Country.CZE, null, null);
            IdCard second = new IdCard(null, 123456, ContactType.IR_CARD, "*********", SourceOfData.DEFAULT_EXTERNAL, null, Country.CZE, null, null);
            assertTrue(comparator.equals(first, second));
        }


        @Test
        void shouldReturnTrueForEqualIdCardsAndDifferentIds() {
            IdCard first = new IdCard(152, 123456, ContactType.IR_CARD, "*********", SourceOfData.DEFAULT_EXTERNAL, null, Country.CZE, null, null);
            IdCard second = new IdCard(254, 123456, ContactType.IR_CARD, "*********", SourceOfData.DEFAULT_EXTERNAL, null, Country.CZE, null, null);
            assertTrue(comparator.equals(first, second));
        }

        @Test
        void shouldReturnFalseForDifferentSourceOfDataInIdCards() {
            IdCard first = new IdCard(null, 123456, ContactType.IR_CARD, "*********", SourceOfData.DEFAULT_EXTERNAL, null, Country.CZE, null, null);
            IdCard second = new IdCard(null, 123456, ContactType.IR_CARD, "*********", SourceOfData.EXTERNAL_BAKALARI, null, Country.CZE, null, null);
            assertFalse(comparator.equals(first, second));
        }

        @Test
        void shouldReturnFalseForDifferentUserIdInIdCards() {
            IdCard first = new IdCard(null, 123456, ContactType.IR_CARD, "*********", SourceOfData.DEFAULT_EXTERNAL, null, Country.CZE, null, null);
            IdCard second = new IdCard(null, 654321, ContactType.IR_CARD, "*********", SourceOfData.DEFAULT_EXTERNAL, null, Country.CZE, null, null);
            assertFalse(comparator.equals(first, second));
        }


        @Test
        void shouldReturnFalseForDifferentIdCards() {
            IdCard first = new IdCard(null, 123456, ContactType.IR_CARD, "*********", SourceOfData.DEFAULT_EXTERNAL, null, Country.CZE, null, null);
            IdCard second = new IdCard(null, 123456, ContactType.IR_CARD, "927548521", SourceOfData.DEFAULT_EXTERNAL, null, Country.CZE, null, null);
            assertFalse(comparator.equals(first, second));
        }

        @Test
        void shouldThrowExceptionIfDifferentContactIsUsed() {
            IdCard first = new IdCard(null, 123456, ContactType.IR_CARD, "*********", SourceOfData.DEFAULT_EXTERNAL, null, Country.CZE, null, null);
            EmailContact second = new EmailContact(null, 123456, "<EMAIL>", SourceOfData.DEFAULT_EXTERNAL, null);
            assertThrows(IllegalArgumentException.class, () -> comparator.equals(first, second));
        }
    }

    @Nested
    class PaymentAccounts {
        @Test
        void shouldReturnTrueForEqualPaymentAccounts() {
            PaymentAccount first = new PaymentAccount(null, 123456, "*********", SourceOfData.DEFAULT_EXTERNAL, "USD");
            PaymentAccount second = new PaymentAccount(null, 123456, "*********", SourceOfData.DEFAULT_EXTERNAL, "USD");
            assertTrue(comparator.equals(first, second));
        }

        @Test
        void shouldReturnTrueForEqualPaymentAccountsAndDifferentIds() {
            PaymentAccount first = new PaymentAccount(152, 123456, "*********", SourceOfData.DEFAULT_EXTERNAL, "USD");
            PaymentAccount second = new PaymentAccount(254, 123456, "*********", SourceOfData.DEFAULT_EXTERNAL, "USD");
            assertTrue(comparator.equals(first, second));
        }

        @Test
        void shouldReturnFalseForDifferentSourceOfDataInPaymentAccounts() {
            PaymentAccount first = new PaymentAccount(null, 123456, "*********", SourceOfData.DEFAULT_EXTERNAL, "USD");
            PaymentAccount second = new PaymentAccount(null, 123456, "*********", SourceOfData.EXTERNAL_BAKALARI, "USD");
            assertFalse(comparator.equals(first, second));
        }

        @Test
        void shouldReturnFalseForDifferentUserIdInPaymentAccounts() {
            PaymentAccount first = new PaymentAccount(null, 123456, "*********", SourceOfData.DEFAULT_EXTERNAL, "USD");
            PaymentAccount second = new PaymentAccount(null, 654321, "*********", SourceOfData.DEFAULT_EXTERNAL, "USD");
            assertFalse(comparator.equals(first, second));
        }

        @Test
        void shouldReturnFalseForDifferentPaymentAccounts() {
            PaymentAccount first = new PaymentAccount(null, 123456, "*********", SourceOfData.DEFAULT_EXTERNAL, "USD");
            PaymentAccount second = new PaymentAccount(null, 654321, "**********", SourceOfData.DEFAULT_EXTERNAL, "USD");
            assertFalse(comparator.equals(first, second));
        }

        @Test
        void shouldThrowExceptionIfDifferentContactIsUsed() {
            PaymentAccount first = new PaymentAccount(null, 123456, "*********", SourceOfData.DEFAULT_EXTERNAL, "USD");
            PhoneContact second = new PhoneContact(null, 123456, ContactType.PHONE, "*********", SourceOfData.DEFAULT_EXTERNAL, null);
            assertThrows(IllegalArgumentException.class, () -> comparator.equals(first, second));
        }
    }

    @Nested
    class PhoneContacts {
        @Test
        void shouldReturnTrueForEqualPhoneContacts() {
            PhoneContact first = new PhoneContact(null, 123456, ContactType.PHONE, "*********", SourceOfData.DEFAULT_EXTERNAL, null);
            PhoneContact second = new PhoneContact(null, 123456, ContactType.PHONE, "*********", SourceOfData.DEFAULT_EXTERNAL, null);
            assertTrue(comparator.equals(first, second));
        }

        @Test
        void shouldReturnTrueForEqualPhoneContactsAndDifferentIds() {
            PhoneContact first = new PhoneContact(152, 123456, ContactType.PHONE, "*********", SourceOfData.DEFAULT_EXTERNAL, null);
            PhoneContact second = new PhoneContact(254, 123456, ContactType.PHONE, "*********", SourceOfData.DEFAULT_EXTERNAL, null);
            assertTrue(comparator.equals(first, second));
        }

        @Test
        void shouldReturnFalseForDifferentSourceOfDataInPhoneContacts() {
            PhoneContact first = new PhoneContact(null, 123456, ContactType.PHONE, "*********", SourceOfData.DEFAULT_EXTERNAL, null);
            PhoneContact second = new PhoneContact(null, 123456, ContactType.PHONE, "*********", SourceOfData.EXTERNAL_BAKALARI, null);
            assertFalse(comparator.equals(first, second));
        }

        @Test
        void shouldReturnFalseForDifferentUserIdInPhoneContacts() {
            PhoneContact first = new PhoneContact(null, 123456, ContactType.PHONE, "*********", SourceOfData.DEFAULT_EXTERNAL, null);
            PhoneContact second = new PhoneContact(null, 654321, ContactType.PHONE, "*********", SourceOfData.DEFAULT_EXTERNAL, null);
            assertFalse(comparator.equals(first, second));
        }

        @Test
        void shouldReturnFalseForDifferentPhoneContacts() {
            PhoneContact first = new PhoneContact(null, 123456, ContactType.PHONE, "*********", SourceOfData.DEFAULT_EXTERNAL, null);
            PhoneContact second = new PhoneContact(null, 123456, ContactType.PHONE, "608988574", SourceOfData.DEFAULT_EXTERNAL, null);
            assertFalse(comparator.equals(first, second));
        }

        @Test
        void shouldThrowExceptionIfDifferentContactIsUsed() {
            PhoneContact first = new PhoneContact(null, 123456, ContactType.PHONE, "*********", SourceOfData.DEFAULT_EXTERNAL, null);
            IdCard second = new IdCard(null, 123456, ContactType.IR_CARD, "*********", SourceOfData.DEFAULT_EXTERNAL, null, Country.CZE, null, null);
            assertThrows(IllegalArgumentException.class, () -> comparator.equals(first, second));
        }
    }
}
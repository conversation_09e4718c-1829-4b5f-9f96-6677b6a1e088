package cz.kpsys.portaro.util.server.info;

import com.fasterxml.jackson.annotation.JsonFilter;
import cz.kpsys.portaro.appserver.properties.AppserverProperties;
import cz.kpsys.portaro.appserver.properties.AppserverPropertiesLoader;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.repo.DataAccessException;
import cz.kpsys.portaro.databaseproperties.DatabaseProperties;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.licence.Licence;
import cz.kpsys.portaro.licence.portaro.PortaroVersion;
import cz.kpsys.portaro.platform.PlatformUtils;
import cz.kpsys.portaro.record.file.cover.CoverSearchManager;
import cz.kpsys.portaro.user.sec.SecurityActions;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SystemUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.AbstractMap;
import java.util.Date;
import java.util.Map;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@RequestMapping("/api/info")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ServerInfoApiController extends GenericApiController {

    @NonNull Date startTime;
    @NonNull AppserverPropertiesLoader appserverPropertiesLoader;
    @NonNull CoverSearchManager coverSearchManager;
    @NonNull PortaroVersion portaroVersion;
    @NonNull Supplier<@NonNull Licence> licenceProvider;
    @NonNull DatabaseProperties databaseProperties;
    @NonNull Map<Department, String> serverUrls;

    @RequestMapping
    public PortaroState getState(@CurrentDepartment Department ctx,
                                 UserAuthentication currentAuth) {
        securityManager.throwIfCannot(SecurityActions.UTIL_SHOW, currentAuth, ctx);

        AppserverProperties appserverProperties = null;
        try {
            appserverProperties = appserverPropertiesLoader.get();
        } catch (DataAccessException e) {
            // ignore
        }

        Licence licence = null;
        try {
            licence = licenceProvider.get();
        } catch (Exception e) {
            log.warn("Cannot get licence", e);
        }

        return new PortaroState(
                startTime,
                appserverProperties,
                databaseProperties,
                coverSearchManager,
                portaroVersion,
                licence,
                getServerUrls());
    }

    private Map<String, String> getServerUrls() {
        return serverUrls.entrySet().stream()
                .map(entry -> new AbstractMap.SimpleEntry<>("%s-%s".formatted(entry.getKey().getId(), entry.getKey().getName()), entry.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    @Slf4j
    @JsonFilter("jsonOnlyForRoleFilter")
    private static class PortaroState {

        @Getter
        @NonNull
        Date startTime;

        AppserverProperties appserverProperties;

        DatabaseProperties databaseProperties;

        CoverSearchManager coverSearchManager;

        PortaroVersion portaroVersion;

        @Getter
        Licence licence;

        @Getter
        Map<String, String> serverUrls;


        public double getRunningDays() {
            long runningMiliseconds = new Date().getTime() - startTime.getTime();
            return runningMiliseconds / (double)86400000;
        }

        public String getVersion() {
            return portaroVersion.getHumanReadableString();
        }

        public String getVersionDetail() {
            return portaroVersion.getHumanReadableDetailString();
        }

        public Object[][] getCoverSearchState() {
            return coverSearchManager.getStavVyhledavaniObalek();
        }

        public long getMaxMemory() {
            return Runtime.getRuntime().maxMemory();
        }

        public long getTotalMemory() {
            return Runtime.getRuntime().totalMemory();
        }

        public long getUsedMemory() {
            return Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        }

        public long getProcesorsCount() {
            return Runtime.getRuntime().availableProcessors();
        }

        public String getOperatingSystem() {
            return SystemUtils.OS_NAME;
        }

        public String getOperatingSystemArchitecture() {
            return SystemUtils.OS_ARCH;
        }

        public String getOperatingSystemVersion() {
            return SystemUtils.OS_VERSION;
        }

        public String getOperatingSystemUsername() {
            return SystemUtils.USER_NAME;
        }

        public String getJavaHome() {
            return SystemUtils.JAVA_HOME;
        }

        public String getJavaVersion() {
            return SystemUtils.JAVA_VERSION;
        }

        public int getProcessId() {
            return PlatformUtils.getPID();
        }





        public String getAppserverUrl() {
            return appserverProperties == null ? null : appserverProperties.url();
        }

        public String getAppserverVersion() {
            return appserverProperties == null ? null : appserverProperties.version();
        }



        public String getDatabaseDriverClassName() {
            return databaseProperties.getDriverClassName();
        }

        public String getDatabaseUrl() {
            return databaseProperties.getUrl();
        }

    }

}

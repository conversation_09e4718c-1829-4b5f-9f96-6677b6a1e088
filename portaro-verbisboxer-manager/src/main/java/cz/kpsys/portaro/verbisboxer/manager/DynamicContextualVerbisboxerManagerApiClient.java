package cz.kpsys.portaro.verbisboxer.manager;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.commons.cache.ScopedCacheDeleter;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.integ.feign.HeaderAddingRequestInterceptor;
import cz.kpsys.portaro.verbisboxer.manager.enablement.StationEnablementSetRequest;
import cz.kpsys.portaro.verbisboxer.manager.layout.StationLayoutResponse;
import cz.kpsys.portaro.verbisboxer.manager.listener.ShipmentInsightResponse;
import cz.kpsys.portaro.verbisboxer.manager.listener.ItemInsightResponse;
import cz.kpsys.portaro.verbisboxer.manager.serviceaccess.ServiceAccessCreationRequest;
import cz.kpsys.portaro.verbisboxer.manager.serviceaccess.ServiceAccessCreationResponse;
import cz.kpsys.portaro.verbisboxer.manager.shipment.*;
import cz.kpsys.portaro.verbisboxer.manager.state.StationStateResponse;
import feign.*;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.slf4j.Slf4jLogger;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.support.SpringMvcContract;

import java.util.List;
import java.util.UUID;
import java.util.function.Consumer;
import java.util.function.Function;

import static cz.kpsys.portaro.commons.web.HttpHeaderConstants.Accept;
import static cz.kpsys.portaro.commons.web.HttpHeaderConstants.ContentType;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class DynamicContextualVerbisboxerManagerApiClient<CTX> implements ContextualVerbisboxerManagerApiClient<CTX> {

    @NonNull Consumer<CTX> enabledAsserter;
    @NonNull ContextualProvider<CTX, @NonNull String> apiUrlProvider;
    @NonNull ContextualFunction<RequestTemplate, CTX, RequestTemplate> authorizationContextualRequestModifier;
    @NonNull ScopedCacheDeleter<CTX> authorizationCacheDeleter;
    @NonNull RequestInterceptor userAgentAddingInterceptor;
    @NonNull ObjectMapper objectMapper;

    @Override
    public StationLayoutResponse getStationLayout(CTX ctx, UUID stationId) {
        return callWithResponse(ctx, client -> client.getStationLayout(stationId));
    }

    @Override
    public StationStateResponse getStationState(CTX ctx, UUID stationId) {
        return callWithResponse(ctx, client -> client.getStationState(stationId));
    }

    @Override
    public void setStationEnablement(CTX ctx, UUID stationId, StationEnablementSetRequest request) {
        callVoid(ctx, client -> client.setStationEnablement(stationId, request));
    }

    @Override
    public ItemInsightResponse getItem(CTX ctx, UUID itemId) {
        return callWithResponse(ctx, client -> client.getItem(itemId));
    }

    @Override
    public void cancelItem(CTX ctx, ShipmentItemCancellationRequest request) {
        callVoid(ctx, client -> client.cancelItem(request));
    }

    @Override
    public void createShipment(CTX ctx, ShipmentCreationRequest request) {
        callVoid(ctx, client -> client.createShipment(request));
    }

    @Override
    public void setItemIdentifiers(CTX ctx, ShipmentItemIdentifiersCreationRequest request) {
        callVoid(ctx, client -> client.setItemIdentifiers(request));
    }

    @Override
    public ServiceAccessCreationResponse createServiceAccess(CTX ctx, ServiceAccessCreationRequest request) {
        return callWithResponse(ctx, client -> client.createServiceAccess(request));
    }

    @Override
    public List<ShipmentInsightResponse> searchShipmentInfo(CTX ctx, ShipmentSearchRequest request) {
        return callWithResponse(ctx, client -> client.searchShipmentInfo(request));
    }

    @Override
    public void changeBoxOfBundle(CTX ctx, ChangeBundleBoxRequest request) {
        callVoid(ctx, client -> client.changeBoxOfBundle(request));
    }

    @Override
    public void moveToNewBundle(CTX ctx, ItemsMoveRequest request) {
        callVoid(ctx, client -> client.moveToNewBundle(request));
    }

    private VerbisboxerManagerApiClient getClientByDepartment(CTX ctx) {
        return Feign.builder()
                .encoder(new JacksonEncoder(objectMapper))
                .decoder(new JacksonDecoder(objectMapper))
                .logger(new Slf4jLogger(VerbisboxerManagerApiClient.class))
                .logLevel(Logger.Level.FULL)
                .requestInterceptor(userAgentAddingInterceptor)
                .requestInterceptor(HeaderAddingRequestInterceptor.ofStaticValue(Accept.NAME, Accept.Value.JSON))
                .requestInterceptor(HeaderAddingRequestInterceptor.ofStaticValue(ContentType.NAME, ContentType.Value.JSON))
                .requestInterceptor(template -> authorizationContextualRequestModifier.getOn(template, ctx))
                .contract(new SpringMvcContract())
                .target(VerbisboxerManagerApiClient.class, apiUrlProvider.getOn(ctx));
    }

    private <RET> RET callWithResponse(CTX ctx, Function<VerbisboxerManagerApiClient, RET> clientConsumer) {
        enabledAsserter.accept(ctx);
        VerbisboxerManagerApiClient client = getClientByDepartment(ctx);
        try {
            return clientConsumer.apply(client);
        } catch (FeignException.Unauthorized unauthorizedEx) {
            log.warn("Unauthorized exception after feign request {}: {}. Clearing authorization cache and retrying", unauthorizedEx.request(), unauthorizedEx.getMessage(), unauthorizedEx);
            authorizationCacheDeleter.clearCacheOn(ctx);
            return clientConsumer.apply(client);
        }
    }

    private void callVoid(CTX ctx, Consumer<VerbisboxerManagerApiClient> clientConsumer) {
        callWithResponse(ctx, verbisboxermanagerapiclient -> {
            clientConsumer.accept(verbisboxermanagerapiclient);
            return Void.TYPE;
        });
    }

}

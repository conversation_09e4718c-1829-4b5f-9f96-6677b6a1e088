package cz.kpsys.portaro.web.page;

import cz.kpsys.portaro.commons.util.StringUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.servlet.View;

import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class TextualView implements View {

    @NonNull MediaType mediaType;
    @NonNull String textContent;

    public static TextualView xml(@NonNull String xml) {
        return new TextualView(MediaType.APPLICATION_XML, xml);
    }

    public static TextualView json(@NonNull String json) {
        return new TextualView(MediaType.APPLICATION_JSON, json);
    }

    public static TextualView html(@NonNull String html) {
        return new TextualView(MediaType.TEXT_HTML, html);
    }

    @Override
    public void render(@NonNull Map<String, ?> model, @NonNull HttpServletRequest request, @NonNull HttpServletResponse response) throws Exception {
        Assert.isTrue(!response.isCommitted(), () -> "Response is already commited, cannot write %s content %s".formatted(mediaType, StringUtil.limitCharsAndTrimWithEllipsis(textContent, 100, true)));
        response.setContentType(mediaType.toString());
        response.getWriter().append(textContent);
    }

    @Override
    public String getContentType() {
        return mediaType.toString();
    }

}
